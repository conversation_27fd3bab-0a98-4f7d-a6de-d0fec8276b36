plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    // END: FlutterFire Configuration
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file("local.properties")
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader("UTF-8") { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty("flutter.versionCode")
if (flutterVersionCode == null) {
    flutterVersionCode = "1"
}

def flutterVersionName = localProperties.getProperty("flutter.versionName")
if (flutterVersionName == null) {
    flutterVersionName = "1.0"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    flavorDimensions "store"
    namespace = "com.appwd.wd"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"


    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = "17"
    }

    productFlavors {
        YL {
            dimension "store"
            applicationId "com.appyl.yl"
            resValue "string", "app_name", "永利娱乐"  // 设置 Release 版本的应用名称
            manifestPlaceholders = [appScheme: "ikztoj",
                                    OPENINSTALL_APPKEY : "ikztoj", // OpenInstall scheme  正式环境：ikztoj
                                    ENGAGELAB_PRIVATES_APPKEY : "689a76e7db852a0043f8cf5e",
                                    ENGAGELAB_PRIVATES_CHANNEL: "developer-default",
                                    ENGAGELAB_PRIVATES_PROCESS: ":remote",
                                    applicationName           : "io.flutter.app.FlutterApplication",
                                    JPUSH_PKGNAME             : applicationId,
                                    JPUSH_APPKEY              : "4fe5e1bc4737d3de76877194", //JPush 上注册的包名对应的 Appkey.
                                    JPUSH_CHANNEL             : "developer-default", //暂时填写默认值即可.
                                    XIAOMI_APPID              : "",//Xiaomi manufacturer, fill in the MI-appid if it exists. If not, don't fill it in. Leave it as ""
                                    XIAOMI_APPKEY             : "",//Xiaomi manufacturer, if available, fill in MI-appkey, if not, don't fill it in, leave it as ""
                                    MEIZU_APPID               : "",//Meizu manufacturer, if available, fill in MZ-appid, if not, do not fill in, leave it as ""
                                    MEIZU_APPKEY              : "",//Meizu manufacturer, if available, fill in MZ-appkey, if not, do not fill in, leave it as ""
                                    OPPO_APPID                : "",//OOPPO manufacturer, if yes, fill in OP-appid, if not, no need to fill in, leave it as ""
                                    OPPO_APPKEY               : "",//OPPO manufacturer, if yes, fill in OP-appkey, if not, no need to fill in, leave it as ""
                                    OPPO_APPSECRET            : "",//OPPO manufacturer, if yes, fill in OP-appsecret, if not, no need to fill in, leave it as ""
                                    VIVO_APPID                : "",//VIVO manufacturer, if available, fill in vivo appid, if not, don't fill in, leave it as ""
                                    VIVO_APPKEY               : "",//VIVO manufacturer, if available, fill in vivo appkey, if not, don't fill in, leave it as ""
                                    HONOR_APPID               : "",//Honor manufacturer, if you have it, fill in honnor appid, if not, don't fill it in, leave it as ""
                                    APP_TCP_SSL               : "",//android - Whether the tcp connection is encrypted, fill in true to indicate encryption, otherwise it means not encrypted, and can be left as "". For this data to take effect, you need to add android:name="com.engagelab.privates.flutter_plugin_engagelab.MTApplication" to the application in AndroidManifest.xml. Or let your MainApplication inherit MTApplication.
                                    APP_DEBUG                 : "",//android - Fill in true to indicate debug mode, others indicate non-debug mode, and can be left as "". For this data to take effect, you need to add android:name="com.engagelab.privates.flutter_plugin_engagelab.MTApplication" to the application in AndroidManifest.xml, or inherit this object.
                                    COUNTRY_CODE              : "",//For testing, can be left as "".For this data to take effect, you need to add android:name="com.engagelab.privates.flutter_plugin_engagelab.MTApplication" to the application in AndroidManifest.xml, or inherit this object.
            ]
        }
        JS {
            flavorDimensions "store"
            applicationId "com.appjs.js"
            resValue "string", "app_name", "金沙娱乐"  // 设置 Release 版本的应用名称
            manifestPlaceholders = [appScheme: "j6m6h0",
                                    OPENINSTALL_APPKEY : "j6m6h0", // OpenInstall scheme  正式环境：j6m6h0
                                    ENGAGELAB_PRIVATES_APPKEY : "689a76e7db852a0043f8cf5e",
                                    ENGAGELAB_PRIVATES_CHANNEL: "developer-default",
                                    ENGAGELAB_PRIVATES_PROCESS: ":remote",
                                    applicationName           : "io.flutter.app.FlutterApplication",
                                    JPUSH_PKGNAME             : applicationId,
                                    JPUSH_APPKEY              : "4fe5e1bc4737d3de76877194", //JPush 上注册的包名对应的 Appkey.
                                    JPUSH_CHANNEL             : "developer-default", //暂时填写默认值即可.
                                    XIAOMI_APPID              : "",//Xiaomi manufacturer, fill in the MI-appid if it exists. If not, don't fill it in. Leave it as ""
                                    XIAOMI_APPKEY             : "",//Xiaomi manufacturer, if available, fill in MI-appkey, if not, don't fill it in, leave it as ""
                                    MEIZU_APPID               : "",//Meizu manufacturer, if available, fill in MZ-appid, if not, do not fill in, leave it as ""
                                    MEIZU_APPKEY              : "",//Meizu manufacturer, if available, fill in MZ-appkey, if not, do not fill in, leave it as ""
                                    OPPO_APPID                : "",//OOPPO manufacturer, if yes, fill in OP-appid, if not, no need to fill in, leave it as ""
                                    OPPO_APPKEY               : "",//OPPO manufacturer, if yes, fill in OP-appkey, if not, no need to fill in, leave it as ""
                                    OPPO_APPSECRET            : "",//OPPO manufacturer, if yes, fill in OP-appsecret, if not, no need to fill in, leave it as ""
                                    VIVO_APPID                : "",//VIVO manufacturer, if available, fill in vivo appid, if not, don't fill in, leave it as ""
                                    VIVO_APPKEY               : "",//VIVO manufacturer, if available, fill in vivo appkey, if not, don't fill in, leave it as ""
                                    HONOR_APPID               : "",//Honor manufacturer, if you have it, fill in honnor appid, if not, don't fill it in, leave it as ""
                                    APP_TCP_SSL               : "",//android - Whether the tcp connection is encrypted, fill in true to indicate encryption, otherwise it means not encrypted, and can be left as "". For this data to take effect, you need to add android:name="com.engagelab.privates.flutter_plugin_engagelab.MTApplication" to the application in AndroidManifest.xml. Or let your MainApplication inherit MTApplication.
                                    APP_DEBUG                 : "",//android - Fill in true to indicate debug mode, others indicate non-debug mode, and can be left as "". For this data to take effect, you need to add android:name="com.engagelab.privates.flutter_plugin_engagelab.MTApplication" to the application in AndroidManifest.xml, or inherit this object.
                                    COUNTRY_CODE              : ""
            ]
        }
        JS2 {
            flavorDimensions "store"
            applicationId "com.appjs.js02"
            resValue "string", "app_name", "金沙娱乐"  // 设置 Release 版本的应用名称
            manifestPlaceholders = [appScheme: "c8ns5n",
                                    OPENINSTALL_APPKEY : "c8ns5n", // OpenInstall scheme  正式环境：c8ns5n
                                    ENGAGELAB_PRIVATES_APPKEY : "74e0523205b5228ca4e834ba",
                                    ENGAGELAB_PRIVATES_CHANNEL: "developer-default",
                                    ENGAGELAB_PRIVATES_PROCESS: ":remote",
                                    applicationName           : "io.flutter.app.FlutterApplication",
                                    JPUSH_PKGNAME             : applicationId,
                                    JPUSH_APPKEY              : "4fe5e1bc4737d3de76877194", //JPush 上注册的包名对应的 Appkey.
                                    JPUSH_CHANNEL             : "developer-default", //暂时填写默认值即可.
                                    XIAOMI_APPID              : "",//Xiaomi manufacturer, fill in the MI-appid if it exists. If not, don't fill it in. Leave it as ""
                                    XIAOMI_APPKEY             : "",//Xiaomi manufacturer, if available, fill in MI-appkey, if not, don't fill it in, leave it as ""
                                    MEIZU_APPID               : "",//Meizu manufacturer, if available, fill in MZ-appid, if not, do not fill in, leave it as ""
                                    MEIZU_APPKEY              : "",//Meizu manufacturer, if available, fill in MZ-appkey, if not, do not fill in, leave it as ""
                                    OPPO_APPID                : "",//OOPPO manufacturer, if yes, fill in OP-appid, if not, no need to fill in, leave it as ""
                                    OPPO_APPKEY               : "",//OPPO manufacturer, if yes, fill in OP-appkey, if not, no need to fill in, leave it as ""
                                    OPPO_APPSECRET            : "",//OPPO manufacturer, if yes, fill in OP-appsecret, if not, no need to fill in, leave it as ""
                                    VIVO_APPID                : "",//VIVO manufacturer, if available, fill in vivo appid, if not, don't fill in, leave it as ""
                                    VIVO_APPKEY               : "",//VIVO manufacturer, if available, fill in vivo appkey, if not, don't fill in, leave it as ""
                                    HONOR_APPID               : "",//Honor manufacturer, if you have it, fill in honnor appid, if not, don't fill it in, leave it as ""
                                    APP_TCP_SSL               : "",//android - Whether the tcp connection is encrypted, fill in true to indicate encryption, otherwise it means not encrypted, and can be left as "". For this data to take effect, you need to add android:name="com.engagelab.privates.flutter_plugin_engagelab.MTApplication" to the application in AndroidManifest.xml. Or let your MainApplication inherit MTApplication.
                                    APP_DEBUG                 : "",//android - Fill in true to indicate debug mode, others indicate non-debug mode, and can be left as "". For this data to take effect, you need to add android:name="com.engagelab.privates.flutter_plugin_engagelab.MTApplication" to the application in AndroidManifest.xml, or inherit this object.
                                    COUNTRY_CODE              : ""
            ]
        }
    }

    //配置渠道对应的安卓资源目录
    sourceSets {
        YL.res.srcDirs 'src/main/res-YL'
        JS.res.srcDirs 'src/main/res-JS'
        JS2.res.srcDirs 'src/main/res-JS2'
    }


    defaultConfig {
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdk = 24
        targetSdk = 34
        versionCode = flutterVersionCode.toInteger()
        versionName = flutterVersionName
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a' // Optional: excludes x86, x86_64 if not needed
        }
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias'] ?: "wd-key-alias"
            keyPassword keystoreProperties['keyPassword'] ?: "123456"
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : file("${System.getenv('HOME')}/wd-keystore.jks")
            storePassword keystoreProperties['storePassword'] ?: "123456"
        }
    }

    buildTypes {
        release {
            applicationIdSuffix ""
//            // 启用代码压缩和优化
//            minifyEnabled true
//            shrinkResources false
            minifyEnabled false // 禁用混淆
            shrinkResources false // 禁用移除未使用资源
            // 使用 ProGuard 规则文件
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.release

        }
        profile {
            initWith release
//            applicationIdSuffix ".profile"
        }
        debug {
//            applicationIdSuffix ".debug"
            // 启用代码压缩和优化
            minifyEnabled false
            // 使用 ProGuard 规则文件
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            // 使用 debug 签名配置
            signingConfig signingConfigs.debug
        }
    }
}

flutter {
    source = "../.."
}
