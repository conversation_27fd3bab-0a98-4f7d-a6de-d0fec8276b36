{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9867b1432ee25a9534ecccec96edfe9a9f", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984cec2ec1f80a7da89a4fdac18efc6203", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989dd90d93e6fadd1c4a020969bffe8372", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ea7be7f0530eb05523950bb1077f4a39", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989dd90d93e6fadd1c4a020969bffe8372", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981f36e7195baa9f7e8f5a409e75d2fa8a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98950d829c9e37e88cc8c57058b8dadeea", "guid": "bfdfe7dc352907fc980b868725387e9861bfbb6a236bfafe99d24647fb0a648c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98fc56ed640800de61cfb79a991736621c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988aff4ead3c0a91d9f8a184d3b09ee588", "guid": "bfdfe7dc352907fc980b868725387e98bb4fdcc38b0c6de8a84737aad1b2ea91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98556db2d77e917ce027f22132f6bb8295", "guid": "bfdfe7dc352907fc980b868725387e98bd404fab034d291ec40154ea90aef255"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98778dd959d694d7e0a0d9062e0a80dac0", "guid": "bfdfe7dc352907fc980b868725387e9855cf4237feae7d230b5b9f5835b27865"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987180fe2a646f093c65c05c5977b45224", "guid": "bfdfe7dc352907fc980b868725387e98fe96ca541e805510716f76e39959b092"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855369e467c4e87a07d9400765c5170ca", "guid": "bfdfe7dc352907fc980b868725387e98c77eeb5a39dcdd516cf5a5e59147a1e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f923395166287047327292d35bfc8794", "guid": "bfdfe7dc352907fc980b868725387e98dc10d91995175f85a161b906f4df6c75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b63b3672db9f151faacda180a0793f19", "guid": "bfdfe7dc352907fc980b868725387e98024b4b959ad8c3b801cf226d53d4cfcd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c9a77d632098a4f1e06e431f18af957", "guid": "bfdfe7dc352907fc980b868725387e9803adff81be901d1ae24761baa2eb7059"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98343e24d377c7fcf6e1c5dc8f24b5b2cc", "guid": "bfdfe7dc352907fc980b868725387e9800782d6d178d0f4ef6a7599725678f9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c94c89d77245eb70b4dc28d46c6c5d0c", "guid": "bfdfe7dc352907fc980b868725387e984e5f8239733b87e173492ead11e18d2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c9daf7501313242ed75d0ddf32635ce", "guid": "bfdfe7dc352907fc980b868725387e989a0dcecfad2408dcb45a15a20942c7b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f7af3258f3e7d52aca4d3031254cb5a", "guid": "bfdfe7dc352907fc980b868725387e98dcb29b32065be49fe2d45e7c1a384350"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aeb8e8771bfe1159944ee5f525382c1b", "guid": "bfdfe7dc352907fc980b868725387e98b3be41c4d4cb57acd97dcbf9056a2034"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b51056c9b1e2aa28f459dbe747683d34", "guid": "bfdfe7dc352907fc980b868725387e98c44ed1c7ef2e335edf03a8669e500384"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b559a76c7c2d738c4a4da060ffbab1cc", "guid": "bfdfe7dc352907fc980b868725387e980b6bf23598179d1b460c191ca0bdfd2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bde512b51488ac75f17286982d9ce284", "guid": "bfdfe7dc352907fc980b868725387e98987c9a52fd9f1cc6753bcb32a03fd2ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859ac098928d5241ee151648cac652ff8", "guid": "bfdfe7dc352907fc980b868725387e9805ca1257218fc06bf362dfc8d328f733"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841142bddc6581063080393d715bdd39c", "guid": "bfdfe7dc352907fc980b868725387e98a242a252d282ca671d43e04129c0b161"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988edf3f229c57e0fda757f955cbdb4e71", "guid": "bfdfe7dc352907fc980b868725387e986ebeab8c8272f8dbe0e8c1a26eb6361f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835e7eae969945769c1636f0aec28dcf8", "guid": "bfdfe7dc352907fc980b868725387e9894ada70d33f11d628f5aa19102cc4e4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebb99b50228ff8dafa3f2c3fbdc0d742", "guid": "bfdfe7dc352907fc980b868725387e98612d965d7a7fce750c679b8ccd3786dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985aa945aa0af1e3deaa16f7466d8dccbc", "guid": "bfdfe7dc352907fc980b868725387e98b8df2e2d67946993fb61209d54b1649c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ada97094224ebc5a715819108ea165f", "guid": "bfdfe7dc352907fc980b868725387e98a82bf5a58c3069f0b7cca6767ba71341"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884be3095343fd9aa7632bf06618f2410", "guid": "bfdfe7dc352907fc980b868725387e986b453f530cac7b3ebdcc574b6c60fdbc"}], "guid": "bfdfe7dc352907fc980b868725387e98212737d6db54fb0517c3224029e3afb5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9869664cd59b56171ce6bd4a54b67071c4", "guid": "bfdfe7dc352907fc980b868725387e98038249339b7dff72c7c598ebba6b91b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984264deed309e6d519543a22165ea948e", "guid": "bfdfe7dc352907fc980b868725387e98cb5ca1666ae9dae6138b4306c79a51ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e8189122d5421d1589ca17eda172100", "guid": "bfdfe7dc352907fc980b868725387e986e39e0bbfbf7b24fece993779bb585ab"}], "guid": "bfdfe7dc352907fc980b868725387e98130c36ab2480349ba5ff0262c3b45df3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e6fff4f6cfc8d12ebc57c4bf8981d65d", "targetReference": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3"}], "guid": "bfdfe7dc352907fc980b868725387e98b4b5611bdd6ff9f5661a6dca82b1072e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController"}, {"guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery"}], "guid": "bfdfe7dc352907fc980b868725387e985fd5cdb9993b1816141f0c012ffa62bd", "name": "DKImagePickerController", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f752ba05adf197c3696519e901961310", "name": "DKImagePickerController.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}