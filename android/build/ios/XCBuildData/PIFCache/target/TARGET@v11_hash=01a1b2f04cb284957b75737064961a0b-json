{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984d3d218fea5d9833baeb5c846f1ceb74", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98124d05d95b0a88b50ba23971f882cc63", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f1c22793e92911c759a6c00c6546e648", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985e60d334a0636be678d24ed155f17e68", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f1c22793e92911c759a6c00c6546e648", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9850968fc64e545db8702a9275a14bb5b9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980c349974fb66bd1756ca19f7466f2f56", "guid": "bfdfe7dc352907fc980b868725387e985e35902722fa51253ddf7f2db938da97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dbaa9c0e7282ad4aef5fa516e854f6f", "guid": "bfdfe7dc352907fc980b868725387e988cb2966c93cbeaa485df350548addf94", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889156f16b1813055ed4b7e834c5c7996", "guid": "bfdfe7dc352907fc980b868725387e98bc6255dd2ede8637ea436c655857d417", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831bf042cb62cd5969cb5cb1a0314f1c2", "guid": "bfdfe7dc352907fc980b868725387e982ec39c66c4aa7512a695ebe85813937e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98772f700b5b79f6bb0fd3c94aeb2165d4", "guid": "bfdfe7dc352907fc980b868725387e98faedbd6fc02bd573fc4230ab1435ae91", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c8b78bec48218225b813e3f202e2d15", "guid": "bfdfe7dc352907fc980b868725387e98a8d3bc5565660649c9f8f2cb8bc30948", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1054b6e005d217fc2f6fd0e76ae8597", "guid": "bfdfe7dc352907fc980b868725387e9896cfcc0a7c556c19046865d9dc5e4e54", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba78decafe04223fc846ce4af01862d7", "guid": "bfdfe7dc352907fc980b868725387e98fac09f64dcbf1d8597740f72046713e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3ba4c55549e45e190547ec43666f132", "guid": "bfdfe7dc352907fc980b868725387e98846d3e90e649ad89c98bbda7c034e256", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f832a64768f58f0793c62e1b5ce8ebe7", "guid": "bfdfe7dc352907fc980b868725387e9857bcd38a911a3e8edddb8accb04857b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898fc11fb0cd9de9982d2629b72b70dc3", "guid": "bfdfe7dc352907fc980b868725387e98a09ad8356b296de5f88705b3f80bfd2e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf363a87c48172fb2df72b0cce28cc72", "guid": "bfdfe7dc352907fc980b868725387e98bb0f3c7b1aeac7c33c4d947e2132e6b6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9851198fae0d8905fb79f391da70d27b9a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986706cd5013d717fb60b03c37dbdd3a42", "guid": "bfdfe7dc352907fc980b868725387e9806ca13c36fada04737fab64394e4a672"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987457fcb0289a7521293fdf645c66d43b", "guid": "bfdfe7dc352907fc980b868725387e984b9b1fc755ebd228d95922f0b77fdc0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98337c254ad78de164a4eb5f9e687c6762", "guid": "bfdfe7dc352907fc980b868725387e98b5274c5245c758169010756d95f11cf7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98961c1b1dbe2ba7c9fb2c80338de66644", "guid": "bfdfe7dc352907fc980b868725387e986155eb0b18e160090f1c3de162b97ac8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f32515d559be0138df830c652100d613", "guid": "bfdfe7dc352907fc980b868725387e98d1cd60dab61b8f6a5d56eb5416544dff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc5c4d4271860d2de771825eddf789e7", "guid": "bfdfe7dc352907fc980b868725387e98dc14ef74b9ab13b7d86e26925f1dda77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847fe1cf289cd775b2383b818c0784663", "guid": "bfdfe7dc352907fc980b868725387e98e2c851e8f0e990d544a2e9ef5aeaaa13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841813f784d10a570712399d86f016670", "guid": "bfdfe7dc352907fc980b868725387e98e9375e23c6c47ac8f5ae87fd56b17b52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a796120bb0e76609fcfdd823d7d8ca54", "guid": "bfdfe7dc352907fc980b868725387e98e8cb6ce462391dd39bfa30e1070fa3b6"}], "guid": "bfdfe7dc352907fc980b868725387e98c53cda76c6714cc99d33f8d438858f07", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9869664cd59b56171ce6bd4a54b67071c4", "guid": "bfdfe7dc352907fc980b868725387e98cdd7430e01d92244c5d5c3d4bd77581d"}], "guid": "bfdfe7dc352907fc980b868725387e98e25737a103f495bf6b667e8f6f32d4bd", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9804c93d896fd7e7654fa5c52570948a90", "targetReference": "bfdfe7dc352907fc980b868725387e9894b6f514aa32ee4cfdd7fc11c1ff5321"}], "guid": "bfdfe7dc352907fc980b868725387e9868af60886bbf96f1bb9951e1a108aa20", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9894b6f514aa32ee4cfdd7fc11c1ff5321", "name": "sqflite-sqflite_darwin_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e983786431ce548989b846bbf1a7384f58e", "name": "sqflite", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9892137925c03f59a4fb600ced1a959f92", "name": "sqflite.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}