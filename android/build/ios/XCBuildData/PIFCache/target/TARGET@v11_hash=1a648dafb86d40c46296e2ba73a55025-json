{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988737c5809b13bc1d7748af0cdfb9529a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/PINOperation/PINOperation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/PINOperation/PINOperation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PINOperation/PINOperation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "PINOperation", "PRODUCT_NAME": "PINOperation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ceaec28f42d451ad5c7ca08f67483b5e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e5acdaecc04966c91de8493461245e0d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/PINOperation/PINOperation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/PINOperation/PINOperation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PINOperation/PINOperation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "PINOperation", "PRODUCT_NAME": "PINOperation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98669afba62ca93bc99c4809c5334df154", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e5acdaecc04966c91de8493461245e0d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/PINOperation/PINOperation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/PINOperation/PINOperation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PINOperation/PINOperation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "PINOperation", "PRODUCT_NAME": "PINOperation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98965fd39054509975de81f12bd35b7684", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988a9a649f13807b9daf82df39ccf6ab4e", "guid": "bfdfe7dc352907fc980b868725387e98dde34b60177f4c4293b84e1ec2c2d5e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884519a76e622367df78c1836f8f3c9ea", "guid": "bfdfe7dc352907fc980b868725387e98acbccf49f5c5dbfe8d366f9cec362206", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816c24e4e37bed718dc158c423e17e7a1", "guid": "bfdfe7dc352907fc980b868725387e9836d3f1b0da50e090440ed818848369ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98276959b2d0d96f181ed36a770d611951", "guid": "bfdfe7dc352907fc980b868725387e98e00670cc57e9ba61764908a9f83f3f6d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b66cc0ce78cc39dff7b7e407e1f9e5bc", "guid": "bfdfe7dc352907fc980b868725387e98805b0a60fcb17d803fcde011bb7d514c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2fc889407a9e6aad766379722264fb7", "guid": "bfdfe7dc352907fc980b868725387e9883a14fc463d0e689d79f828b9a83ef4a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98db2d2a19769cdf1d868a7815f9c32ba2", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98733e8d8f12e8a068ba6fc061b729816a", "guid": "bfdfe7dc352907fc980b868725387e98d866d62c6b98ff563002a0c400315bf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9864178865fce1fb0a23e0b5f0fd5f9", "guid": "bfdfe7dc352907fc980b868725387e98c29caf7d2083470df2ff3dd4ea05bb43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf51b25eedc4c71ab25d86d3bed6c122", "guid": "bfdfe7dc352907fc980b868725387e98351a4ac26738e1102f5cc5180891ff36"}], "guid": "bfdfe7dc352907fc980b868725387e98dd205b8553c1d5e7a99059922493b91b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9869664cd59b56171ce6bd4a54b67071c4", "guid": "bfdfe7dc352907fc980b868725387e98d9c368f252020afee9e3324392f1419a"}], "guid": "bfdfe7dc352907fc980b868725387e98a60ca2a54e2f9fb192cfe45fbfa47c9f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98accd71e768c9cb23fc104957d85d4fe9", "targetReference": "bfdfe7dc352907fc980b868725387e9872a1d0c9b4d65372f854ad7ceb04716f"}], "guid": "bfdfe7dc352907fc980b868725387e989f5debacca8b4c27828f2cc73e96e414", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9872a1d0c9b4d65372f854ad7ceb04716f", "name": "PINOperation-PINOperation"}], "guid": "bfdfe7dc352907fc980b868725387e98d09cfc976f4622a7180a4de1a3b0a290", "name": "PINOperation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ccd5063b353607b0bc283d743114ffaf", "name": "PINOperation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}