{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c8cb7ed323fb84ea6b65c717f2968117", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/better_player_plus/better_player_plus-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/better_player_plus/better_player_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/better_player_plus/better_player_plus.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "better_player_plus", "PRODUCT_NAME": "better_player_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e0478b0e36e600007af7aebef02ff119", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d0059715c8138f240f902bff3b961287", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/better_player_plus/better_player_plus-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/better_player_plus/better_player_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/better_player_plus/better_player_plus.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "better_player_plus", "PRODUCT_NAME": "better_player_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9877475cc60ba18310df7fbd40b9e4a1a5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d0059715c8138f240f902bff3b961287", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/better_player_plus/better_player_plus-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/better_player_plus/better_player_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/better_player_plus/better_player_plus.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "better_player_plus", "PRODUCT_NAME": "better_player_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ef81cd99698455e5f740992b44345ebb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98864425ad4ad7041fddff0ee96e8da6ad", "guid": "bfdfe7dc352907fc980b868725387e9879fa8fe4e966d566c39efbd76610588e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cddf1ffadd6c538d81a88cfb82957ce9", "guid": "bfdfe7dc352907fc980b868725387e9805d282b1b9b7d757225e0718f760c4c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce86ba3393c5eb866cb2061da034f185", "guid": "bfdfe7dc352907fc980b868725387e98b2cc77b7ae4c7c8290e9ba8b6ff94ee6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a57a6973aca37b588271d51143a218a", "guid": "bfdfe7dc352907fc980b868725387e9838370f14cd88758160a6f94c75ecf2f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98244e7c8e5a26543c32a06d7d8ce37435", "guid": "bfdfe7dc352907fc980b868725387e98e056a21f1e77c0c46f443b2ef1d0fecb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984157ca9c754511bae360c4fbfe06cd96", "guid": "bfdfe7dc352907fc980b868725387e986692806144305f0a534f84398d79b4de", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e984912eb5d04206a4e352cd66884f42b4d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e7536da472ce1911770bdacf136e6ca4", "guid": "bfdfe7dc352907fc980b868725387e98e76d592610126047f73cd62fcfbd23a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d16e17cef7c7168c5a05ff15d12f126a", "guid": "bfdfe7dc352907fc980b868725387e9876e92ef8a6529344e32e578f967ccc9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f06f9ce5d957fadb6538a1c73f5f6253", "guid": "bfdfe7dc352907fc980b868725387e9864cc4c05cfa9c92dd273c5f67dc2865f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef98cee73147016ec80843e1f1a2304c", "guid": "bfdfe7dc352907fc980b868725387e98a6f9bf70d969345189ae760640f68ee7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98547bed87d6b8ad502df0a699cdb89d3c", "guid": "bfdfe7dc352907fc980b868725387e98257418024d90173a5a7c50fbc45ae405"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be64843b76039646a0fae0333d6bd33d", "guid": "bfdfe7dc352907fc980b868725387e9820cd3390762cca4824f1e06e4b0853a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885a0aa01417bb6964ad14df7c7cc4ac2", "guid": "bfdfe7dc352907fc980b868725387e98a51aeb97184283e2003500f46425a548"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806c12781c81f4c6c7704e9698bcd3353", "guid": "bfdfe7dc352907fc980b868725387e986fae8aaba8108f4fc404a8a3e0f5eb70"}], "guid": "bfdfe7dc352907fc980b868725387e98ae467dac73b1f0925174c6ea1e67bc69", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9869664cd59b56171ce6bd4a54b67071c4", "guid": "bfdfe7dc352907fc980b868725387e980fcba21f54f881b4fc0e528bc7dad9d2"}], "guid": "bfdfe7dc352907fc980b868725387e98dc9dc28bb516693c5d8e59cff4621e50", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fed75713ba2d2198e7842f18455d94ac", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98687f14f834c716a0a6b72b2f678d5d39", "name": "<PERSON><PERSON>"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98ae4cba9dafde33c1f8a32ad5773d6cd3", "name": "GCDWebServer"}, {"guid": "bfdfe7dc352907fc980b868725387e989f6c9e975471ecf29d91b182619839be", "name": "HLSCachingReverseProxyServer"}, {"guid": "bfdfe7dc352907fc980b868725387e980a765b211b0c8c508dddcb830a52bbab", "name": "PINCache"}], "guid": "bfdfe7dc352907fc980b868725387e986cd267420621a3ff1d813b97fdcfc70c", "name": "better_player_plus", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982979aa52633ff059b6ad20ce50d04f84", "name": "better_player_plus.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}