{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9854998250c4d6f27893bfac0796f2e848", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/Cache/Cache-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/Cache/Cache-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Cache/Cache.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "<PERSON><PERSON>", "PRODUCT_NAME": "<PERSON><PERSON>", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98efa64b4f32883aa2693280737608548d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98298d10736d9de40cc9bdb029fd6f877d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/Cache/Cache-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/Cache/Cache-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Cache/Cache.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "<PERSON><PERSON>", "PRODUCT_NAME": "<PERSON><PERSON>", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98989b2338715926b834aa535b673e6aaf", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98298d10736d9de40cc9bdb029fd6f877d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/Cache/Cache-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/Cache/Cache-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Cache/Cache.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "<PERSON><PERSON>", "PRODUCT_NAME": "<PERSON><PERSON>", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fd5000d068fbe011714f8562c0d9d0c8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984c9ce2e5ab005f5c1dd67bca7e84fe47", "guid": "bfdfe7dc352907fc980b868725387e987fb6b6abfc38575efb1c6772f650c282", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982e61f9f163cd58f1677ced56115aff38", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982e2dc282b613ea273f690a92e7ac88e1", "guid": "bfdfe7dc352907fc980b868725387e982a8b73e309da47ae27c86fff9c316839"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf07aaedc9d9e421b8ad9abb3f0f4d79", "guid": "bfdfe7dc352907fc980b868725387e98d034807ddf5941e2a21a95c4bbf34f27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be0592cec59a834d55f17a55cd9a2da6", "guid": "bfdfe7dc352907fc980b868725387e98cb2d0ce7cb0bc506d49f6dae59c1ec1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98538b379d34aaab5cdac4fdc2ec1875a5", "guid": "bfdfe7dc352907fc980b868725387e98f962c709a2d6e435a8fde00f7c16a9f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c911a170c2851897572726bb805b682", "guid": "bfdfe7dc352907fc980b868725387e98f8b871c66fec42ec2fd5c86b8b49e70a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830cbecf5f00c224c9de9d610463a1da2", "guid": "bfdfe7dc352907fc980b868725387e987e5ac713696e1f6bc8f93851b2a4df6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988304805018b42c23e04cf642f00a1f6d", "guid": "bfdfe7dc352907fc980b868725387e9810320f13c2b0fba8c6dface85e14e2e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baad4e6d9726b58097309973238b0138", "guid": "bfdfe7dc352907fc980b868725387e98522ff25381551d9a41e3d947051c2817"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f32883d6448fd6819153e26235a2806", "guid": "bfdfe7dc352907fc980b868725387e98258ffbb7bfd4b12a32629a383abe8b3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd626e91bc158bce02db72f693fb688d", "guid": "bfdfe7dc352907fc980b868725387e98da97e89920b77ee32be08491da265bc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee502806226aa3a6b161341a5d531681", "guid": "bfdfe7dc352907fc980b868725387e98874f3a2c8f0b589f72dd44db80d6adaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805b11f1f7bdc88aec9ae402c86d88901", "guid": "bfdfe7dc352907fc980b868725387e9880a69ececabc04a149137f4e24369e04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98122759912bcd33301c60e046cbd09d58", "guid": "bfdfe7dc352907fc980b868725387e984d4b55f323dccb924f8cd07e8dd75505"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a475edcdb0be478a83d57a2e157fb850", "guid": "bfdfe7dc352907fc980b868725387e98d9b9b30798aefc93053fce0daaa90d2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efb748b09315b68db1228ba67a27184a", "guid": "bfdfe7dc352907fc980b868725387e9848cd670fdc120c31daf2194e08dbf17a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e3aaf630372a21a90d417536939e017", "guid": "bfdfe7dc352907fc980b868725387e98d3aa6537895fd5c153d2a841f659c4b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aca1f639b59099cb40724152b982f22b", "guid": "bfdfe7dc352907fc980b868725387e98319257db86358d2866b47c15cc6ba3e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b36609af5b66ab0d937996bb2369f016", "guid": "bfdfe7dc352907fc980b868725387e98b1852d131ca5c1a74ce08b0b1778df0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5ffad953bbaffcdadff1c1b0c9f57f0", "guid": "bfdfe7dc352907fc980b868725387e980645104c3ee42c4426987406426d1669"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840b46bf2d7c4c6740c131b13a1b3ba60", "guid": "bfdfe7dc352907fc980b868725387e98e702c9716de20ae323a540ebb9c4ba4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859098fb568f5234abb204061c06874bd", "guid": "bfdfe7dc352907fc980b868725387e98448d3d5e56cd4e3f4edbd3d3820d8c51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981eaa06fa88027347a4eb1c44ad92daa8", "guid": "bfdfe7dc352907fc980b868725387e9872d39828f9bf859013ea541be1369687"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d5c3987045a99c4c417d75da1021f01", "guid": "bfdfe7dc352907fc980b868725387e98b779f1b9aadaf6e85b041029c4c5b38d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857f3ed9dcbdca3093e8506fbc42b02d0", "guid": "bfdfe7dc352907fc980b868725387e98d8ab7263d3294d006ae6efe7db47803b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ee10cf1f827fb677822d4fc0bc57311", "guid": "bfdfe7dc352907fc980b868725387e9839e77cdb6912504c4cd3ec6801d8df85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981df9c72816020aa6907a6318c470f943", "guid": "bfdfe7dc352907fc980b868725387e9817f5b9422009b971412d39186884644f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98334ca753194b51d2bbde714f7babe5d8", "guid": "bfdfe7dc352907fc980b868725387e98cf55692f6a3b4f1fa225bf656989e61f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ebd4f3dba03652e180059b2e427281d", "guid": "bfdfe7dc352907fc980b868725387e989f152c9af72bda8ff84bef9bdb202d08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cae469493ec8ebbb95614d58bd503e98", "guid": "bfdfe7dc352907fc980b868725387e987cc566f4d78c816b6bfcefb993c6193c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f223879cce582c06d41fbe0e68b23cab", "guid": "bfdfe7dc352907fc980b868725387e985223b3b6c7bc0df863c1f047a29fea90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c43ba1c604b9d022a783099c393d9a19", "guid": "bfdfe7dc352907fc980b868725387e98da904e6f6e9884b90dd19fffbb55eecd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98608f699caef58d6605c73b4c41cac0f5", "guid": "bfdfe7dc352907fc980b868725387e9897cca6d4b05dc33ddcbcc2c967d26486"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825109b1e4a6132ea9b3ba97f16452e98", "guid": "bfdfe7dc352907fc980b868725387e98d579eab40bed8c9b412e6c2097dcd74e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983211d309e9b553ba1f1074147f9d74d5", "guid": "bfdfe7dc352907fc980b868725387e983905710fe0d1449bd5ac2e9232a353ab"}], "guid": "bfdfe7dc352907fc980b868725387e984103295213b48517178bdc70f4164c2d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9869664cd59b56171ce6bd4a54b67071c4", "guid": "bfdfe7dc352907fc980b868725387e981481f16b8dc0593edacb851a7510189a"}], "guid": "bfdfe7dc352907fc980b868725387e98a50ec29d5e618f099bfbdf56290aed64", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98d97aa38e597a004a05f8e33864fff274", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98687f14f834c716a0a6b72b2f678d5d39", "name": "<PERSON><PERSON>", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98fdbf8692076b1f163449c92d81d4a201", "name": "Cache.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}