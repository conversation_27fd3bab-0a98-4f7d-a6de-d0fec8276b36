{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d199af765e7943502c1a2e677123089d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/file_picker/file_picker-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/file_picker/file_picker-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/file_picker/file_picker.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "file_picker", "PRODUCT_NAME": "file_picker", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98146aa5eb22cae0070d94203a6014cb48", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986f76eea1f6ac2802c607e48f5d1276e6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/file_picker/file_picker-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/file_picker/file_picker-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/file_picker/file_picker.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "file_picker", "PRODUCT_NAME": "file_picker", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dd83bad799274b5983694d0fc9456356", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986f76eea1f6ac2802c607e48f5d1276e6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/file_picker/file_picker-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/file_picker/file_picker-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/file_picker/file_picker.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "file_picker", "PRODUCT_NAME": "file_picker", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a441e7d83bb983a6bafa78c59bcd4380", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bf037a210e9868ac0a430277a79f9195", "guid": "bfdfe7dc352907fc980b868725387e985e16ad5a382204897f39dae52deb75b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987746178aa6e298a2f6a48f37713284c2", "guid": "bfdfe7dc352907fc980b868725387e9805d76c5f9338b9d9901b47e7245faf19", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecd60654fbc93bfd9555d1a8df410f4f", "guid": "bfdfe7dc352907fc980b868725387e98b83aa5f662d20d369e537856082fc7e9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d6d4b5a7aa2d057bae0675188eba2aa", "guid": "bfdfe7dc352907fc980b868725387e98811b30f5554ce5068909aed466eaec6f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898d5f279937a203b439c5b5e99de0b6a", "guid": "bfdfe7dc352907fc980b868725387e988b9ed0852fc432b70d5027492d4accf2", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e0bc081bbc292a5c12fc4e5f3f921a39", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988257f63c58a38cb5ae3b2e1e04735652", "guid": "bfdfe7dc352907fc980b868725387e98fc1269b55757729ccc202cd52d75b18c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d762d6991dce2b25fcfd7165d5b3230", "guid": "bfdfe7dc352907fc980b868725387e9840d491c3a923e29e1ea9f16c5487fb8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866f186bc4999692910aa0943e02b3e8e", "guid": "bfdfe7dc352907fc980b868725387e98fe793b778649b86d5aecbda3bbce0383"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98deae3811e6dbfc193efb6ab734b4ee6a", "guid": "bfdfe7dc352907fc980b868725387e98a224f3262e34211afc2d090224c46f88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98301789508e435e0b68b086bd34aa7985", "guid": "bfdfe7dc352907fc980b868725387e98692975de4e8fe928757c86ff190435fa"}], "guid": "bfdfe7dc352907fc980b868725387e986fd9f1028afcb0fc7d723b030bb9b3a5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9869664cd59b56171ce6bd4a54b67071c4", "guid": "bfdfe7dc352907fc980b868725387e98b469fafd3d129ee7bbb385053a38f76e"}], "guid": "bfdfe7dc352907fc980b868725387e98aafd7c90e67e0c65f0d0dfd16af8292a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e987c45671648cc8b310228a813befdc42c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e985fd5cdb9993b1816141f0c012ffa62bd", "name": "DKImagePickerController"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98bf75f71a2aa7fdc38a7d7d70e6c200c1", "name": "file_picker", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981da9957ede39da2a81727560e62463b0", "name": "file_picker.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}