{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e0293f0f37f2cb055c024713a9d672bb", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/HydraAsync/HydraAsync-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/HydraAsync/HydraAsync-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/HydraAsync/HydraAsync.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Hydra", "PRODUCT_NAME": "Hydra", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9891b281539f0c4faa1913effc3482f77b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988b2741909c2f399dff6e9db02fcb6aef", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/HydraAsync/HydraAsync-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/HydraAsync/HydraAsync-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/HydraAsync/HydraAsync.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Hydra", "PRODUCT_NAME": "Hydra", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98746d1fa071b422766c0f167cae0f9e79", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988b2741909c2f399dff6e9db02fcb6aef", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/HydraAsync/HydraAsync-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/HydraAsync/HydraAsync-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/HydraAsync/HydraAsync.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Hydra", "PRODUCT_NAME": "Hydra", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9847e894582c4bfa60c34f20a57156c92c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98194d3b434c390ec41228dc9c947287ee", "guid": "bfdfe7dc352907fc980b868725387e9810a93ccc09beb9b3354e17ee3998444a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988dd8eadce6ef6f1175892eefdd3d65a1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f4a9aa64d4c9c13bd697844ea40334c4", "guid": "bfdfe7dc352907fc980b868725387e98b33420f7d105cb1761a88c28edbeb308"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2df9232c83522a9e106b1ad49988396", "guid": "bfdfe7dc352907fc980b868725387e983f293d59674e28e903cd5c38011b74fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98352f29e8191a0a2aaf286ba005c19b66", "guid": "bfdfe7dc352907fc980b868725387e9859120bbcd86fbeb7881edfb3a9984f27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98252a6ee63cd65a21c90cae171a569105", "guid": "bfdfe7dc352907fc980b868725387e9820e56505e8aeb91374dace150339ceed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98329559ffa45c483c139da9876a0dea54", "guid": "bfdfe7dc352907fc980b868725387e9825b46cff1414a87bcb421060ff9c5ae7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb878a4810fd787b77726a7c5111082d", "guid": "bfdfe7dc352907fc980b868725387e98a80400919540dd0ab200502a95f3a762"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861ee3c51dfac8ede2f2adacec6a3afb8", "guid": "bfdfe7dc352907fc980b868725387e98617e65b2fb490f93d6dd9388cc8fc8a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c2d68cb855accda3e1d684498ba96ce", "guid": "bfdfe7dc352907fc980b868725387e98a37a8820fd3026fb797686be8b0138a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816db52ac35c654459edc264e7933ba96", "guid": "bfdfe7dc352907fc980b868725387e9888b98515cb62b6f82dc12459ada138f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98099261a76df7b80a264791b421272eea", "guid": "bfdfe7dc352907fc980b868725387e98f55eadf21e1ba2c8d92e683a03e8e202"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859ae4a62a086d4088be1d02ad11ee516", "guid": "bfdfe7dc352907fc980b868725387e98ff10277fc8ddd7bc3855685988096ed3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f552b33b4d3a4a0341ce276c771a7d1c", "guid": "bfdfe7dc352907fc980b868725387e981fb69e8038a14a2677ca98cc9fb4314e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de7451f9a46a4b520493a4a643898b0b", "guid": "bfdfe7dc352907fc980b868725387e98308a34999ce96af86f2355174caa6663"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c6ea364cb2ddcf661ea3e1673231936", "guid": "bfdfe7dc352907fc980b868725387e9896b02d7a9f69f7f42858e85b33941c9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3ac9aa2a43fe205855ece4d58063a82", "guid": "bfdfe7dc352907fc980b868725387e98fe5b32f2daf02c4b75b54a48c844e5d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866534be67cd2047b9698792d3cfef7da", "guid": "bfdfe7dc352907fc980b868725387e98a1e56f4ed71561582e09ac89f9340cee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844f25f01236afe2a7217dce5009cc3e8", "guid": "bfdfe7dc352907fc980b868725387e98684a4e5547afdbd789684ccb1a269b82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c44d5b0fd2e506c6895467562e552dd8", "guid": "bfdfe7dc352907fc980b868725387e981693d1fc97830f3fc76f595ce193ef10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd68840ce8a611de55b959ed24150c12", "guid": "bfdfe7dc352907fc980b868725387e9829a2c0a738b47d52ea948fe90fd657e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855368872dd6288a6cd25117af8ca9f61", "guid": "bfdfe7dc352907fc980b868725387e98380d4f502e4ec04008ae9cc3c30071b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ece7e4975a80ac705fad0bf508f8106", "guid": "bfdfe7dc352907fc980b868725387e982cf0b422a1a5bdf12ec6c7838c6861c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eefef8f3dd323891f4ce6a0117768cb8", "guid": "bfdfe7dc352907fc980b868725387e984efed78d20380b050aacb355fa197266"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5f0d2e53070851bb2c1618320b1cdb8", "guid": "bfdfe7dc352907fc980b868725387e98aba5a2e850ceffded57a92fb08e5e412"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f351df84c8c57e3aa295c81d18381913", "guid": "bfdfe7dc352907fc980b868725387e98266fd28728a9f8a77e27b8745e1fa2dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98023d791a9cd515220ba115112843d7e6", "guid": "bfdfe7dc352907fc980b868725387e98e8d3a26deb2cdd9cab87542ff96e0b2d"}], "guid": "bfdfe7dc352907fc980b868725387e98d3bdf33a0768e2763ff3db64d7dcb062", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9869664cd59b56171ce6bd4a54b67071c4", "guid": "bfdfe7dc352907fc980b868725387e988a328da6aaf486fd18ad6dba8c844b1a"}], "guid": "bfdfe7dc352907fc980b868725387e983b5bef38b5d3c1a2e469ca39955e52f5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9888181c19099c8b92f4ffcdffb4c3e3f6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98df71c66ecc82a9796c50f296fb0a215b", "name": "HydraAsync", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9874406eff01050d7992f58c088679c282", "name": "Hydra.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}