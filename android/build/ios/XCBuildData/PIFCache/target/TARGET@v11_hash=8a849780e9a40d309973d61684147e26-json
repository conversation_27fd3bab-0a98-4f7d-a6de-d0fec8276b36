{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9843aded7222ead9b9c19f7fc29df2a1f9", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/GCDWebServer/GCDWebServer-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/GCDWebServer/GCDWebServer-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GCDWebServer/GCDWebServer.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GCDWebServer", "PRODUCT_NAME": "GCDWebServer", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9893f42daf829a19e29e67f219e416396e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983052d89dfad131d53036b07f72a3acde", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/GCDWebServer/GCDWebServer-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/GCDWebServer/GCDWebServer-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GCDWebServer/GCDWebServer.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GCDWebServer", "PRODUCT_NAME": "GCDWebServer", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c1c8b851c901c5a56792186be2c22d1e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983052d89dfad131d53036b07f72a3acde", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/GCDWebServer/GCDWebServer-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/GCDWebServer/GCDWebServer-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GCDWebServer/GCDWebServer.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GCDWebServer", "PRODUCT_NAME": "GCDWebServer", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981bdb4905631996d6ccb0c4545a27b15e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9876fed914fec1508dfd79ccbd27ac7fdb", "guid": "bfdfe7dc352907fc980b868725387e98c6f0b8e728a0ed20a7dfcc50baafb066", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860399c35a093ec297353660bd2761fa6", "guid": "bfdfe7dc352907fc980b868725387e9850e0816339a86b3de0583f3d8da5bd5c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6ebd1b4e66f77e051f2ac795d365ddc", "guid": "bfdfe7dc352907fc980b868725387e98250bec32f4e0a4fbff4f6b40f0b17e58", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823704026e8b1cc686c4c54b7fdcc5369", "guid": "bfdfe7dc352907fc980b868725387e98404e7d2e5b79de01da4b86c3df988adc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea4ab0a32e3a1dc46e92375afce2f865", "guid": "bfdfe7dc352907fc980b868725387e98836bb297925c6838652e4ad5e280600c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2101a6e86589805d6a46b4547f98c8f", "guid": "bfdfe7dc352907fc980b868725387e988c026de31cb5b84c3f237d1a0a77143c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d6cf0c32e66fd96aad63ee2c149e03e", "guid": "bfdfe7dc352907fc980b868725387e98536bc3226e94a6fce00d94b0887063d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a0ae17d0e8c281301462c43aea142d6", "guid": "bfdfe7dc352907fc980b868725387e98341beb131176ea45f5561b460aafba1e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985795246e9825824a0d8e620c608e76fb", "guid": "bfdfe7dc352907fc980b868725387e988c871a992d648ceb94fa581c5ca12a9d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840ed4062bae3b600a2958fccad929064", "guid": "bfdfe7dc352907fc980b868725387e989fb7ddb77f2ec335564acc62b1c1518c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9afe660aa6ce92547b0b073a1305e54", "guid": "bfdfe7dc352907fc980b868725387e98f65bac6b9b6df6d8934628c359f5a694", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c8949f98cb26253a6c92607aad48900", "guid": "bfdfe7dc352907fc980b868725387e982c325530838a389c9388c9384bca41cd", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc2f7d8b05917ae53fd0ec78c2cc8688", "guid": "bfdfe7dc352907fc980b868725387e980bf82a4d0232d53ba01bec724dcb942e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98883d571c0af77728a3602293d735d9b5", "guid": "bfdfe7dc352907fc980b868725387e984aa38768d32c551b768f0f289663fc84", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a6d7ac68e917f8f729656c41a14fa38", "guid": "bfdfe7dc352907fc980b868725387e98b97430183ec4aeb73eebb0758d19cc85", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b379b9f97bacabca21640b00ff72124", "guid": "bfdfe7dc352907fc980b868725387e98dd7b91ac4952d72f5c61db8acab21c55", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9896797cb91c21acd4c5d890e2b980905b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989fe31305e5ebbaf29937dfda5857b149", "guid": "bfdfe7dc352907fc980b868725387e9831c7c82f564421f563b092ed30f9812c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803826570d0e1bd9595e400d2660c788c", "guid": "bfdfe7dc352907fc980b868725387e982a8d1cf8d04e474725aaa741150b3f59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf781143932ef0fe69775fab9ed3cd28", "guid": "bfdfe7dc352907fc980b868725387e984e8ae00569ae723cf085e8723e53af29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b27b178356929a815155303be25c02b", "guid": "bfdfe7dc352907fc980b868725387e9861688f254aa77512f5bbeb47cb4e3a7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98777bece2cc45a6416af7e3e19c8bab1d", "guid": "bfdfe7dc352907fc980b868725387e9859174f47142766b37afb50c0ccf38b87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b914a3404f14d17c9bcb5063f330e367", "guid": "bfdfe7dc352907fc980b868725387e9891b9a0258dbe1f52f6bcd7dee51ccda7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a8dad93e7eab3cf477144ff0a818a31", "guid": "bfdfe7dc352907fc980b868725387e98d49bdaca76f9463a1a2e3b658e3cca14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d33c7eacd8ecb3c44de636ea106f413", "guid": "bfdfe7dc352907fc980b868725387e98723a5d497569158c5f62dacece9a059f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986530e7d554928467e760266283bcbd85", "guid": "bfdfe7dc352907fc980b868725387e98cf5c320670cfacaebe544adcab3e609e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859f99cf8267ee451df7adb28b0c743ac", "guid": "bfdfe7dc352907fc980b868725387e98a58473dd7e08426ca585becf16a2bc7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827161e7ade0052258395b4fc5d4b1c98", "guid": "bfdfe7dc352907fc980b868725387e98df38afbda30261bfc00501ede263de05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcb6b37235d9a229c96f35744abcc3a3", "guid": "bfdfe7dc352907fc980b868725387e987366494826acbf5d395743740cf55bfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da9b81bc77573e597c2154749681da61", "guid": "bfdfe7dc352907fc980b868725387e98de20f97a59ae3c68cc7c7157860c7319"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98123fe8fc1ef2b6e46634c3878411adc2", "guid": "bfdfe7dc352907fc980b868725387e98de9a1bb137caeec222e08a46b9fffb1d"}], "guid": "bfdfe7dc352907fc980b868725387e986fa4959e5362e884deafa63039d137bc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98073b37e86455e8c4a07903117f92db6e", "guid": "bfdfe7dc352907fc980b868725387e98bdd0976d25801e8b99b29fbf6633da73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869664cd59b56171ce6bd4a54b67071c4", "guid": "bfdfe7dc352907fc980b868725387e981b28db397f6e20dc4e2f42e2fa295f30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989677e65a9b73bf4374056acb4d80c9a4", "guid": "bfdfe7dc352907fc980b868725387e98e4022c02d4cfe2ab9b29c083aef9af57"}], "guid": "bfdfe7dc352907fc980b868725387e98f8f350f1efe9c75fd26af935262a763a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98304632b5fb989273495c0d36a6cdb1a0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98ae4cba9dafde33c1f8a32ad5773d6cd3", "name": "GCDWebServer", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ea940bf2ad9c0f24726e4c14aa315ff6", "name": "GCDWebServer.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}