{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9810e5482cbbc6ef8a0e8433ae0d8dc44c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "tencent_cloud_chat_sdk", "PRODUCT_NAME": "tencent_cloud_chat_sdk", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ef8303e3c125cfb66805def5ccde7550", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a421ae680f264f03d9ad9282a2347462", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "tencent_cloud_chat_sdk", "PRODUCT_NAME": "tencent_cloud_chat_sdk", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b0a76b30e080ca862389700969e1400c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a421ae680f264f03d9ad9282a2347462", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "tencent_cloud_chat_sdk", "PRODUCT_NAME": "tencent_cloud_chat_sdk", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984dc9fca47311d84290958ef7e8f90abc", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989f8441ae29966760570a96ee4d620b9c", "guid": "bfdfe7dc352907fc980b868725387e9830491ce6d8dc79d27eac396fa36020c3", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9863f8a399148fcd97797831916a9c56d4", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989c257db58862ff96eebe092d48c7f72b", "guid": "bfdfe7dc352907fc980b868725387e98ea7e1abff4cac458084461e8cd01bc58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813517cc35f485c85cc342524ac9fc073", "guid": "bfdfe7dc352907fc980b868725387e98060d4bc79daa9bf928aee89fac417f7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f30d716950c0176f9986f5fdc94bf3ae", "guid": "bfdfe7dc352907fc980b868725387e98e8993c7a407fe2ad5d431aafd2c6215f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839c9aea45a523cd84916b433b32e597c", "guid": "bfdfe7dc352907fc980b868725387e98ba81b84674588537fe04ef18c5d16795"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b519f25ff76eac3f7c8116dce4a70512", "guid": "bfdfe7dc352907fc980b868725387e9874bb66b5829d998b8cbdaf123cff3be2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe4a9118ee18e94b6ee6eea7f0a3a743", "guid": "bfdfe7dc352907fc980b868725387e983c705081de3688f163899f0536692c90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1aa78341a233986bc43d7faee25f7c2", "guid": "bfdfe7dc352907fc980b868725387e984fb868d2df3e2366ebf682253d5ffdb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98390bde8d06cc0105bf776cc016c2b1b5", "guid": "bfdfe7dc352907fc980b868725387e98df608da25d99c26b35f60c7c9fac1eb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf858dc262000835ec2c6537f1492d7d", "guid": "bfdfe7dc352907fc980b868725387e984bced858ce0ce36004e7d7d4b7c37d67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cda767e5bd658d383e720a8ee279a56c", "guid": "bfdfe7dc352907fc980b868725387e98c94ef7b038d96fcd5dae3d768b530c78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f61018c0fee1e2647fdb884ace613c56", "guid": "bfdfe7dc352907fc980b868725387e987173a4dfd4f950425daa4e1da6bb105f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e66ab7084ebfc2bcc3290997ff9b5f22", "guid": "bfdfe7dc352907fc980b868725387e989ae6771aaf52c0951345ac141ad71b7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fca4e7b59f19c9449e99e45fe9a6cff", "guid": "bfdfe7dc352907fc980b868725387e98ce9b9a765145f0795abb19e1839b40b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fe9fbf352784956640c23c606687666", "guid": "bfdfe7dc352907fc980b868725387e9871940d863dc78e482da4cb26f5f8bd3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc47acce94eb886b7ad628adcb5fd2f4", "guid": "bfdfe7dc352907fc980b868725387e9857b8698cd3e52fdbf4af1797de4f914c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b91efeb01610db66b9e4e19ef054b73f", "guid": "bfdfe7dc352907fc980b868725387e98ec19e0800077344fb2585a57fe371e27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c95fcc3e9a0ac4183161563802499aa", "guid": "bfdfe7dc352907fc980b868725387e98dd6ae637ab4568d751116267579386c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dad621abd4c4e37129fc5c04b8ef95ab", "guid": "bfdfe7dc352907fc980b868725387e984d1c307371d63002bc9ad6aa12bb2547"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5e3639cee8b63b7ca7f73e6e55cba6e", "guid": "bfdfe7dc352907fc980b868725387e98bab5cec3335ff4b825e0a64456efac0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875e5acacb5f266f8c27d4d87e3936fc4", "guid": "bfdfe7dc352907fc980b868725387e98ea3126378778f7cd2fbdca4a466a17b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f361dbb98af7c1d2cf2ea3e82cbb9c32", "guid": "bfdfe7dc352907fc980b868725387e984be692bfca5cb56a636a32cdaa25324a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98080e5ebdc4437ea551d190ede4bed563", "guid": "bfdfe7dc352907fc980b868725387e988f945c39521360aefafbaa74691b72da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848704b665d6ae9f4683136aefd56950d", "guid": "bfdfe7dc352907fc980b868725387e982273b605181d8e5507783e856937afec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1b662c45bf220fbdb279c7779d472ce", "guid": "bfdfe7dc352907fc980b868725387e989d8d8645c1dd2933514b66d9b1602f50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a883209c461ac249631c9ba2b598afd1", "guid": "bfdfe7dc352907fc980b868725387e98a21609c7ecb950007cd2f2d099a78ca2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98622ce9c3fd8069bf2dbeef3efde24b4f", "guid": "bfdfe7dc352907fc980b868725387e984241a8c2723c7f989511f29eb1e358db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4aa9106df11dc11988c812f8d8ba7ac", "guid": "bfdfe7dc352907fc980b868725387e98045fff38afe999b89a47928714911590"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a764f004f77e23609825cefbb26757c", "guid": "bfdfe7dc352907fc980b868725387e984ef3c2836af48c778b8320350f25ff3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cafb9f1e5792e99fbf3617711238c24", "guid": "bfdfe7dc352907fc980b868725387e98c7f77bd19b1b7d3cba7edcc304cfc8f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da20a69faf3e29c454f63bcd394bd17b", "guid": "bfdfe7dc352907fc980b868725387e986d0487de2dd313ca62542eef5503db6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98593dd35a4e21f6c36f73d98d35c4e77e", "guid": "bfdfe7dc352907fc980b868725387e9862225c5f9a98e7d53852bb3133a241cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3a5cb249246c81f7ff24f10b0b37555", "guid": "bfdfe7dc352907fc980b868725387e98881bf000c7c5191f16763a7c01357e68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808e100b58c75a3fe8d59277b1ddf4ee3", "guid": "bfdfe7dc352907fc980b868725387e98cb35abab82ec7168e554a705242780b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df136db3f552c62e367ea311780faf8c", "guid": "bfdfe7dc352907fc980b868725387e988d6c1f58c5b7d3f6d4358566e27a364b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c7e6ef28cdb2fea465cab65441af6b1", "guid": "bfdfe7dc352907fc980b868725387e98fb81a13aa047213147ca0c2b01779ba5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844291dcdb18c6b3a321b0e5d3066abb9", "guid": "bfdfe7dc352907fc980b868725387e9854baa144c4d4974aedd8352dae79ae8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98965f3f1b2c73be9a0adf4d7fec777aed", "guid": "bfdfe7dc352907fc980b868725387e9860a984c525b36a8540ef3e295dcc1383"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb7afde6254c5956ba3ee4a296255800", "guid": "bfdfe7dc352907fc980b868725387e98fcd57a2243f7cf3106c3e356081577d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a19377be099de49ddc1caa674f54b5d9", "guid": "bfdfe7dc352907fc980b868725387e986cd5f9956ff6e7b22a4dadcc7fe8fc98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3a94a5ec746ee8d1ea9799058d9183a", "guid": "bfdfe7dc352907fc980b868725387e989c118cd160623ed12b82e772af1fb9bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b624ed0e33200ccc353b9e05cbb7f158", "guid": "bfdfe7dc352907fc980b868725387e980370a72d36ac503ef42f5aa93a81d881"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dea5d75c4ffcbc7932990053cd330023", "guid": "bfdfe7dc352907fc980b868725387e986d15ab47f8f115e9824208b733403113"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98629733688b7f9273761735a9284686af", "guid": "bfdfe7dc352907fc980b868725387e986c8208ccaf51d98d20f36ae086f29a18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98751ffd1b6706240590da83b88bfccb28", "guid": "bfdfe7dc352907fc980b868725387e987930451af270c1d5eaceef80f8ed562f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b3348e7a75ac6fab87d67db7f069058", "guid": "bfdfe7dc352907fc980b868725387e984531b3ec6188c5151c1acc0cfbc16958"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857cfd73f1219fe1d55f38a69d2a361e8", "guid": "bfdfe7dc352907fc980b868725387e98e57974e343d2e71ad7ebf21b78573ea3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985edae868ec817b78728025151ee95018", "guid": "bfdfe7dc352907fc980b868725387e98cd493cbf347e247bc2689d9ed7801e0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5f63e52addb317d2d4f2ba1927b6d4f", "guid": "bfdfe7dc352907fc980b868725387e986874a599f9120508a3f16962e4d7f897"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aff86f072c3354acf23efa5a37ed6d47", "guid": "bfdfe7dc352907fc980b868725387e986d869c98dad5ec4a139fe926009024f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba31b5c1d0cded29727f4205d42a9338", "guid": "bfdfe7dc352907fc980b868725387e98bc3419b89f7c5c8f6a61123a95d412ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2d900e134561cc45813d377501a84ef", "guid": "bfdfe7dc352907fc980b868725387e981f1b941365fba4cc57cdd3e991c0d28a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985776f9174faaa8292df9cc34ec87ca2d", "guid": "bfdfe7dc352907fc980b868725387e98639119f1a2bf6781b8864e087e41f9d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1c0619d6d739bc4406a8e208434dae5", "guid": "bfdfe7dc352907fc980b868725387e981a8629b477754a90593f6ffccb9bdaa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878f4c25908e4766ebc761d61a3f0a909", "guid": "bfdfe7dc352907fc980b868725387e983fd4fa0655fd59d25eb4bbed14587ed5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e7f41840ac81ad4a5bb2521e740674f", "guid": "bfdfe7dc352907fc980b868725387e98ab3a61d8a45addf21a2e68ef0d6a28a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846cff7e6b2b31575f0201fbac09cde39", "guid": "bfdfe7dc352907fc980b868725387e98b018e2f5520f56a0db32f27f9dd2ca95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b50c6cbcbafe6fe0657eaf7fd0c4e781", "guid": "bfdfe7dc352907fc980b868725387e98f9fbbd61c26f3365e53a01b01f6983d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d850ce31010987b8498246bc3c0dc816", "guid": "bfdfe7dc352907fc980b868725387e98d500759718ed4cf1acf3e293fc6b4695"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98900ff1aacfbf7cd066d29951a7e3630a", "guid": "bfdfe7dc352907fc980b868725387e98cb819c2c23540ea188f4a0349db34a11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871c44c967a2a4d50431ed7d454ac0917", "guid": "bfdfe7dc352907fc980b868725387e98317d98de105e001013b5b19ddf8a1a81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f063dd102b7a7ce915fbf78f16a8c342", "guid": "bfdfe7dc352907fc980b868725387e98fb7ce0a999d45116350b61625a0052f0"}], "guid": "bfdfe7dc352907fc980b868725387e98f13f1c04531f4233a5d7eb72bd1f781e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9869664cd59b56171ce6bd4a54b67071c4", "guid": "bfdfe7dc352907fc980b868725387e989ad793429916e4d52bca5c6576a40175"}], "guid": "bfdfe7dc352907fc980b868725387e980b95e6dc4af2e84517bb62cd3ea67115", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98df532b4da6dd2e55c1e3fb77ee42c827", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98df71c66ecc82a9796c50f296fb0a215b", "name": "HydraAsync"}, {"guid": "bfdfe7dc352907fc980b868725387e98f1e4294f9d6a6e169e02b24c5d04222a", "name": "TXIMSDK_Plus_iOS_XCFramework"}], "guid": "bfdfe7dc352907fc980b868725387e98ba204d184f78a46e2605c4a18658ab11", "name": "tencent_cloud_chat_sdk", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ec6577985ee917b8779818a66bcd1b13", "name": "tencent_cloud_chat_sdk.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}