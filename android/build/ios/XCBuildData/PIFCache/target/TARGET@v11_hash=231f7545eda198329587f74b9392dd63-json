{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a89c5d50243bb202441eeebacfc7a104", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9810bdb3018dba7b3fc87b085e5c7a18ec", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983ad8b2dab5b7f12a22129324bba6a644", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9808213667fea3cf0e28f6feb0339126c5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983ad8b2dab5b7f12a22129324bba6a644", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9892b119b956a581fc1e399b4ce8515719", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987fb95a6e054c536717a6761a0bbd734a", "guid": "bfdfe7dc352907fc980b868725387e98ba40db6ec69aceff174ee9174f36a8ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897c34f412036017a451c361a34f97556", "guid": "bfdfe7dc352907fc980b868725387e98206b57e8470055960ebbc2b3cb3cf001", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb08e543c59d3f9166a961bc16b14a2a", "guid": "bfdfe7dc352907fc980b868725387e982872500d6e8f4554580d9bb1bae6272b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ca362c151d4003067272a986ef87140", "guid": "bfdfe7dc352907fc980b868725387e9879ec4d3754f0007552471e765899015f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897a0a746146b82f4c74b1c7b5a6b8029", "guid": "bfdfe7dc352907fc980b868725387e985ce6a56c6d21035925736adc7b23f8d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b768376ca5390ae3bed3c10b405e7f2", "guid": "bfdfe7dc352907fc980b868725387e987b93c2b2140ccc82864a26fcfc2118d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fae6a4d3cfd2954bc3ae4d0c829d4243", "guid": "bfdfe7dc352907fc980b868725387e983a4c66b9b91a919b96e74536759e67c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dfec47db3cee58212bcd641c4d98c79", "guid": "bfdfe7dc352907fc980b868725387e9812c6d42cf21cf7e6b6cbb1cc07985b53", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859001502c98e2d3ec0904b75f4cbf3db", "guid": "bfdfe7dc352907fc980b868725387e988d74347dcfd98ba34b20a3db2cb638b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6d33cb7b60140b081e05a20d1de3bf9", "guid": "bfdfe7dc352907fc980b868725387e98c850ee5fbb6d5732cf44861b15549b50", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ddd81d19afdaa62c68d4f083f3b99f8", "guid": "bfdfe7dc352907fc980b868725387e98b73041bb8f520a300118c1d2b04ddcf1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98143159e1cfc4abc4efafe44fcd4a2dec", "guid": "bfdfe7dc352907fc980b868725387e988172efcf7e539cec45f244c654a74a8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98274c048cdbafa3b8fb1df45e21230c5d", "guid": "bfdfe7dc352907fc980b868725387e98f336a55ce4516bb93675432f8b89cc86", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98239883b5d6b159ac7bae12b1aa67c3cb", "guid": "bfdfe7dc352907fc980b868725387e985fb7de7a1e311a0e45c496bf86f96881", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832ea031d5dec5919849af92c6c559e9c", "guid": "bfdfe7dc352907fc980b868725387e987f38d62a08cefacdf38292a4d918555b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d1e43d6c236bcc2b71ecb0fc9c5a3f8", "guid": "bfdfe7dc352907fc980b868725387e98669adf5bcb1ceb3854e055ff414db3c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f60c141549304e6ef5e01897d7c3607a", "guid": "bfdfe7dc352907fc980b868725387e98fddc7176b8f49904b4310ce12e8046dc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839e8242a71c762e6f5ea7c3133037d46", "guid": "bfdfe7dc352907fc980b868725387e98a2458c22844b5bb42844e7af71e945d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3c700d5ad97c202c20595bcf7415f92", "guid": "bfdfe7dc352907fc980b868725387e98c971490bd82ce4df4545c041e460a777", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809d4e8ba0757244199b75d9c9b718e32", "guid": "bfdfe7dc352907fc980b868725387e988b68bf5cd7e9b92c51486132ba88b32a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1e383ee172b896a2a74ea6fdd0af1ee", "guid": "bfdfe7dc352907fc980b868725387e98d8e6c985d7f2389d03d3e0bd5efbc1c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869d2d47b537fcba781c88141f6e7db63", "guid": "bfdfe7dc352907fc980b868725387e9863f22d534f67bca6e9ad3c753cdacec0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829fb560b1ddb525c1f4593ccbd86869c", "guid": "bfdfe7dc352907fc980b868725387e984d2017d3024b2cf3050d2650d8074e7a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828de89f55b3c14c6f0ba302c6e6ec1c9", "guid": "bfdfe7dc352907fc980b868725387e9839b015542199b813c3b06098476f1592", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b809dbfa4c6712e1c19e51590af7fa04", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9897d71d9045867b0098be0d6a6d28ae09", "guid": "bfdfe7dc352907fc980b868725387e981a75667d9873b4a87eba193da4ce2f20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982652376936051f79d868701b005f7ef1", "guid": "bfdfe7dc352907fc980b868725387e9844b179bdb514196ab134c5c5d927a310"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98859baa4c2203d8c70df2c028c9f420ae", "guid": "bfdfe7dc352907fc980b868725387e987983c6d6765984ee811d3afdbb57c607"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866feb4077a66a5200776cee25637e8f9", "guid": "bfdfe7dc352907fc980b868725387e98771ec4b0dacc5fa54a56b48ec9c2f1ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a72697456a51f1a4557944255ac602fa", "guid": "bfdfe7dc352907fc980b868725387e98dd96eced9f9337b20e85c6e79e164481"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b8b64c2447cef26e1b04eb2b331e58a", "guid": "bfdfe7dc352907fc980b868725387e98670a33b8e0df31762e441d4ad93e43f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884182698d3fbcafb7e8e7aa555a86766", "guid": "bfdfe7dc352907fc980b868725387e9851fadca02be4e5c429f5c659229f82ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98757fb21797c8ddf1e505474787592c7e", "guid": "bfdfe7dc352907fc980b868725387e9809988a0e815b9800106b5c634c96b549"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e85a0936f90ad160a9d15670951de521", "guid": "bfdfe7dc352907fc980b868725387e981dbef84de0b991d2402735faf8c37331"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98661e31be5b89fe45e03a849dcd474016", "guid": "bfdfe7dc352907fc980b868725387e9891fbd3cea52973a644ad00c635669405"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839e34af143c9a6e78331dcef9d220958", "guid": "bfdfe7dc352907fc980b868725387e985c84a995b5a71d84999e6f82b3a1616d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb8ad7a8df0755fa9c48d81711698e2a", "guid": "bfdfe7dc352907fc980b868725387e98268bac931f9b7f96d57966fae54e9fce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a0062a8c221b2a9ac4db91f0794a773", "guid": "bfdfe7dc352907fc980b868725387e9809b731d6dc1495a108bde0e533ca1d0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c61fe0285c221986534b6b6716bbae17", "guid": "bfdfe7dc352907fc980b868725387e98fe5feed92975fbddaf340bed1f092d29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ee17e582d67aae507f10fa648892236", "guid": "bfdfe7dc352907fc980b868725387e980cd27700d106a1d3aafbc07cae9c8208"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832aaf798eb64d6c9a9d6335c953644de", "guid": "bfdfe7dc352907fc980b868725387e9891203d6f8705520295a5fb4c3ea9b6de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f6602c3312234ecbff934e1ebdcfe7a", "guid": "bfdfe7dc352907fc980b868725387e9895250e54c9b0b38a37e050538e745a79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989779f55532b547af2abd2f70dba89cb2", "guid": "bfdfe7dc352907fc980b868725387e98980b631e917bff20fb2759a1c4616031"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d485c3ef9f627f8eef8c3cfd48f8e5af", "guid": "bfdfe7dc352907fc980b868725387e984c1700f815fe04b11747d28c095a81e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98982539d3849cf16873f5c2f38ac5ce2c", "guid": "bfdfe7dc352907fc980b868725387e98900f652aaad26ab6fd60e6ce7f64ff7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba55a3f9cbdb36dc4ffaa15cf85f15d9", "guid": "bfdfe7dc352907fc980b868725387e98db33a33fd491c8b64695f722472cc802"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c64d69235582ac32c7a6f2fc3464ab45", "guid": "bfdfe7dc352907fc980b868725387e9868a260301aa085869cb1432a180bd91c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc65d993cca88d8133a3e84d8cc8970d", "guid": "bfdfe7dc352907fc980b868725387e989980e3343a39ecfb8cc0522bda25f465"}], "guid": "bfdfe7dc352907fc980b868725387e98295a6a912ea2f996619108451429afc3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9869664cd59b56171ce6bd4a54b67071c4", "guid": "bfdfe7dc352907fc980b868725387e98d69d3f754ff2570528c4de0715d27ea9"}], "guid": "bfdfe7dc352907fc980b868725387e986ff48b1d20b26e526e96f1291209d3a7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ca04b2f98dda4aa3ea7d695df2288e55", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e98b319dc9b9bc94b538d198b41cff1b6d9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}