{"version": "", "tasks": [{"label": "Setup JS Environment", "type": "shell", "command": "echo", "args": ["Setting up JS environment"], "group": "none"}, {"label": "Setup YL Environment", "type": "shell", "command": "echo", "args": ["Setting up YL environment"], "group": "none"}, {"label": "Build Web (JS)", "type": "shell", "command": "fvm", "args": ["flutter", "build", "web", "--dart-define=CHANNEL=JS", "--dart-define=DEBUG=true"], "group": "build", "dependsOn": "Setup JS Environment", "problemMatcher": "$flutter-analyzer"}, {"label": "Build Web (YL)", "type": "shell", "command": "fvm", "args": ["flutter", "build", "web", "--dart-define=CHANNEL=YL", "--dart-define=DEBUG=true"], "group": "build", "dependsOn": "Setup YL Environment", "problemMatcher": "$flutter-analyzer"}, {"label": "Build APK (JS)", "type": "shell", "command": "fvm", "args": ["flutter", "build", "apk", "--flavor", "JS", "--dart-define=CHANNEL=JS", "--dart-define=DEBUG=true"], "group": "build", "dependsOn": "Setup JS Environment", "problemMatcher": "$flutter-analyzer"}, {"label": "Build APK (YL)", "type": "shell", "command": "fvm", "args": ["flutter", "build", "apk", "--flavor", "YL", "--dart-define=CHANNEL=YL", "--dart-define=DEBUG=true"], "group": "build", "dependsOn": "Setup YL Environment", "problemMatcher": "$flutter-analyzer"}, {"label": "Build IPA (JS)", "type": "shell", "command": "fvm", "args": ["flutter", "build", "ipa", "--flavor", "JS", "--dart-define=CHANNEL=JS", "--dart-define=DEBUG=true"], "group": "build", "dependsOn": "Setup JS Environment", "problemMatcher": "$flutter-analyzer"}, {"label": "Build IPA (YL)", "type": "shell", "command": "fvm", "args": ["flutter", "build", "ipa", "--flavor", "YL", "--dart-define=CHANNEL=YL", "--dart-define=DEBUG=true"], "group": "build", "dependsOn": "Setup YL Environment", "problemMatcher": "$flutter-analyzer"}, {"label": "Build All YL", "dependsOn": ["Build Web (YL)", "Build APK (YL)", "Build IPA (YL)"], "group": {"kind": "build", "isDefault": false}}, {"label": "Build All JS", "dependsOn": ["Build Web (JS)", "Build APK (JS)", "Build IPA (JS)"], "group": {"kind": "build", "isDefault": false}}]}