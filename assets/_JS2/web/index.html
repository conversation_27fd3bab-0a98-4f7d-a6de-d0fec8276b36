<!DOCTYPE html>
<html>
  <head>
    <!-- 把加载动画的样式放在最前面，确保最先加载 -->
    <style>
      body {
        background-color: white;
        font-family: "Roboto", "PingFang SC", "NotoSans SC", sans-serif;
        overflow: hidden;
      }
      .loader {
        height: 100vh;
        position: relative;
        z-index: 99999;
        background: white;
        overflow: hidden;
      }
      .loadingicon {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 124px;
        height: 24px;
        -webkit-mask: conic-gradient(
              from 135deg at top,
              #0000,
              #000 0.5deg 90deg,
              #0000 90.5deg
            )
            0 0,
          conic-gradient(
              from -45deg at bottom,
              #0000,
              #000 0.5deg 90deg,
              #0000 90.5deg
            )
            0 100%;
        -webkit-mask-size: 25% 50%;
        -webkit-mask-repeat: repeat-x;
        background: linear-gradient(#cfb096 0 0) left/0% 100% no-repeat #ddd;
        animation: l13 2s infinite linear;
        margin-bottom: 15px;
      }

      #loading-text {
        font-size: 16px;
        margin-bottom: 5px;
      }

      #countdown {
        font-size: 14px;
        color: #666;
      }

      .extra {
        position: absolute;
        bottom: 10vh;
        text-align: center;
        width: 100%;
      }

      @keyframes l13 {
        100% {
          background-size: 100% 100%;
        }
      }

      .dots {
        display: inline-block;
        width: 24px;
        text-align: left;
      }

      @keyframes blink {
        0% {
          opacity: 0;
        }
        50% {
          opacity: 1;
        }
        100% {
          opacity: 0;
        }
      }
    </style>

    <script src="scripts/fp.min.js"></script>

    <!-- 统计脚本 -->
    <script
      charset="UTF-8"
      id="LA_COLLECT"
      src="scripts/js-sdk-pro.min.js"
    ></script>
    <script>
      LA.init({ id: "KgqIl42atn1zQ8Z3", ck: "KgqIl42atn1zQ8Z3" });
    </script>
    <!--
      If you are serving your web app in a path other than the root, change the
      href value below to reflect the base path you are serving from.

      The path provided below has to start and end with a slash "/" in order for
      it to work correctly.

      For more details:
      * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

      This is a placeholder for base href that will be replaced by the value of
      the `--base-href` argument provided to `flutter build`.
    -->
    <base href="$FLUTTER_BASE_HREF" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta charset="UTF-8" />
    <meta content="IE=Edge" http-equiv="X-UA-Compatible" />
    <meta name="description" content="金沙娱乐" />
    <meta
      http-equiv="Content-Security-Policy"
      content="upgrade-insecure-requests"
    />

    <!-- iOS meta tags & icons -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-mobile-web-app-title" content="game_store" />
    <link rel="apple-touch-icon" href="icons/Icon-192.png" />
    <!-- 以下脚本已改为动态加载 -->
    <!-- 
    <script src="./node_modules/tim-upload-plugin/index.js"></script>
    <script src="./node_modules/tim-js-sdk/tim-js-friendship.js"></script>
    -->

    <!-- 腾讯IM - 已改为动态加载 -->
    <!-- 
    <script src="./node_modules/tim-upload-plugin/index.js"></script>
    <script src="./node_modules/@tencentcloud/chat/index.js"></script>
    <script src="./node_modules/@tencentcloud/chat/modules/group-module.js"></script>
    <script src="./node_modules/@tencentcloud/chat/modules/relationship-module.js"></script>
    <script src="./node_modules/@tencentcloud/chat/modules/signaling-module.js"></script>
    -->

    <!-- 网易验证码 - 已改为动态加载 -->
    <script defer>
      const timestamp = Math.floor(Date.now() / 60000);
      const script = document.createElement("script");
      script.src = `scripts/load.min.js?t=${timestamp}`;
      document.head.appendChild(script);
    </script>

    <!-- Video_Player_HLS插件 -->
    <script src="scripts/hls.js" type="application/javascript" defer></script>

    <!-- Favicon -->
    <link
      rel="icon"
      href="assets/assets/images/logo/logo_32.png"
      type="image/png"
    />

    <title>金沙娱乐</title>
    <link rel="manifest" href="manifest.json" />
  </head>
  <body>
    <!-- 把加载动画放在 body 的最开始 -->
    <div class="loader">
      <div class="loadingicon"></div>
      <div class="extra">
        <div id="loading-text">正在选择最佳线路<span class="dots"></span></div>
        <div id="countdown">5</div>
      </div>
    </div>

    <script src="flutter_bootstrap.js" async></script>
    <script src="scripts/loading.js" async></script>
    <!-- 脚本加载管理 -->
    <script>
      // 监听 Flutter 初始化完成事件
      window.addEventListener("flutter-first-frame", function () {
        // 延迟加载非关键脚本
        setTimeout(() => {
          // 自动加载腾讯IM
          loadTencentIM();
          // 其他可以延迟加载的脚本
        }, 2000); // 延迟2秒加载，可以根据需要调整
      });

      // 腾讯IM相关脚本
      function loadTencentIM() {
        const scripts = [
          "./node_modules/tim-upload-plugin/index.js",
          "./node_modules/@tencentcloud/chat/index.js",
          "./node_modules/@tencentcloud/chat/modules/group-module.js",
          "./node_modules/@tencentcloud/chat/modules/relationship-module.js",
          "./node_modules/@tencentcloud/chat/modules/signaling-module.js",
        ];

        scripts.forEach((src) => {
          const script = document.createElement("script");
          script.src = src;
          script.async = true;
          document.head.appendChild(script);
        });
      }
    </script>
  </body>
</html>
