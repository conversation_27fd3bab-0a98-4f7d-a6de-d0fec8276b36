<!DOCTYPE html>
<html>
<head>
    <!--    <title>HLS Video Player</title>-->
    <style>
        body {
           height: 100%;
           margin: 0;
           padding: 0;
         }
         video {
           width: 100%;
           aspect-ratio: 16/9;
           height: auto;
         }
        video::-webkit-media-controls-fullscreen-button {
           display: none;
        }
    </style>
</head>
<body>
<div style="position: relative;">
    <video playsinline webkit-playsinline id="player" controls></video>
</div>

<script src="https://cdn.jsdelivr.net/npm/hls.js@1.2.8/dist/hls.min.js"></script>
<script>
    var video = document.getElementById('player');
    var overlay = document.getElementById('overlay');

    document.addEventListener('fullscreenchange', function () {
        if (document.fullscreenElement) {
            document.exitFullscreen();
        }
    });

    video.addEventListener('touchend', function(event) {
        event.preventDefault();
    });

      if (video.canPlayType('application/vnd.apple.mpegurl')) {
        // <PERSON>fari has native HLS support
        video.src = "{{url}}";
        video.addEventListener('loadedmetadata', function() {
            video.play()
            .then(function() {
                overlay.style.display = 'none';
            })
            .catch(function(error) {
                console.log("Play failed:", error);
            });
        });
      } else if (Hls.isSupported()) {
            var hls = new Hls();
            hls.loadSource("{{url}}");
            hls.attachMedia(video);
            hls.on(Hls.Events.MANIFEST_PARSED, function() {
                video.play();
                overlay.style.display = 'none';
            });
        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
            video.src = '{{url}}';
            video.addEventListener('canplaythrough', function() {
                video.play();
                overlay.style.display = 'none';
            });
        }
</script>
</body>
</html>