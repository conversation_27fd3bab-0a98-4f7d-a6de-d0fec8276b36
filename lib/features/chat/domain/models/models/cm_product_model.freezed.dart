// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'cm_product_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CMProductModel _$CMProductModelFromJson(Map<String, dynamic> json) {
  return _CMProductModel.fromJson(json);
}

/// @nodoc
mixin _$CMProductModel {
  String? get id => throw _privateConstructorUsedError;
  int? get mentorId => throw _privateConstructorUsedError;
  String? get productName => throw _privateConstructorUsedError;
  String? get applicationTime => throw _privateConstructorUsedError;
  String? get mentorName => throw _privateConstructorUsedError;
  double? get price => throw _privateConstructorUsedError;
  int? get commissionRatio => throw _privateConstructorUsedError;
  int? get productId => throw _privateConstructorUsedError;

  /// Serializes this CMProductModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CMProductModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CMProductModelCopyWith<CMProductModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CMProductModelCopyWith<$Res> {
  factory $CMProductModelCopyWith(
          CMProductModel value, $Res Function(CMProductModel) then) =
      _$CMProductModelCopyWithImpl<$Res, CMProductModel>;
  @useResult
  $Res call(
      {String? id,
      int? mentorId,
      String? productName,
      String? applicationTime,
      String? mentorName,
      double? price,
      int? commissionRatio,
      int? productId});
}

/// @nodoc
class _$CMProductModelCopyWithImpl<$Res, $Val extends CMProductModel>
    implements $CMProductModelCopyWith<$Res> {
  _$CMProductModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CMProductModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? mentorId = freezed,
    Object? productName = freezed,
    Object? applicationTime = freezed,
    Object? mentorName = freezed,
    Object? price = freezed,
    Object? commissionRatio = freezed,
    Object? productId = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      mentorId: freezed == mentorId
          ? _value.mentorId
          : mentorId // ignore: cast_nullable_to_non_nullable
              as int?,
      productName: freezed == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String?,
      applicationTime: freezed == applicationTime
          ? _value.applicationTime
          : applicationTime // ignore: cast_nullable_to_non_nullable
              as String?,
      mentorName: freezed == mentorName
          ? _value.mentorName
          : mentorName // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      commissionRatio: freezed == commissionRatio
          ? _value.commissionRatio
          : commissionRatio // ignore: cast_nullable_to_non_nullable
              as int?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CMProductModelImplCopyWith<$Res>
    implements $CMProductModelCopyWith<$Res> {
  factory _$$CMProductModelImplCopyWith(_$CMProductModelImpl value,
          $Res Function(_$CMProductModelImpl) then) =
      __$$CMProductModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      int? mentorId,
      String? productName,
      String? applicationTime,
      String? mentorName,
      double? price,
      int? commissionRatio,
      int? productId});
}

/// @nodoc
class __$$CMProductModelImplCopyWithImpl<$Res>
    extends _$CMProductModelCopyWithImpl<$Res, _$CMProductModelImpl>
    implements _$$CMProductModelImplCopyWith<$Res> {
  __$$CMProductModelImplCopyWithImpl(
      _$CMProductModelImpl _value, $Res Function(_$CMProductModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of CMProductModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? mentorId = freezed,
    Object? productName = freezed,
    Object? applicationTime = freezed,
    Object? mentorName = freezed,
    Object? price = freezed,
    Object? commissionRatio = freezed,
    Object? productId = freezed,
  }) {
    return _then(_$CMProductModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      mentorId: freezed == mentorId
          ? _value.mentorId
          : mentorId // ignore: cast_nullable_to_non_nullable
              as int?,
      productName: freezed == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String?,
      applicationTime: freezed == applicationTime
          ? _value.applicationTime
          : applicationTime // ignore: cast_nullable_to_non_nullable
              as String?,
      mentorName: freezed == mentorName
          ? _value.mentorName
          : mentorName // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      commissionRatio: freezed == commissionRatio
          ? _value.commissionRatio
          : commissionRatio // ignore: cast_nullable_to_non_nullable
              as int?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CMProductModelImpl implements _CMProductModel {
  const _$CMProductModelImpl(
      {this.id,
      this.mentorId,
      this.productName,
      this.applicationTime,
      this.mentorName,
      this.price,
      this.commissionRatio,
      this.productId});

  factory _$CMProductModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$CMProductModelImplFromJson(json);

  @override
  final String? id;
  @override
  final int? mentorId;
  @override
  final String? productName;
  @override
  final String? applicationTime;
  @override
  final String? mentorName;
  @override
  final double? price;
  @override
  final int? commissionRatio;
  @override
  final int? productId;

  @override
  String toString() {
    return 'CMProductModel(id: $id, mentorId: $mentorId, productName: $productName, applicationTime: $applicationTime, mentorName: $mentorName, price: $price, commissionRatio: $commissionRatio, productId: $productId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CMProductModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.mentorId, mentorId) ||
                other.mentorId == mentorId) &&
            (identical(other.productName, productName) ||
                other.productName == productName) &&
            (identical(other.applicationTime, applicationTime) ||
                other.applicationTime == applicationTime) &&
            (identical(other.mentorName, mentorName) ||
                other.mentorName == mentorName) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.commissionRatio, commissionRatio) ||
                other.commissionRatio == commissionRatio) &&
            (identical(other.productId, productId) ||
                other.productId == productId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, mentorId, productName,
      applicationTime, mentorName, price, commissionRatio, productId);

  /// Create a copy of CMProductModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CMProductModelImplCopyWith<_$CMProductModelImpl> get copyWith =>
      __$$CMProductModelImplCopyWithImpl<_$CMProductModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CMProductModelImplToJson(
      this,
    );
  }
}

abstract class _CMProductModel implements CMProductModel {
  const factory _CMProductModel(
      {final String? id,
      final int? mentorId,
      final String? productName,
      final String? applicationTime,
      final String? mentorName,
      final double? price,
      final int? commissionRatio,
      final int? productId}) = _$CMProductModelImpl;

  factory _CMProductModel.fromJson(Map<String, dynamic> json) =
      _$CMProductModelImpl.fromJson;

  @override
  String? get id;
  @override
  int? get mentorId;
  @override
  String? get productName;
  @override
  String? get applicationTime;
  @override
  String? get mentorName;
  @override
  double? get price;
  @override
  int? get commissionRatio;
  @override
  int? get productId;

  /// Create a copy of CMProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CMProductModelImplCopyWith<_$CMProductModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
