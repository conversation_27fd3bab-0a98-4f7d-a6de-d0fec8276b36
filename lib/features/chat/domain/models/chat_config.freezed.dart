// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chat_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ChatConfig _$ChatConfigFromJson(Map<String, dynamic> json) {
  return _ChatConfig.fromJson(json);
}

/// @nodoc
mixin _$ChatConfig {
  String get imImage => throw _privateConstructorUsedError;
  String get nickName => throw _privateConstructorUsedError;
  String get sdkAppId => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get userSig => throw _privateConstructorUsedError;

  /// Serializes this ChatConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChatConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChatConfigCopyWith<ChatConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChatConfigCopyWith<$Res> {
  factory $ChatConfigCopyWith(
          ChatConfig value, $Res Function(ChatConfig) then) =
      _$ChatConfigCopyWithImpl<$Res, ChatConfig>;
  @useResult
  $Res call(
      {String imImage,
      String nickName,
      String sdkAppId,
      String userId,
      String userSig});
}

/// @nodoc
class _$ChatConfigCopyWithImpl<$Res, $Val extends ChatConfig>
    implements $ChatConfigCopyWith<$Res> {
  _$ChatConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChatConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? imImage = null,
    Object? nickName = null,
    Object? sdkAppId = null,
    Object? userId = null,
    Object? userSig = null,
  }) {
    return _then(_value.copyWith(
      imImage: null == imImage
          ? _value.imImage
          : imImage // ignore: cast_nullable_to_non_nullable
              as String,
      nickName: null == nickName
          ? _value.nickName
          : nickName // ignore: cast_nullable_to_non_nullable
              as String,
      sdkAppId: null == sdkAppId
          ? _value.sdkAppId
          : sdkAppId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      userSig: null == userSig
          ? _value.userSig
          : userSig // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ChatConfigImplCopyWith<$Res>
    implements $ChatConfigCopyWith<$Res> {
  factory _$$ChatConfigImplCopyWith(
          _$ChatConfigImpl value, $Res Function(_$ChatConfigImpl) then) =
      __$$ChatConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String imImage,
      String nickName,
      String sdkAppId,
      String userId,
      String userSig});
}

/// @nodoc
class __$$ChatConfigImplCopyWithImpl<$Res>
    extends _$ChatConfigCopyWithImpl<$Res, _$ChatConfigImpl>
    implements _$$ChatConfigImplCopyWith<$Res> {
  __$$ChatConfigImplCopyWithImpl(
      _$ChatConfigImpl _value, $Res Function(_$ChatConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of ChatConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? imImage = null,
    Object? nickName = null,
    Object? sdkAppId = null,
    Object? userId = null,
    Object? userSig = null,
  }) {
    return _then(_$ChatConfigImpl(
      imImage: null == imImage
          ? _value.imImage
          : imImage // ignore: cast_nullable_to_non_nullable
              as String,
      nickName: null == nickName
          ? _value.nickName
          : nickName // ignore: cast_nullable_to_non_nullable
              as String,
      sdkAppId: null == sdkAppId
          ? _value.sdkAppId
          : sdkAppId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      userSig: null == userSig
          ? _value.userSig
          : userSig // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ChatConfigImpl implements _ChatConfig {
  const _$ChatConfigImpl(
      {this.imImage = '',
      this.nickName = '',
      this.sdkAppId = '',
      this.userId = '',
      this.userSig = ''});

  factory _$ChatConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChatConfigImplFromJson(json);

  @override
  @JsonKey()
  final String imImage;
  @override
  @JsonKey()
  final String nickName;
  @override
  @JsonKey()
  final String sdkAppId;
  @override
  @JsonKey()
  final String userId;
  @override
  @JsonKey()
  final String userSig;

  @override
  String toString() {
    return 'ChatConfig(imImage: $imImage, nickName: $nickName, sdkAppId: $sdkAppId, userId: $userId, userSig: $userSig)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChatConfigImpl &&
            (identical(other.imImage, imImage) || other.imImage == imImage) &&
            (identical(other.nickName, nickName) ||
                other.nickName == nickName) &&
            (identical(other.sdkAppId, sdkAppId) ||
                other.sdkAppId == sdkAppId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userSig, userSig) || other.userSig == userSig));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, imImage, nickName, sdkAppId, userId, userSig);

  /// Create a copy of ChatConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChatConfigImplCopyWith<_$ChatConfigImpl> get copyWith =>
      __$$ChatConfigImplCopyWithImpl<_$ChatConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChatConfigImplToJson(
      this,
    );
  }
}

abstract class _ChatConfig implements ChatConfig {
  const factory _ChatConfig(
      {final String imImage,
      final String nickName,
      final String sdkAppId,
      final String userId,
      final String userSig}) = _$ChatConfigImpl;

  factory _ChatConfig.fromJson(Map<String, dynamic> json) =
      _$ChatConfigImpl.fromJson;

  @override
  String get imImage;
  @override
  String get nickName;
  @override
  String get sdkAppId;
  @override
  String get userId;
  @override
  String get userSig;

  /// Create a copy of ChatConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChatConfigImplCopyWith<_$ChatConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
