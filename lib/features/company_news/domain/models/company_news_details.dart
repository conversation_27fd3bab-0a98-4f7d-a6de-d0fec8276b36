import 'package:freezed_annotation/freezed_annotation.dart';

part 'company_news_details.freezed.dart';
part 'company_news_details.g.dart';

@freezed
class CompanyNewsDetailsResponse with _$CompanyNewsDetailsResponse {
  const factory CompanyNewsDetailsResponse({
    int? code,
    CompanyNewsDetailsData? data,
    String? msg,
  }) = _CompanyNewsDetailsResponse;

  factory CompanyNewsDetailsResponse.fromJson(Map<String, dynamic> json) => _$CompanyNewsDetailsResponseFromJson(json);
}

@freezed
class CompanyNewsDetailsData with _$CompanyNewsDetailsData {
  const factory CompanyNewsDetailsData({
    @JsonKey(name: 'article_title') String? articleTitle,
    @JsonKey(name: 'article_market') String? articleMarket,
    @JsonKey(name: 'article_symbol') String? articleSymbol,
    @JsonKey(name: 'article_auth') String? articleAuth,
    @JsonKey(name: 'article_content') String? articleContent,
    String? id,
    @<PERSON>sonKey(name: 'article_date') String? articleDate,
  }) = _CompanyNewsDetailsData;

  factory CompanyNewsDetailsData.fromJson(Map<String, dynamic> json) => _$CompanyNewsDetailsDataFromJson(json);
}
