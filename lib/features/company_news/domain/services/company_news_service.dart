import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/api/network/endpoint/api_endpoints.dart';
import '../../../../core/api/network/models/result.dart';
import '../../../../core/api/network/network.dart';
import '../../../../core/api/network/network_helper.dart';
import '../models/company_news_details.dart';
import '../repository/company_news_repository.dart';

@Injectable(as: CompanyNewsRepository)
class CompanyNewsService implements CompanyNewsRepository {
  @override
  Future<ResponseResult<CompanyNewsDetailsResponse>> getCompanyNewsDetails(String id) async {
    try {
      final response = await NetworkProvider().get(
        ApiEndpoints.companyNewsInfo,
        queryParameters: {'id': id},
        isAuthRequired: false,
      );

      final responseModel = NetworkHelper.mappingResponseData<CompanyNewsDetailsResponse>(response);

      return ResponseResult(
        data: responseModel.data,
        error: responseModel.data == null ? responseModel.msg : null,
      );
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
