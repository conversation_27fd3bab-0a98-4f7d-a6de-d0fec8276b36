import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:injectable/injectable.dart';

import '../../domain/models/notification/notification_model.dart';
import '../../domain/services/notifications_service.dart';

part 'notifications_state.dart';

@singleton
class NotificationsCubit extends Cubit<NotificationsState> {
  NotificationsCubit() : super(NotificationsState());

  Future<void> getNotificationList({
    required String messageType,
    bool isLoadMore = false,
  }) async {
    if (!isLoadMore) {
      emit(state.copyWith(notificationFetchStatus: DataStatus.loading));
    }

    final int page = isLoadMore ? (state.notificationData?.current ?? 0) + 1 : 1;

    try {
      final result = await NotificationsService.getNotificationList(messageType: messageType, page: page);

      if (!result.isSuccess) {
        emit(state.copyWith(
          notificationFetchStatus: DataStatus.failed,
          error: result.error,
        ));
        return;
      }

      final currentRecords = state.notificationData?.records ?? [];
      final newRecords = result.data?.data?.records ?? [];

      final updatedRecords = isLoadMore ? [...currentRecords, ...newRecords] : newRecords;

      emit(state.copyWith(
        notificationFetchStatus: DataStatus.success,
        notificationData: NotificationData()
          ..records = updatedRecords
          ..current = result.data?.data?.current
          ..total = result.data?.data?.total
          ..pages = result.data?.data?.pages
          ..size = result.data?.data?.size
          ..optimizeCountSql = result.data?.data?.optimizeCountSql
          ..searchCount = result.data?.data?.searchCount
          ..orders = result.data?.data?.orders,
      ));
      getNotificationCount();
    } on Exception catch (e) {
      emit(state.copyWith(
        notificationFetchStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  Future<void> getNotificationCount() async {
    try {
      final result = await NotificationsService.getNotificationCount();
      if (result.isSuccess) {
        // Ensure count is never negative
        final count = (result.data ?? 0) < 0 ? 0 : result.data;
        emit(state.copyWith(notificationCount: count));
      } else {
        emit(state.copyWith(error: result.error));
      }
    } on Exception catch (e) {
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> readNotification({required int messageId}) async {
    try {
      emit(state.copyWith(notificationReadStatus: DataStatus.loading));

      // Find the notification in current records
      final records = state.notificationData?.records ?? [];
      final notificationIndex = records.indexWhere((n) => n.id == messageId);

      // Only proceed if notification exists and is unread (status != 1)
      if (notificationIndex != -1 && records[notificationIndex].status != 1) {
        final result = await NotificationsService.readNotification(messageId: messageId);

        if (result.isSuccess) {
          // Create updated notification with read status
          final updatedNotification = records[notificationIndex].copyWith(status: 1);

          // Create new records list with updated notification
          final updatedRecords = List<NotificationRecord>.from(records);
          updatedRecords[notificationIndex] = updatedNotification;

          // Only decrement count if the notification was unread
          final newCount = (state.notificationCount ?? 1) - 1;

          emit(state.copyWith(
            notificationCount: newCount < 0 ? 0 : newCount,
            notificationReadStatus: DataStatus.success,
            notificationData: state.notificationData?.copyWith(
              records: updatedRecords,
            ),
          ));

          // Refresh notification count to ensure accuracy
          await getNotificationCount();
        } else {
          emit(state.copyWith(
            notificationReadStatus: DataStatus.failed,
            error: result.error,
          ));
        }
      } else {
        // If notification is already read, just update status
        emit(state.copyWith(notificationReadStatus: DataStatus.success));
      }
    } on Exception catch (e) {
      emit(state.copyWith(
        notificationReadStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }
}
