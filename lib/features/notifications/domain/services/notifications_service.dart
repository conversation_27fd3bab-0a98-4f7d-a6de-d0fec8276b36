import 'package:dio/dio.dart';

import '../../../../core/api/network/endpoint/api_endpoints.dart';
import '../../../../core/api/network/models/result.dart';
import '../../../../core/api/network/network.dart';
import '../../../../core/api/network/network_helper.dart';
import '../models/notification/notification_model.dart';

/// Provides static methods for notification operations
class NotificationsService {
  /// Static method to get notification list
  static Future<ResponseResult<NotificationModel>> getNotificationList({
    required String messageType,
    required int page,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.notificationPage,
        isAuthRequired: true,
        queryParameters: {
          'type': messageType,
          'pageNumber': page,
          'pageSize': 20,
        },
      );

      final responseModel = NetworkHelper.mappingResponseData<NotificationModel>(response);

      return ResponseResult(
        data: responseModel.data,
        error: responseModel.data == null ? responseModel.msg : null,
      );
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Static method to get notification count
  static Future<ResponseResult<int>> getNotificationCount() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.notificationCount,
        isAuthRequired: true,
      );

      final responseModel = NetworkHelper.mappingResponseData<int>(response);

      return ResponseResult(
        data: responseModel.data,
        error: responseModel.data == null ? responseModel.msg : null,
      );
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Static method to mark notification as read
  static Future<ResponseResult<bool>> readNotification({
    required messageId,
  }) async {
    try {
      final Response response = await NetworkProvider().put(
        ApiEndpoints.notificationRead,
        isAuthRequired: true,
        data: {'id': messageId},
      );

      final responseModel = NetworkHelper.mappingResponseData<bool>(response);

      return ResponseResult(
        data: responseModel.data != null ? true : null,
        error: responseModel.data == null ? responseModel.msg : null,
      );
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
