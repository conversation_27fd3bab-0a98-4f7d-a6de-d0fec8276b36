import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/notification_model.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/notification_model.g.dart';

@JsonSerializable()
class NotificationModel {
  int? code;
  String? msg;
  NotificationData? data;

  NotificationModel();

  factory NotificationModel.fromJson(Map<String, dynamic> json) => $NotificationModelFromJson(json);

  Map<String, dynamic> toJson() => $NotificationModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class NotificationData {
  int? current;
  bool? optimizeCountSql;
  List<dynamic>? orders;
  int? pages;
  List<NotificationRecord>? records;
  bool? searchCount;
  int? size;
  int? total;

  NotificationData();

  factory NotificationData.fromJson(Map<String, dynamic> json) => $NotificationDataFromJson(json);

  Map<String, dynamic> toJson() => $NotificationDataToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class NotificationRecord {
  String? content;
  int? contentType;
  String? extra;
  int? id;
  int? isNeedPush;
  String? messageNo;
  int? messageType;
  int? pushStatus;
  int? status;
  String? title;
  String? type;
  int? userId;

  NotificationRecord();

  factory NotificationRecord.fromJson(Map<String, dynamic> json) => $NotificationRecordFromJson(json);

  Map<String, dynamic> toJson() => $NotificationRecordToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
