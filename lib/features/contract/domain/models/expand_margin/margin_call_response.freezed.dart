// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'margin_call_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MarginCallResponse _$MarginCallResponseFromJson(Map<String, dynamic> json) {
  return _MarginCallResponse.fromJson(json);
}

/// @nodoc
mixin _$MarginCallResponse {
  @JsonKey(name: 'code')
  int? get code => throw _privateConstructorUsedError;
  @JsonKey(name: 'data')
  MarginCallData? get data => throw _privateConstructorUsedError;
  @JsonKey(name: 'msg')
  String? get msg => throw _privateConstructorUsedError;
  @JsonKey(name: 'sign')
  String? get sign => throw _privateConstructorUsedError;

  /// Serializes this MarginCallResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MarginCallResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MarginCallResponseCopyWith<MarginCallResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MarginCallResponseCopyWith<$Res> {
  factory $MarginCallResponseCopyWith(
          MarginCallResponse value, $Res Function(MarginCallResponse) then) =
      _$MarginCallResponseCopyWithImpl<$Res, MarginCallResponse>;
  @useResult
  $Res call(
      {@JsonKey(name: 'code') int? code,
      @JsonKey(name: 'data') MarginCallData? data,
      @JsonKey(name: 'msg') String? msg,
      @JsonKey(name: 'sign') String? sign});

  $MarginCallDataCopyWith<$Res>? get data;
}

/// @nodoc
class _$MarginCallResponseCopyWithImpl<$Res, $Val extends MarginCallResponse>
    implements $MarginCallResponseCopyWith<$Res> {
  _$MarginCallResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MarginCallResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
    Object? sign = freezed,
  }) {
    return _then(_value.copyWith(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as MarginCallData?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
      sign: freezed == sign
          ? _value.sign
          : sign // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of MarginCallResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MarginCallDataCopyWith<$Res>? get data {
    if (_value.data == null) {
      return null;
    }

    return $MarginCallDataCopyWith<$Res>(_value.data!, (value) {
      return _then(_value.copyWith(data: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MarginCallResponseImplCopyWith<$Res>
    implements $MarginCallResponseCopyWith<$Res> {
  factory _$$MarginCallResponseImplCopyWith(_$MarginCallResponseImpl value,
          $Res Function(_$MarginCallResponseImpl) then) =
      __$$MarginCallResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'code') int? code,
      @JsonKey(name: 'data') MarginCallData? data,
      @JsonKey(name: 'msg') String? msg,
      @JsonKey(name: 'sign') String? sign});

  @override
  $MarginCallDataCopyWith<$Res>? get data;
}

/// @nodoc
class __$$MarginCallResponseImplCopyWithImpl<$Res>
    extends _$MarginCallResponseCopyWithImpl<$Res, _$MarginCallResponseImpl>
    implements _$$MarginCallResponseImplCopyWith<$Res> {
  __$$MarginCallResponseImplCopyWithImpl(_$MarginCallResponseImpl _value,
      $Res Function(_$MarginCallResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of MarginCallResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
    Object? sign = freezed,
  }) {
    return _then(_$MarginCallResponseImpl(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as MarginCallData?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
      sign: freezed == sign
          ? _value.sign
          : sign // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MarginCallResponseImpl implements _MarginCallResponse {
  const _$MarginCallResponseImpl(
      {@JsonKey(name: 'code') this.code,
      @JsonKey(name: 'data') this.data,
      @JsonKey(name: 'msg') this.msg,
      @JsonKey(name: 'sign') this.sign});

  factory _$MarginCallResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$MarginCallResponseImplFromJson(json);

  @override
  @JsonKey(name: 'code')
  final int? code;
  @override
  @JsonKey(name: 'data')
  final MarginCallData? data;
  @override
  @JsonKey(name: 'msg')
  final String? msg;
  @override
  @JsonKey(name: 'sign')
  final String? sign;

  @override
  String toString() {
    return 'MarginCallResponse(code: $code, data: $data, msg: $msg, sign: $sign)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MarginCallResponseImpl &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.msg, msg) || other.msg == msg) &&
            (identical(other.sign, sign) || other.sign == sign));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, code, data, msg, sign);

  /// Create a copy of MarginCallResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MarginCallResponseImplCopyWith<_$MarginCallResponseImpl> get copyWith =>
      __$$MarginCallResponseImplCopyWithImpl<_$MarginCallResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MarginCallResponseImplToJson(
      this,
    );
  }
}

abstract class _MarginCallResponse implements MarginCallResponse {
  const factory _MarginCallResponse(
      {@JsonKey(name: 'code') final int? code,
      @JsonKey(name: 'data') final MarginCallData? data,
      @JsonKey(name: 'msg') final String? msg,
      @JsonKey(name: 'sign') final String? sign}) = _$MarginCallResponseImpl;

  factory _MarginCallResponse.fromJson(Map<String, dynamic> json) =
      _$MarginCallResponseImpl.fromJson;

  @override
  @JsonKey(name: 'code')
  int? get code;
  @override
  @JsonKey(name: 'data')
  MarginCallData? get data;
  @override
  @JsonKey(name: 'msg')
  String? get msg;
  @override
  @JsonKey(name: 'sign')
  String? get sign;

  /// Create a copy of MarginCallResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MarginCallResponseImplCopyWith<_$MarginCallResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

MarginCallData _$MarginCallDataFromJson(Map<String, dynamic> json) {
  return _MarginCallData.fromJson(json);
}

/// @nodoc
mixin _$MarginCallData {
  @JsonKey(name: 'amountList')
  List<MarginCallAmount>? get amountList => throw _privateConstructorUsedError;
  @JsonKey(name: 'bonusAmountList')
  List<double>? get bonusAmountList => throw _privateConstructorUsedError;
  @JsonKey(name: 'closeAmount')
  double? get closeAmount => throw _privateConstructorUsedError;
  @JsonKey(name: 'closeValue')
  double? get closeValue => throw _privateConstructorUsedError;
  @JsonKey(name: 'giveDay')
  int? get giveDay => throw _privateConstructorUsedError;
  @JsonKey(name: 'id')
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'initCash')
  double? get initCash => throw _privateConstructorUsedError;
  @JsonKey(name: 'interestRate')
  double? get interestRate => throw _privateConstructorUsedError;
  @JsonKey(name: 'marketType')
  String? get marketType => throw _privateConstructorUsedError;
  @JsonKey(name: 'multiple')
  int? get multiple => throw _privateConstructorUsedError;
  @JsonKey(name: 'periodType')
  int? get periodType => throw _privateConstructorUsedError;
  @JsonKey(name: 'totalCash')
  double? get totalCash => throw _privateConstructorUsedError;
  @JsonKey(name: 'totalFinance')
  double? get totalFinance => throw _privateConstructorUsedError;
  @JsonKey(name: 'totalPower')
  double? get totalPower => throw _privateConstructorUsedError;
  @JsonKey(name: 'type')
  int? get type => throw _privateConstructorUsedError;
  @JsonKey(name: 'warnAmount')
  double? get warnAmount => throw _privateConstructorUsedError;
  @JsonKey(name: 'warnValue')
  double? get warnValue => throw _privateConstructorUsedError;

  /// Serializes this MarginCallData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MarginCallData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MarginCallDataCopyWith<MarginCallData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MarginCallDataCopyWith<$Res> {
  factory $MarginCallDataCopyWith(
          MarginCallData value, $Res Function(MarginCallData) then) =
      _$MarginCallDataCopyWithImpl<$Res, MarginCallData>;
  @useResult
  $Res call(
      {@JsonKey(name: 'amountList') List<MarginCallAmount>? amountList,
      @JsonKey(name: 'bonusAmountList') List<double>? bonusAmountList,
      @JsonKey(name: 'closeAmount') double? closeAmount,
      @JsonKey(name: 'closeValue') double? closeValue,
      @JsonKey(name: 'giveDay') int? giveDay,
      @JsonKey(name: 'id') int? id,
      @JsonKey(name: 'initCash') double? initCash,
      @JsonKey(name: 'interestRate') double? interestRate,
      @JsonKey(name: 'marketType') String? marketType,
      @JsonKey(name: 'multiple') int? multiple,
      @JsonKey(name: 'periodType') int? periodType,
      @JsonKey(name: 'totalCash') double? totalCash,
      @JsonKey(name: 'totalFinance') double? totalFinance,
      @JsonKey(name: 'totalPower') double? totalPower,
      @JsonKey(name: 'type') int? type,
      @JsonKey(name: 'warnAmount') double? warnAmount,
      @JsonKey(name: 'warnValue') double? warnValue});
}

/// @nodoc
class _$MarginCallDataCopyWithImpl<$Res, $Val extends MarginCallData>
    implements $MarginCallDataCopyWith<$Res> {
  _$MarginCallDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MarginCallData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amountList = freezed,
    Object? bonusAmountList = freezed,
    Object? closeAmount = freezed,
    Object? closeValue = freezed,
    Object? giveDay = freezed,
    Object? id = freezed,
    Object? initCash = freezed,
    Object? interestRate = freezed,
    Object? marketType = freezed,
    Object? multiple = freezed,
    Object? periodType = freezed,
    Object? totalCash = freezed,
    Object? totalFinance = freezed,
    Object? totalPower = freezed,
    Object? type = freezed,
    Object? warnAmount = freezed,
    Object? warnValue = freezed,
  }) {
    return _then(_value.copyWith(
      amountList: freezed == amountList
          ? _value.amountList
          : amountList // ignore: cast_nullable_to_non_nullable
              as List<MarginCallAmount>?,
      bonusAmountList: freezed == bonusAmountList
          ? _value.bonusAmountList
          : bonusAmountList // ignore: cast_nullable_to_non_nullable
              as List<double>?,
      closeAmount: freezed == closeAmount
          ? _value.closeAmount
          : closeAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      closeValue: freezed == closeValue
          ? _value.closeValue
          : closeValue // ignore: cast_nullable_to_non_nullable
              as double?,
      giveDay: freezed == giveDay
          ? _value.giveDay
          : giveDay // ignore: cast_nullable_to_non_nullable
              as int?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      initCash: freezed == initCash
          ? _value.initCash
          : initCash // ignore: cast_nullable_to_non_nullable
              as double?,
      interestRate: freezed == interestRate
          ? _value.interestRate
          : interestRate // ignore: cast_nullable_to_non_nullable
              as double?,
      marketType: freezed == marketType
          ? _value.marketType
          : marketType // ignore: cast_nullable_to_non_nullable
              as String?,
      multiple: freezed == multiple
          ? _value.multiple
          : multiple // ignore: cast_nullable_to_non_nullable
              as int?,
      periodType: freezed == periodType
          ? _value.periodType
          : periodType // ignore: cast_nullable_to_non_nullable
              as int?,
      totalCash: freezed == totalCash
          ? _value.totalCash
          : totalCash // ignore: cast_nullable_to_non_nullable
              as double?,
      totalFinance: freezed == totalFinance
          ? _value.totalFinance
          : totalFinance // ignore: cast_nullable_to_non_nullable
              as double?,
      totalPower: freezed == totalPower
          ? _value.totalPower
          : totalPower // ignore: cast_nullable_to_non_nullable
              as double?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      warnAmount: freezed == warnAmount
          ? _value.warnAmount
          : warnAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      warnValue: freezed == warnValue
          ? _value.warnValue
          : warnValue // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MarginCallDataImplCopyWith<$Res>
    implements $MarginCallDataCopyWith<$Res> {
  factory _$$MarginCallDataImplCopyWith(_$MarginCallDataImpl value,
          $Res Function(_$MarginCallDataImpl) then) =
      __$$MarginCallDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'amountList') List<MarginCallAmount>? amountList,
      @JsonKey(name: 'bonusAmountList') List<double>? bonusAmountList,
      @JsonKey(name: 'closeAmount') double? closeAmount,
      @JsonKey(name: 'closeValue') double? closeValue,
      @JsonKey(name: 'giveDay') int? giveDay,
      @JsonKey(name: 'id') int? id,
      @JsonKey(name: 'initCash') double? initCash,
      @JsonKey(name: 'interestRate') double? interestRate,
      @JsonKey(name: 'marketType') String? marketType,
      @JsonKey(name: 'multiple') int? multiple,
      @JsonKey(name: 'periodType') int? periodType,
      @JsonKey(name: 'totalCash') double? totalCash,
      @JsonKey(name: 'totalFinance') double? totalFinance,
      @JsonKey(name: 'totalPower') double? totalPower,
      @JsonKey(name: 'type') int? type,
      @JsonKey(name: 'warnAmount') double? warnAmount,
      @JsonKey(name: 'warnValue') double? warnValue});
}

/// @nodoc
class __$$MarginCallDataImplCopyWithImpl<$Res>
    extends _$MarginCallDataCopyWithImpl<$Res, _$MarginCallDataImpl>
    implements _$$MarginCallDataImplCopyWith<$Res> {
  __$$MarginCallDataImplCopyWithImpl(
      _$MarginCallDataImpl _value, $Res Function(_$MarginCallDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of MarginCallData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amountList = freezed,
    Object? bonusAmountList = freezed,
    Object? closeAmount = freezed,
    Object? closeValue = freezed,
    Object? giveDay = freezed,
    Object? id = freezed,
    Object? initCash = freezed,
    Object? interestRate = freezed,
    Object? marketType = freezed,
    Object? multiple = freezed,
    Object? periodType = freezed,
    Object? totalCash = freezed,
    Object? totalFinance = freezed,
    Object? totalPower = freezed,
    Object? type = freezed,
    Object? warnAmount = freezed,
    Object? warnValue = freezed,
  }) {
    return _then(_$MarginCallDataImpl(
      amountList: freezed == amountList
          ? _value._amountList
          : amountList // ignore: cast_nullable_to_non_nullable
              as List<MarginCallAmount>?,
      bonusAmountList: freezed == bonusAmountList
          ? _value._bonusAmountList
          : bonusAmountList // ignore: cast_nullable_to_non_nullable
              as List<double>?,
      closeAmount: freezed == closeAmount
          ? _value.closeAmount
          : closeAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      closeValue: freezed == closeValue
          ? _value.closeValue
          : closeValue // ignore: cast_nullable_to_non_nullable
              as double?,
      giveDay: freezed == giveDay
          ? _value.giveDay
          : giveDay // ignore: cast_nullable_to_non_nullable
              as int?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      initCash: freezed == initCash
          ? _value.initCash
          : initCash // ignore: cast_nullable_to_non_nullable
              as double?,
      interestRate: freezed == interestRate
          ? _value.interestRate
          : interestRate // ignore: cast_nullable_to_non_nullable
              as double?,
      marketType: freezed == marketType
          ? _value.marketType
          : marketType // ignore: cast_nullable_to_non_nullable
              as String?,
      multiple: freezed == multiple
          ? _value.multiple
          : multiple // ignore: cast_nullable_to_non_nullable
              as int?,
      periodType: freezed == periodType
          ? _value.periodType
          : periodType // ignore: cast_nullable_to_non_nullable
              as int?,
      totalCash: freezed == totalCash
          ? _value.totalCash
          : totalCash // ignore: cast_nullable_to_non_nullable
              as double?,
      totalFinance: freezed == totalFinance
          ? _value.totalFinance
          : totalFinance // ignore: cast_nullable_to_non_nullable
              as double?,
      totalPower: freezed == totalPower
          ? _value.totalPower
          : totalPower // ignore: cast_nullable_to_non_nullable
              as double?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      warnAmount: freezed == warnAmount
          ? _value.warnAmount
          : warnAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      warnValue: freezed == warnValue
          ? _value.warnValue
          : warnValue // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MarginCallDataImpl extends _MarginCallData {
  const _$MarginCallDataImpl(
      {@JsonKey(name: 'amountList') final List<MarginCallAmount>? amountList,
      @JsonKey(name: 'bonusAmountList') final List<double>? bonusAmountList,
      @JsonKey(name: 'closeAmount') this.closeAmount,
      @JsonKey(name: 'closeValue') this.closeValue,
      @JsonKey(name: 'giveDay') this.giveDay,
      @JsonKey(name: 'id') this.id,
      @JsonKey(name: 'initCash') this.initCash,
      @JsonKey(name: 'interestRate') this.interestRate,
      @JsonKey(name: 'marketType') this.marketType,
      @JsonKey(name: 'multiple') this.multiple,
      @JsonKey(name: 'periodType') this.periodType,
      @JsonKey(name: 'totalCash') this.totalCash,
      @JsonKey(name: 'totalFinance') this.totalFinance,
      @JsonKey(name: 'totalPower') this.totalPower,
      @JsonKey(name: 'type') this.type,
      @JsonKey(name: 'warnAmount') this.warnAmount,
      @JsonKey(name: 'warnValue') this.warnValue})
      : _amountList = amountList,
        _bonusAmountList = bonusAmountList,
        super._();

  factory _$MarginCallDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$MarginCallDataImplFromJson(json);

  final List<MarginCallAmount>? _amountList;
  @override
  @JsonKey(name: 'amountList')
  List<MarginCallAmount>? get amountList {
    final value = _amountList;
    if (value == null) return null;
    if (_amountList is EqualUnmodifiableListView) return _amountList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<double>? _bonusAmountList;
  @override
  @JsonKey(name: 'bonusAmountList')
  List<double>? get bonusAmountList {
    final value = _bonusAmountList;
    if (value == null) return null;
    if (_bonusAmountList is EqualUnmodifiableListView) return _bonusAmountList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: 'closeAmount')
  final double? closeAmount;
  @override
  @JsonKey(name: 'closeValue')
  final double? closeValue;
  @override
  @JsonKey(name: 'giveDay')
  final int? giveDay;
  @override
  @JsonKey(name: 'id')
  final int? id;
  @override
  @JsonKey(name: 'initCash')
  final double? initCash;
  @override
  @JsonKey(name: 'interestRate')
  final double? interestRate;
  @override
  @JsonKey(name: 'marketType')
  final String? marketType;
  @override
  @JsonKey(name: 'multiple')
  final int? multiple;
  @override
  @JsonKey(name: 'periodType')
  final int? periodType;
  @override
  @JsonKey(name: 'totalCash')
  final double? totalCash;
  @override
  @JsonKey(name: 'totalFinance')
  final double? totalFinance;
  @override
  @JsonKey(name: 'totalPower')
  final double? totalPower;
  @override
  @JsonKey(name: 'type')
  final int? type;
  @override
  @JsonKey(name: 'warnAmount')
  final double? warnAmount;
  @override
  @JsonKey(name: 'warnValue')
  final double? warnValue;

  @override
  String toString() {
    return 'MarginCallData(amountList: $amountList, bonusAmountList: $bonusAmountList, closeAmount: $closeAmount, closeValue: $closeValue, giveDay: $giveDay, id: $id, initCash: $initCash, interestRate: $interestRate, marketType: $marketType, multiple: $multiple, periodType: $periodType, totalCash: $totalCash, totalFinance: $totalFinance, totalPower: $totalPower, type: $type, warnAmount: $warnAmount, warnValue: $warnValue)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MarginCallDataImpl &&
            const DeepCollectionEquality()
                .equals(other._amountList, _amountList) &&
            const DeepCollectionEquality()
                .equals(other._bonusAmountList, _bonusAmountList) &&
            (identical(other.closeAmount, closeAmount) ||
                other.closeAmount == closeAmount) &&
            (identical(other.closeValue, closeValue) ||
                other.closeValue == closeValue) &&
            (identical(other.giveDay, giveDay) || other.giveDay == giveDay) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.initCash, initCash) ||
                other.initCash == initCash) &&
            (identical(other.interestRate, interestRate) ||
                other.interestRate == interestRate) &&
            (identical(other.marketType, marketType) ||
                other.marketType == marketType) &&
            (identical(other.multiple, multiple) ||
                other.multiple == multiple) &&
            (identical(other.periodType, periodType) ||
                other.periodType == periodType) &&
            (identical(other.totalCash, totalCash) ||
                other.totalCash == totalCash) &&
            (identical(other.totalFinance, totalFinance) ||
                other.totalFinance == totalFinance) &&
            (identical(other.totalPower, totalPower) ||
                other.totalPower == totalPower) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.warnAmount, warnAmount) ||
                other.warnAmount == warnAmount) &&
            (identical(other.warnValue, warnValue) ||
                other.warnValue == warnValue));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_amountList),
      const DeepCollectionEquality().hash(_bonusAmountList),
      closeAmount,
      closeValue,
      giveDay,
      id,
      initCash,
      interestRate,
      marketType,
      multiple,
      periodType,
      totalCash,
      totalFinance,
      totalPower,
      type,
      warnAmount,
      warnValue);

  /// Create a copy of MarginCallData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MarginCallDataImplCopyWith<_$MarginCallDataImpl> get copyWith =>
      __$$MarginCallDataImplCopyWithImpl<_$MarginCallDataImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MarginCallDataImplToJson(
      this,
    );
  }
}

abstract class _MarginCallData extends MarginCallData {
  const factory _MarginCallData(
      {@JsonKey(name: 'amountList') final List<MarginCallAmount>? amountList,
      @JsonKey(name: 'bonusAmountList') final List<double>? bonusAmountList,
      @JsonKey(name: 'closeAmount') final double? closeAmount,
      @JsonKey(name: 'closeValue') final double? closeValue,
      @JsonKey(name: 'giveDay') final int? giveDay,
      @JsonKey(name: 'id') final int? id,
      @JsonKey(name: 'initCash') final double? initCash,
      @JsonKey(name: 'interestRate') final double? interestRate,
      @JsonKey(name: 'marketType') final String? marketType,
      @JsonKey(name: 'multiple') final int? multiple,
      @JsonKey(name: 'periodType') final int? periodType,
      @JsonKey(name: 'totalCash') final double? totalCash,
      @JsonKey(name: 'totalFinance') final double? totalFinance,
      @JsonKey(name: 'totalPower') final double? totalPower,
      @JsonKey(name: 'type') final int? type,
      @JsonKey(name: 'warnAmount') final double? warnAmount,
      @JsonKey(name: 'warnValue')
      final double? warnValue}) = _$MarginCallDataImpl;
  const _MarginCallData._() : super._();

  factory _MarginCallData.fromJson(Map<String, dynamic> json) =
      _$MarginCallDataImpl.fromJson;

  @override
  @JsonKey(name: 'amountList')
  List<MarginCallAmount>? get amountList;
  @override
  @JsonKey(name: 'bonusAmountList')
  List<double>? get bonusAmountList;
  @override
  @JsonKey(name: 'closeAmount')
  double? get closeAmount;
  @override
  @JsonKey(name: 'closeValue')
  double? get closeValue;
  @override
  @JsonKey(name: 'giveDay')
  int? get giveDay;
  @override
  @JsonKey(name: 'id')
  int? get id;
  @override
  @JsonKey(name: 'initCash')
  double? get initCash;
  @override
  @JsonKey(name: 'interestRate')
  double? get interestRate;
  @override
  @JsonKey(name: 'marketType')
  String? get marketType;
  @override
  @JsonKey(name: 'multiple')
  int? get multiple;
  @override
  @JsonKey(name: 'periodType')
  int? get periodType;
  @override
  @JsonKey(name: 'totalCash')
  double? get totalCash;
  @override
  @JsonKey(name: 'totalFinance')
  double? get totalFinance;
  @override
  @JsonKey(name: 'totalPower')
  double? get totalPower;
  @override
  @JsonKey(name: 'type')
  int? get type;
  @override
  @JsonKey(name: 'warnAmount')
  double? get warnAmount;
  @override
  @JsonKey(name: 'warnValue')
  double? get warnValue;

  /// Create a copy of MarginCallData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MarginCallDataImplCopyWith<_$MarginCallDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

MarginCallAmount _$MarginCallAmountFromJson(Map<String, dynamic> json) {
  return _MarginCallAmount.fromJson(json);
}

/// @nodoc
mixin _$MarginCallAmount {
  @JsonKey(name: 'id')
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'applyAmount')
  double? get applyAmount => throw _privateConstructorUsedError;

  /// Serializes this MarginCallAmount to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MarginCallAmount
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MarginCallAmountCopyWith<MarginCallAmount> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MarginCallAmountCopyWith<$Res> {
  factory $MarginCallAmountCopyWith(
          MarginCallAmount value, $Res Function(MarginCallAmount) then) =
      _$MarginCallAmountCopyWithImpl<$Res, MarginCallAmount>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'applyAmount') double? applyAmount});
}

/// @nodoc
class _$MarginCallAmountCopyWithImpl<$Res, $Val extends MarginCallAmount>
    implements $MarginCallAmountCopyWith<$Res> {
  _$MarginCallAmountCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MarginCallAmount
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? applyAmount = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      applyAmount: freezed == applyAmount
          ? _value.applyAmount
          : applyAmount // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MarginCallAmountImplCopyWith<$Res>
    implements $MarginCallAmountCopyWith<$Res> {
  factory _$$MarginCallAmountImplCopyWith(_$MarginCallAmountImpl value,
          $Res Function(_$MarginCallAmountImpl) then) =
      __$$MarginCallAmountImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') int? id,
      @JsonKey(name: 'applyAmount') double? applyAmount});
}

/// @nodoc
class __$$MarginCallAmountImplCopyWithImpl<$Res>
    extends _$MarginCallAmountCopyWithImpl<$Res, _$MarginCallAmountImpl>
    implements _$$MarginCallAmountImplCopyWith<$Res> {
  __$$MarginCallAmountImplCopyWithImpl(_$MarginCallAmountImpl _value,
      $Res Function(_$MarginCallAmountImpl) _then)
      : super(_value, _then);

  /// Create a copy of MarginCallAmount
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? applyAmount = freezed,
  }) {
    return _then(_$MarginCallAmountImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      applyAmount: freezed == applyAmount
          ? _value.applyAmount
          : applyAmount // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MarginCallAmountImpl implements _MarginCallAmount {
  const _$MarginCallAmountImpl(
      {@JsonKey(name: 'id') this.id,
      @JsonKey(name: 'applyAmount') this.applyAmount});

  factory _$MarginCallAmountImpl.fromJson(Map<String, dynamic> json) =>
      _$$MarginCallAmountImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final int? id;
  @override
  @JsonKey(name: 'applyAmount')
  final double? applyAmount;

  @override
  String toString() {
    return 'MarginCallAmount(id: $id, applyAmount: $applyAmount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MarginCallAmountImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.applyAmount, applyAmount) ||
                other.applyAmount == applyAmount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, applyAmount);

  /// Create a copy of MarginCallAmount
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MarginCallAmountImplCopyWith<_$MarginCallAmountImpl> get copyWith =>
      __$$MarginCallAmountImplCopyWithImpl<_$MarginCallAmountImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MarginCallAmountImplToJson(
      this,
    );
  }
}

abstract class _MarginCallAmount implements MarginCallAmount {
  const factory _MarginCallAmount(
          {@JsonKey(name: 'id') final int? id,
          @JsonKey(name: 'applyAmount') final double? applyAmount}) =
      _$MarginCallAmountImpl;

  factory _MarginCallAmount.fromJson(Map<String, dynamic> json) =
      _$MarginCallAmountImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  int? get id;
  @override
  @JsonKey(name: 'applyAmount')
  double? get applyAmount;

  /// Create a copy of MarginCallAmount
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MarginCallAmountImplCopyWith<_$MarginCallAmountImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
