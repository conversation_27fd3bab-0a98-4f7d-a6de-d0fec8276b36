import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/constants/enums.dart';
import '../../domain/models/sign_up_request.dart';
import '../../domain/services/sign_up_service.dart';

part 'sign_up_state.dart';

@injectable
class SignUpCubit extends Cubit<SignUpState> {
  SignUpCubit() : super(SignUpState());

  Future<void> register({required SignUpRequest signUpRequest}) async {
    emit(
      state.copyWith(
        signUpFetchStatus: DataStatus.loading,
      ),
    );
    try {
      final result = await SignUpService.register(
        signUpRequest: signUpRequest,
      );
      if (result.isSuccess) {
        emit(state.copyWith(
          signUpFetchStatus: DataStatus.success,
        ));
      } else {
        emit(state.copyWith(signUpFetchStatus: DataStatus.failed, error: result.error));
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          signUpFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }
}
