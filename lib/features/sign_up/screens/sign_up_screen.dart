import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/api/network/network_helper.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/widgets/app_header.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';

import '../../../core/utils/validators.dart';
import '../../../shared/app/utilities/easy_loading.dart';
import '../../../shared/constants/enums.dart';
import '../../../shared/theme/my_color_scheme.dart';
import '../../account/logic/otp/otp_cubit.dart';
import '../../account/logic/otp/otp_state.dart';
import '../domain/models/sign_up_request.dart';
import '../logic/sign_up/sign_up_cubit.dart';
import '../widgets/sign_up_widget.dart';

class SignUpScreen extends StatelessWidget with AppHeaderMixin {
  const SignUpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          children: [
            buildAppHeader(context, height: .35.gsh, showHeaderImage: true),
            _SignUpForm(),
          ],
        ),
      ),
    );
  }
}

class _SignUpForm extends StatefulWidget {
  @override
  State<_SignUpForm> createState() => _SignUpFormState();
}

class _SignUpFormState extends State<_SignUpForm> {
  final TextEditingController mobileController = TextEditingController();
  final TextEditingController codeController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController = TextEditingController();
  final TextEditingController inviteCodeController = TextEditingController();

  // Add form key for validation
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  void _handleRegisterButtonPress(BuildContext context) {
    // Use form validation instead of toast messages
    if (!_formKey.currentState!.validate()) {
      return;
    }
    context.read<SignUpCubit>().register(
          signUpRequest: SignUpRequest()
            ..mobile = mobileController.text
            ..password = passwordController.text.toBase64()
            ..inviteCode = inviteCodeController.text
            ..smsCode = codeController.text,
        );
  }

  void _handleSendVerificationCode(BuildContext context) async {
    // Validate only the mobile field
    final mobileValidation = Validators.validateMobile(mobileController.text);
    if (mobileValidation != null) {
      _formKey.currentState?.validate();
      return;
    }
    context.read<OtpCubit>().sendOtp(mobileController.text, type: OtpType.register);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => OtpCubit(),
      child: MultiBlocListener(
        listeners: [
          BlocListener<OtpCubit, OtpState>(
            listenWhen: (previous, current) => previous.sendStatus != current.sendStatus,
            listener: (context, state) {
              if (state.sendStatus == DataStatus.loading) {
                GPEasyLoading.showLoading(message: 'sendingCode'.tr());
              } else if (state.sendStatus == DataStatus.success && state.isSent) {
                GPEasyLoading.showSuccess(message: 'verificationCodeSent'.tr());
              } else if (state.sendStatus == DataStatus.failed && state.error != null) {
                GPEasyLoading.showToast(state.error!.tr(), bgColor: Colors.red);
              }
            },
          ),
          BlocListener<SignUpCubit, SignUpState>(
            listenWhen: (previous, current) => previous.signUpFetchStatus != current.signUpFetchStatus,
            listener: (context, state) {
              if (state.signUpFetchStatus == DataStatus.success) {
                GPEasyLoading.showSuccess(message: 'registerSuccess'.tr());
                Navigator.of(context).pop({
                  'mobile': mobileController.text,
                  'password': passwordController.text,
                });
              }
              if (state.signUpFetchStatus == DataStatus.failed) {
                NetworkHelper.handleMessage(
                  state.error,
                  context,
                  type: HandleTypes.customDialog,
                  snackBarType: SnackBarType.error,
                  dialogKey: 'signup_error_dialog',
                );
              }
            },
          ),
        ],
        child: Builder(
          builder: (context) {
            return AnimationConfiguration.synchronized(
                duration: const Duration(milliseconds: 800),
                child: SlideAnimation(
                  verticalOffset: 100.0,
                  child: FadeInAnimation(
                    child: Container(
                      width: double.infinity,
                      height: 0.70.gsh,
                      decoration: BoxDecoration(
                        color: myColorScheme(context).cardColor,
                        borderRadius: BorderRadius.circular(40.gr),
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 30.gw),
                        child: SingleChildScrollView(
                          child: AnimationLimiter(
                            child: Form(
                              key: _formKey,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: AnimationConfiguration.toStaggeredList(
                                  duration: const Duration(milliseconds: 600),
                                  childAnimationBuilder: (widget) => SlideAnimation(
                                    verticalOffset: 50.0,
                                    child: FadeInAnimation(
                                      child: widget,
                                    ),
                                  ),
                                  children: [
                                    28.verticalSpace,
                                    Text(
                                      'registerAccount'.tr(),
                                      style: FontPalette.semiBold18.copyWith(
                                        color: myColorScheme(context).primaryColor,
                                      ),
                                    ),
                                    30.verticalSpace,
                                    BlocBuilder<OtpCubit, OtpState>(
                                      builder: (context, otpState) {
                                        return Column(
                                          children: [
                                            // Phone field with validation
                                            TextFieldWidget(
                                              controller: mobileController,
                                              hintText: 'loginPhonePlaceholder'.tr(),
                                              textInputType: TextInputType.phone,
                                              maxLength: 11,
                                              counterText: '',
                                              validator: (value) => Validators.validateMobile(value),
                                              prefixIcon: SvgPicture.asset(
                                                Assets.mobileIcon,
                                                fit: BoxFit.scaleDown,
                                                width: 18.gw,
                                                height: 18.gh,
                                              ),
                                            ),
                                            15.verticalSpace,
                                            // Verification code field with button
                                            Container(
                                              padding: EdgeInsets.zero,
                                              child: Row(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Expanded(
                                                    flex: 3,
                                                    child: SizedBox(
                                                      child: TextFieldWidget(
                                                        controller: codeController,
                                                        hintText: 'loginVerificationCodePlaceholder'.tr(),
                                                        textInputType: TextInputType.text,
                                                        contentPadding: EdgeInsets.zero,
                                                        validator: (value) =>
                                                            value == null || value.isEmpty ? 'incorrectOtp'.tr() : null,
                                                        prefixIcon: SvgPicture.asset(
                                                          Assets.shiledIcon,
                                                          fit: BoxFit.scaleDown,
                                                          width: 18.gw,
                                                          height: 18.gh,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  8.horizontalSpace,
                                                  Expanded(
                                                    flex: 2,
                                                    child: SizedBox(
                                                      height: 43.gh,
                                                      child: AnimationConfiguration.synchronized(
                                                        duration: const Duration(milliseconds: 300),
                                                        child: ScaleAnimation(
                                                          scale: 0.95,
                                                          child: CustomMaterialButton(
                                                            height: 48.gh,
                                                            buttonText: otpState.isTimerActive
                                                                ? '${'resend'.tr()} (${otpState.timerDuration}s)'
                                                                : 'loginVerificationCode'.tr(),
                                                            fontSize: 12.gsp,
                                                            borderRadius: 8.gr,
                                                            padding: EdgeInsets.symmetric(horizontal: 8.gw),
                                                            onPressed: otpState.isTimerActive
                                                                ? null
                                                                : () => _handleSendVerificationCode(context),
                                                            isEnabled: !otpState.isTimerActive,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            15.verticalSpace,
                                            TextFieldWidget(
                                              controller: passwordController,
                                              hintText: 'registerPasswordHint'.tr(),
                                              textInputType: TextInputType.visiblePassword,
                                              prefixIcon: SvgPicture.asset(
                                                Assets.lockIcon,
                                                fit: BoxFit.scaleDown,
                                                width: 18.gw,
                                                height: 18.gh,
                                              ),
                                              obscureText: true,
                                              passwordIcon: true,
                                              validator: (value) => !Validators.newPassword.hasMatch(value ?? '')
                                                  ? 'registerPasswordHint'.tr()
                                                  : null,
                                            ),
                                            15.verticalSpace,
                                            TextFieldWidget(
                                              controller: confirmPasswordController,
                                              hintText: 'registerConfirmPassword'.tr(),
                                              textInputType: TextInputType.visiblePassword,
                                              prefixIcon: SvgPicture.asset(
                                                Assets.lockIcon,
                                                fit: BoxFit.scaleDown,
                                                width: 18.gw,
                                                height: 18.gh,
                                              ),
                                              obscureText: true,
                                              passwordIcon: true,
                                              validator: (value) =>
                                                  value != passwordController.text ? 'passwordsDontMatch'.tr() : null,
                                            ),
                                            15.verticalSpace,
                                            TextFieldWidget(
                                              controller: inviteCodeController,
                                              hintText: 'registerInviteCode'.tr(),
                                              textInputType: TextInputType.text,
                                              prefixIcon: SvgPicture.asset(
                                                Assets.mailIcon,
                                                fit: BoxFit.scaleDown,
                                                width: 18.gw,
                                                height: 18.gh,
                                              ),
                                            ),
                                          ],
                                        );
                                      },
                                    ),
                                    20.verticalSpace,
                                    AnimationConfiguration.synchronized(
                                      duration: const Duration(milliseconds: 300),
                                      child: ScaleAnimation(
                                        scale: 0.95,
                                        child: CustomMaterialButton(
                                          buttonText: 'register'.tr(),
                                          isLoading:
                                              context.read<SignUpCubit>().state.signUpFetchStatus == DataStatus.loading,
                                          onPressed: () => _handleRegisterButtonPress(context),
                                          height: 48.gh,
                                          borderRadius: 8.gr,
                                        ),
                                      ),
                                    ),
                                    12.verticalSpace,
                                    RegisterFooter(),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ));
          },
        ),
      ),
    );
  }
}
