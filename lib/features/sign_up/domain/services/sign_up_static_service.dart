import 'package:gp_stock_app/core/models/apis/sign_up.dart';

import '../../../../core/api/network/models/result.dart';
import '../models/sign_up_request.dart';

/// Static SignUpService class similar to FTradeService and AuthService
/// Provides static methods for sign-up operations
class SignUpService {
  /// Static register method like FTradeService.fetchFTradeConfig
  static Future<ResponseResult<bool>> register({required SignUpRequest signUpRequest}) async {
    return await SignUpApi.register(signUpRequest: signUpRequest);
  }
}
