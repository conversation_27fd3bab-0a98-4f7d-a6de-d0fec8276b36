import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/api/network/endpoint/api_endpoints.dart';
import '../../../../core/api/network/models/result.dart';
import '../../../../core/api/network/network.dart';
import '../models/sigin_up_request.dart/sign_up_request.dart';
import '../repository/sign_up_repository.dart';

@Injectable(as: SignUpRepository)
class SignUpService implements SignUpRepository {
  @override
  Future<ResponseResult<bool>> register({required SignUpRequest signUpRequest}) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.register,
        data: signUpRequest.toJson(),
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to register');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
