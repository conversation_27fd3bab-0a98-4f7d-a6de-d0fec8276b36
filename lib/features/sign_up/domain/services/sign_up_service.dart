import 'package:gp_stock_app/core/models/apis/sign_up.dart';

import '../../../../core/api/network/models/result.dart';
import '../models/sign_up_request.dart';

/// Provides static methods for sign-up operations
/// 提供静态方法用于注册
class SignUpService {
  
  /// Static register method
  /// 静态注册方法
  static Future<ResponseResult<bool>> register({required SignUpRequest signUpRequest}) async {
    return await SignUpApi.register(signUpRequest: signUpRequest);
  }
}
