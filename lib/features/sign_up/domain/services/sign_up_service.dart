import 'package:gp_stock_app/core/models/apis/sign_up.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/api/network/models/result.dart';
import '../models/sigin_up_request.dart/sign_up_request.dart';
import '../repository/sign_up_repository.dart';

@Injectable(as: SignUpRepository)
class SignUpService implements SignUpRepository {
  @override
  Future<ResponseResult<bool>> register({required SignUpRequest signUpRequest}) async {
    return await SignUpApi.register(signUpRequest: signUpRequest);
  }
}
