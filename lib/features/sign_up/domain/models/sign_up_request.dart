import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/sign_up_request.g.dart';
import 'dart:convert';

@JsonSerializable()
class SignUpRequest {
  String? mobile;
  String? password;
  String? inviteCode;
  String? smsCode;
  String? avatar;
  String? countryCode;
  String? email;
  String? nickname;
  int? pid;
  int? sex;
  int? siteId;
  int? type;

  SignUpRequest();

  factory SignUpRequest.fromJson(Map<String, dynamic> json) => $SignUpRequestFromJson(json);

  Map<String, dynamic> toJson() => $SignUpRequestToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
