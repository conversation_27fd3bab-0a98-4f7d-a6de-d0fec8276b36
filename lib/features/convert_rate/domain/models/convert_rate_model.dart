import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/convert_rate_model.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/convert_rate_model.g.dart';

@JsonSerializable()
class ConvertRate {
  String? currencyBase;
  String? currencyTarget;
  double? rate;

  ConvertRate();

  factory ConvertRate.fromJson(Map<String, dynamic> json) => $ConvertRateFromJson(json);

  Map<String, dynamic> toJson() => $ConvertRateToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
