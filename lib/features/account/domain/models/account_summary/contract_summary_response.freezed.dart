// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'contract_summary_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ContractSummaryResponse _$ContractSummaryResponseFromJson(
    Map<String, dynamic> json) {
  return _ContractSummaryResponse.fromJson(json);
}

/// @nodoc
mixin _$ContractSummaryResponse {
  int? get code => throw _privateConstructorUsedError;
  ContractSummaryPage? get data => throw _privateConstructorUsedError;
  String? get msg => throw _privateConstructorUsedError;
  String? get sign => throw _privateConstructorUsedError;

  /// Serializes this ContractSummaryResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ContractSummaryResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ContractSummaryResponseCopyWith<ContractSummaryResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContractSummaryResponseCopyWith<$Res> {
  factory $ContractSummaryResponseCopyWith(ContractSummaryResponse value,
          $Res Function(ContractSummaryResponse) then) =
      _$ContractSummaryResponseCopyWithImpl<$Res, ContractSummaryResponse>;
  @useResult
  $Res call({int? code, ContractSummaryPage? data, String? msg, String? sign});

  $ContractSummaryPageCopyWith<$Res>? get data;
}

/// @nodoc
class _$ContractSummaryResponseCopyWithImpl<$Res,
        $Val extends ContractSummaryResponse>
    implements $ContractSummaryResponseCopyWith<$Res> {
  _$ContractSummaryResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ContractSummaryResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
    Object? sign = freezed,
  }) {
    return _then(_value.copyWith(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as ContractSummaryPage?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
      sign: freezed == sign
          ? _value.sign
          : sign // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of ContractSummaryResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ContractSummaryPageCopyWith<$Res>? get data {
    if (_value.data == null) {
      return null;
    }

    return $ContractSummaryPageCopyWith<$Res>(_value.data!, (value) {
      return _then(_value.copyWith(data: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ContractSummaryResponseImplCopyWith<$Res>
    implements $ContractSummaryResponseCopyWith<$Res> {
  factory _$$ContractSummaryResponseImplCopyWith(
          _$ContractSummaryResponseImpl value,
          $Res Function(_$ContractSummaryResponseImpl) then) =
      __$$ContractSummaryResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? code, ContractSummaryPage? data, String? msg, String? sign});

  @override
  $ContractSummaryPageCopyWith<$Res>? get data;
}

/// @nodoc
class __$$ContractSummaryResponseImplCopyWithImpl<$Res>
    extends _$ContractSummaryResponseCopyWithImpl<$Res,
        _$ContractSummaryResponseImpl>
    implements _$$ContractSummaryResponseImplCopyWith<$Res> {
  __$$ContractSummaryResponseImplCopyWithImpl(
      _$ContractSummaryResponseImpl _value,
      $Res Function(_$ContractSummaryResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContractSummaryResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
    Object? sign = freezed,
  }) {
    return _then(_$ContractSummaryResponseImpl(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as ContractSummaryPage?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
      sign: freezed == sign
          ? _value.sign
          : sign // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ContractSummaryResponseImpl implements _ContractSummaryResponse {
  const _$ContractSummaryResponseImpl(
      {this.code, this.data, this.msg, this.sign});

  factory _$ContractSummaryResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$ContractSummaryResponseImplFromJson(json);

  @override
  final int? code;
  @override
  final ContractSummaryPage? data;
  @override
  final String? msg;
  @override
  final String? sign;

  @override
  String toString() {
    return 'ContractSummaryResponse(code: $code, data: $data, msg: $msg, sign: $sign)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContractSummaryResponseImpl &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.msg, msg) || other.msg == msg) &&
            (identical(other.sign, sign) || other.sign == sign));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, code, data, msg, sign);

  /// Create a copy of ContractSummaryResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ContractSummaryResponseImplCopyWith<_$ContractSummaryResponseImpl>
      get copyWith => __$$ContractSummaryResponseImplCopyWithImpl<
          _$ContractSummaryResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ContractSummaryResponseImplToJson(
      this,
    );
  }
}

abstract class _ContractSummaryResponse implements ContractSummaryResponse {
  const factory _ContractSummaryResponse(
      {final int? code,
      final ContractSummaryPage? data,
      final String? msg,
      final String? sign}) = _$ContractSummaryResponseImpl;

  factory _ContractSummaryResponse.fromJson(Map<String, dynamic> json) =
      _$ContractSummaryResponseImpl.fromJson;

  @override
  int? get code;
  @override
  ContractSummaryPage? get data;
  @override
  String? get msg;
  @override
  String? get sign;

  /// Create a copy of ContractSummaryResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ContractSummaryResponseImplCopyWith<_$ContractSummaryResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ContractSummaryPage _$ContractSummaryPageFromJson(Map<String, dynamic> json) {
  return _ContractSummaryPage.fromJson(json);
}

/// @nodoc
mixin _$ContractSummaryPage {
  int? get current => throw _privateConstructorUsedError;
  bool? get hasNext => throw _privateConstructorUsedError;
  List<ContractSummaryData>? get records => throw _privateConstructorUsedError;
  int? get total => throw _privateConstructorUsedError;

  /// Serializes this ContractSummaryPage to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ContractSummaryPage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ContractSummaryPageCopyWith<ContractSummaryPage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContractSummaryPageCopyWith<$Res> {
  factory $ContractSummaryPageCopyWith(
          ContractSummaryPage value, $Res Function(ContractSummaryPage) then) =
      _$ContractSummaryPageCopyWithImpl<$Res, ContractSummaryPage>;
  @useResult
  $Res call(
      {int? current,
      bool? hasNext,
      List<ContractSummaryData>? records,
      int? total});
}

/// @nodoc
class _$ContractSummaryPageCopyWithImpl<$Res, $Val extends ContractSummaryPage>
    implements $ContractSummaryPageCopyWith<$Res> {
  _$ContractSummaryPageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ContractSummaryPage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? current = freezed,
    Object? hasNext = freezed,
    Object? records = freezed,
    Object? total = freezed,
  }) {
    return _then(_value.copyWith(
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      hasNext: freezed == hasNext
          ? _value.hasNext
          : hasNext // ignore: cast_nullable_to_non_nullable
              as bool?,
      records: freezed == records
          ? _value.records
          : records // ignore: cast_nullable_to_non_nullable
              as List<ContractSummaryData>?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ContractSummaryPageImplCopyWith<$Res>
    implements $ContractSummaryPageCopyWith<$Res> {
  factory _$$ContractSummaryPageImplCopyWith(_$ContractSummaryPageImpl value,
          $Res Function(_$ContractSummaryPageImpl) then) =
      __$$ContractSummaryPageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? current,
      bool? hasNext,
      List<ContractSummaryData>? records,
      int? total});
}

/// @nodoc
class __$$ContractSummaryPageImplCopyWithImpl<$Res>
    extends _$ContractSummaryPageCopyWithImpl<$Res, _$ContractSummaryPageImpl>
    implements _$$ContractSummaryPageImplCopyWith<$Res> {
  __$$ContractSummaryPageImplCopyWithImpl(_$ContractSummaryPageImpl _value,
      $Res Function(_$ContractSummaryPageImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContractSummaryPage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? current = freezed,
    Object? hasNext = freezed,
    Object? records = freezed,
    Object? total = freezed,
  }) {
    return _then(_$ContractSummaryPageImpl(
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      hasNext: freezed == hasNext
          ? _value.hasNext
          : hasNext // ignore: cast_nullable_to_non_nullable
              as bool?,
      records: freezed == records
          ? _value._records
          : records // ignore: cast_nullable_to_non_nullable
              as List<ContractSummaryData>?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ContractSummaryPageImpl implements _ContractSummaryPage {
  const _$ContractSummaryPageImpl(
      {this.current,
      this.hasNext,
      final List<ContractSummaryData>? records,
      this.total})
      : _records = records;

  factory _$ContractSummaryPageImpl.fromJson(Map<String, dynamic> json) =>
      _$$ContractSummaryPageImplFromJson(json);

  @override
  final int? current;
  @override
  final bool? hasNext;
  final List<ContractSummaryData>? _records;
  @override
  List<ContractSummaryData>? get records {
    final value = _records;
    if (value == null) return null;
    if (_records is EqualUnmodifiableListView) return _records;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? total;

  @override
  String toString() {
    return 'ContractSummaryPage(current: $current, hasNext: $hasNext, records: $records, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContractSummaryPageImpl &&
            (identical(other.current, current) || other.current == current) &&
            (identical(other.hasNext, hasNext) || other.hasNext == hasNext) &&
            const DeepCollectionEquality().equals(other._records, _records) &&
            (identical(other.total, total) || other.total == total));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, current, hasNext,
      const DeepCollectionEquality().hash(_records), total);

  /// Create a copy of ContractSummaryPage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ContractSummaryPageImplCopyWith<_$ContractSummaryPageImpl> get copyWith =>
      __$$ContractSummaryPageImplCopyWithImpl<_$ContractSummaryPageImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ContractSummaryPageImplToJson(
      this,
    );
  }
}

abstract class _ContractSummaryPage implements ContractSummaryPage {
  const factory _ContractSummaryPage(
      {final int? current,
      final bool? hasNext,
      final List<ContractSummaryData>? records,
      final int? total}) = _$ContractSummaryPageImpl;

  factory _ContractSummaryPage.fromJson(Map<String, dynamic> json) =
      _$ContractSummaryPageImpl.fromJson;

  @override
  int? get current;
  @override
  bool? get hasNext;
  @override
  List<ContractSummaryData>? get records;
  @override
  int? get total;

  /// Create a copy of ContractSummaryPage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ContractSummaryPageImplCopyWith<_$ContractSummaryPageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ContractSummaryData _$ContractSummaryDataFromJson(Map<String, dynamic> json) {
  return _ContractSummaryData.fromJson(json);
}

/// @nodoc
mixin _$ContractSummaryData {
  int? get id => throw _privateConstructorUsedError;
  double? get accountWinAmount => throw _privateConstructorUsedError;
  double? get allAsset => throw _privateConstructorUsedError;
  double? get closeRemindAmount => throw _privateConstructorUsedError;
  double? get coverLossAmount => throw _privateConstructorUsedError;
  double? get negativeAmount => throw _privateConstructorUsedError;
  String? get currency => throw _privateConstructorUsedError;
  double? get expendAmount => throw _privateConstructorUsedError;
  String? get expireTime => throw _privateConstructorUsedError;
  double? get freezePower => throw _privateConstructorUsedError;
  double? get interestAmount =>
      throw _privateConstructorUsedError; // 实际支付利息总额 折扣后的
// The actual interest paid after the discount.
  double? get receivableInterest => throw _privateConstructorUsedError;
  double? get interestRate => throw _privateConstructorUsedError; // 利率
  String? get marketType => throw _privateConstructorUsedError;
  int? get multiple => throw _privateConstructorUsedError;
  String? get openTime => throw _privateConstructorUsedError;
  int? get periodType =>
      throw _privateConstructorUsedError; // 期限类型 1：按天 2：按周 3：按月 4：免息
  double? get positionAmount => throw _privateConstructorUsedError;
  int? get settlementStatus =>
      throw _privateConstructorUsedError; // 结算状态 1未结算 2已结束
  double? get todayWinAmount => throw _privateConstructorUsedError;
  double? get todayWinRate => throw _privateConstructorUsedError;
  double? get totalAccountAmount => throw _privateConstructorUsedError;
  double? get totalCash => throw _privateConstructorUsedError;
  double? get totalFinance => throw _privateConstructorUsedError;
  double? get totalPower => throw _privateConstructorUsedError;
  int? get type => throw _privateConstructorUsedError; //	合约类型 1：普通 2：体验 3：彩金
  double? get useAmount => throw _privateConstructorUsedError;
  double? get warnRemindAmount => throw _privateConstructorUsedError;
  double? get withdrawAmount => throw _privateConstructorUsedError;
  double? get yesterdayAsset => throw _privateConstructorUsedError;
  double? get winAmount => throw _privateConstructorUsedError;
  double? get initCash => throw _privateConstructorUsedError;
  double? get giveAmount => throw _privateConstructorUsedError;
  double? get contractAssetAmount => throw _privateConstructorUsedError; // 净资产
  double? get gapWarnRemindAmount =>
      throw _privateConstructorUsedError; // 距离预警线金额
  double? get gapCloseRemindAmount =>
      throw _privateConstructorUsedError; // 距离强平线金额
  @JsonKey(name: 'isAutoRenew')
  bool get isAutoRenew =>
      throw _privateConstructorUsedError; // 合约续期状态: true自动续期 false到期结算
  double? get winRate => throw _privateConstructorUsedError;

  /// Serializes this ContractSummaryData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ContractSummaryData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ContractSummaryDataCopyWith<ContractSummaryData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContractSummaryDataCopyWith<$Res> {
  factory $ContractSummaryDataCopyWith(
          ContractSummaryData value, $Res Function(ContractSummaryData) then) =
      _$ContractSummaryDataCopyWithImpl<$Res, ContractSummaryData>;
  @useResult
  $Res call(
      {int? id,
      double? accountWinAmount,
      double? allAsset,
      double? closeRemindAmount,
      double? coverLossAmount,
      double? negativeAmount,
      String? currency,
      double? expendAmount,
      String? expireTime,
      double? freezePower,
      double? interestAmount,
      double? receivableInterest,
      double? interestRate,
      String? marketType,
      int? multiple,
      String? openTime,
      int? periodType,
      double? positionAmount,
      int? settlementStatus,
      double? todayWinAmount,
      double? todayWinRate,
      double? totalAccountAmount,
      double? totalCash,
      double? totalFinance,
      double? totalPower,
      int? type,
      double? useAmount,
      double? warnRemindAmount,
      double? withdrawAmount,
      double? yesterdayAsset,
      double? winAmount,
      double? initCash,
      double? giveAmount,
      double? contractAssetAmount,
      double? gapWarnRemindAmount,
      double? gapCloseRemindAmount,
      @JsonKey(name: 'isAutoRenew') bool isAutoRenew,
      double? winRate});
}

/// @nodoc
class _$ContractSummaryDataCopyWithImpl<$Res, $Val extends ContractSummaryData>
    implements $ContractSummaryDataCopyWith<$Res> {
  _$ContractSummaryDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ContractSummaryData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? accountWinAmount = freezed,
    Object? allAsset = freezed,
    Object? closeRemindAmount = freezed,
    Object? coverLossAmount = freezed,
    Object? negativeAmount = freezed,
    Object? currency = freezed,
    Object? expendAmount = freezed,
    Object? expireTime = freezed,
    Object? freezePower = freezed,
    Object? interestAmount = freezed,
    Object? receivableInterest = freezed,
    Object? interestRate = freezed,
    Object? marketType = freezed,
    Object? multiple = freezed,
    Object? openTime = freezed,
    Object? periodType = freezed,
    Object? positionAmount = freezed,
    Object? settlementStatus = freezed,
    Object? todayWinAmount = freezed,
    Object? todayWinRate = freezed,
    Object? totalAccountAmount = freezed,
    Object? totalCash = freezed,
    Object? totalFinance = freezed,
    Object? totalPower = freezed,
    Object? type = freezed,
    Object? useAmount = freezed,
    Object? warnRemindAmount = freezed,
    Object? withdrawAmount = freezed,
    Object? yesterdayAsset = freezed,
    Object? winAmount = freezed,
    Object? initCash = freezed,
    Object? giveAmount = freezed,
    Object? contractAssetAmount = freezed,
    Object? gapWarnRemindAmount = freezed,
    Object? gapCloseRemindAmount = freezed,
    Object? isAutoRenew = null,
    Object? winRate = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      accountWinAmount: freezed == accountWinAmount
          ? _value.accountWinAmount
          : accountWinAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      allAsset: freezed == allAsset
          ? _value.allAsset
          : allAsset // ignore: cast_nullable_to_non_nullable
              as double?,
      closeRemindAmount: freezed == closeRemindAmount
          ? _value.closeRemindAmount
          : closeRemindAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      coverLossAmount: freezed == coverLossAmount
          ? _value.coverLossAmount
          : coverLossAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      negativeAmount: freezed == negativeAmount
          ? _value.negativeAmount
          : negativeAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      expendAmount: freezed == expendAmount
          ? _value.expendAmount
          : expendAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      expireTime: freezed == expireTime
          ? _value.expireTime
          : expireTime // ignore: cast_nullable_to_non_nullable
              as String?,
      freezePower: freezed == freezePower
          ? _value.freezePower
          : freezePower // ignore: cast_nullable_to_non_nullable
              as double?,
      interestAmount: freezed == interestAmount
          ? _value.interestAmount
          : interestAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      receivableInterest: freezed == receivableInterest
          ? _value.receivableInterest
          : receivableInterest // ignore: cast_nullable_to_non_nullable
              as double?,
      interestRate: freezed == interestRate
          ? _value.interestRate
          : interestRate // ignore: cast_nullable_to_non_nullable
              as double?,
      marketType: freezed == marketType
          ? _value.marketType
          : marketType // ignore: cast_nullable_to_non_nullable
              as String?,
      multiple: freezed == multiple
          ? _value.multiple
          : multiple // ignore: cast_nullable_to_non_nullable
              as int?,
      openTime: freezed == openTime
          ? _value.openTime
          : openTime // ignore: cast_nullable_to_non_nullable
              as String?,
      periodType: freezed == periodType
          ? _value.periodType
          : periodType // ignore: cast_nullable_to_non_nullable
              as int?,
      positionAmount: freezed == positionAmount
          ? _value.positionAmount
          : positionAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      settlementStatus: freezed == settlementStatus
          ? _value.settlementStatus
          : settlementStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      todayWinAmount: freezed == todayWinAmount
          ? _value.todayWinAmount
          : todayWinAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      todayWinRate: freezed == todayWinRate
          ? _value.todayWinRate
          : todayWinRate // ignore: cast_nullable_to_non_nullable
              as double?,
      totalAccountAmount: freezed == totalAccountAmount
          ? _value.totalAccountAmount
          : totalAccountAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      totalCash: freezed == totalCash
          ? _value.totalCash
          : totalCash // ignore: cast_nullable_to_non_nullable
              as double?,
      totalFinance: freezed == totalFinance
          ? _value.totalFinance
          : totalFinance // ignore: cast_nullable_to_non_nullable
              as double?,
      totalPower: freezed == totalPower
          ? _value.totalPower
          : totalPower // ignore: cast_nullable_to_non_nullable
              as double?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      useAmount: freezed == useAmount
          ? _value.useAmount
          : useAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      warnRemindAmount: freezed == warnRemindAmount
          ? _value.warnRemindAmount
          : warnRemindAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      withdrawAmount: freezed == withdrawAmount
          ? _value.withdrawAmount
          : withdrawAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      yesterdayAsset: freezed == yesterdayAsset
          ? _value.yesterdayAsset
          : yesterdayAsset // ignore: cast_nullable_to_non_nullable
              as double?,
      winAmount: freezed == winAmount
          ? _value.winAmount
          : winAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      initCash: freezed == initCash
          ? _value.initCash
          : initCash // ignore: cast_nullable_to_non_nullable
              as double?,
      giveAmount: freezed == giveAmount
          ? _value.giveAmount
          : giveAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      contractAssetAmount: freezed == contractAssetAmount
          ? _value.contractAssetAmount
          : contractAssetAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      gapWarnRemindAmount: freezed == gapWarnRemindAmount
          ? _value.gapWarnRemindAmount
          : gapWarnRemindAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      gapCloseRemindAmount: freezed == gapCloseRemindAmount
          ? _value.gapCloseRemindAmount
          : gapCloseRemindAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      isAutoRenew: null == isAutoRenew
          ? _value.isAutoRenew
          : isAutoRenew // ignore: cast_nullable_to_non_nullable
              as bool,
      winRate: freezed == winRate
          ? _value.winRate
          : winRate // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ContractSummaryDataImplCopyWith<$Res>
    implements $ContractSummaryDataCopyWith<$Res> {
  factory _$$ContractSummaryDataImplCopyWith(_$ContractSummaryDataImpl value,
          $Res Function(_$ContractSummaryDataImpl) then) =
      __$$ContractSummaryDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      double? accountWinAmount,
      double? allAsset,
      double? closeRemindAmount,
      double? coverLossAmount,
      double? negativeAmount,
      String? currency,
      double? expendAmount,
      String? expireTime,
      double? freezePower,
      double? interestAmount,
      double? receivableInterest,
      double? interestRate,
      String? marketType,
      int? multiple,
      String? openTime,
      int? periodType,
      double? positionAmount,
      int? settlementStatus,
      double? todayWinAmount,
      double? todayWinRate,
      double? totalAccountAmount,
      double? totalCash,
      double? totalFinance,
      double? totalPower,
      int? type,
      double? useAmount,
      double? warnRemindAmount,
      double? withdrawAmount,
      double? yesterdayAsset,
      double? winAmount,
      double? initCash,
      double? giveAmount,
      double? contractAssetAmount,
      double? gapWarnRemindAmount,
      double? gapCloseRemindAmount,
      @JsonKey(name: 'isAutoRenew') bool isAutoRenew,
      double? winRate});
}

/// @nodoc
class __$$ContractSummaryDataImplCopyWithImpl<$Res>
    extends _$ContractSummaryDataCopyWithImpl<$Res, _$ContractSummaryDataImpl>
    implements _$$ContractSummaryDataImplCopyWith<$Res> {
  __$$ContractSummaryDataImplCopyWithImpl(_$ContractSummaryDataImpl _value,
      $Res Function(_$ContractSummaryDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContractSummaryData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? accountWinAmount = freezed,
    Object? allAsset = freezed,
    Object? closeRemindAmount = freezed,
    Object? coverLossAmount = freezed,
    Object? negativeAmount = freezed,
    Object? currency = freezed,
    Object? expendAmount = freezed,
    Object? expireTime = freezed,
    Object? freezePower = freezed,
    Object? interestAmount = freezed,
    Object? receivableInterest = freezed,
    Object? interestRate = freezed,
    Object? marketType = freezed,
    Object? multiple = freezed,
    Object? openTime = freezed,
    Object? periodType = freezed,
    Object? positionAmount = freezed,
    Object? settlementStatus = freezed,
    Object? todayWinAmount = freezed,
    Object? todayWinRate = freezed,
    Object? totalAccountAmount = freezed,
    Object? totalCash = freezed,
    Object? totalFinance = freezed,
    Object? totalPower = freezed,
    Object? type = freezed,
    Object? useAmount = freezed,
    Object? warnRemindAmount = freezed,
    Object? withdrawAmount = freezed,
    Object? yesterdayAsset = freezed,
    Object? winAmount = freezed,
    Object? initCash = freezed,
    Object? giveAmount = freezed,
    Object? contractAssetAmount = freezed,
    Object? gapWarnRemindAmount = freezed,
    Object? gapCloseRemindAmount = freezed,
    Object? isAutoRenew = null,
    Object? winRate = freezed,
  }) {
    return _then(_$ContractSummaryDataImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      accountWinAmount: freezed == accountWinAmount
          ? _value.accountWinAmount
          : accountWinAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      allAsset: freezed == allAsset
          ? _value.allAsset
          : allAsset // ignore: cast_nullable_to_non_nullable
              as double?,
      closeRemindAmount: freezed == closeRemindAmount
          ? _value.closeRemindAmount
          : closeRemindAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      coverLossAmount: freezed == coverLossAmount
          ? _value.coverLossAmount
          : coverLossAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      negativeAmount: freezed == negativeAmount
          ? _value.negativeAmount
          : negativeAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      expendAmount: freezed == expendAmount
          ? _value.expendAmount
          : expendAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      expireTime: freezed == expireTime
          ? _value.expireTime
          : expireTime // ignore: cast_nullable_to_non_nullable
              as String?,
      freezePower: freezed == freezePower
          ? _value.freezePower
          : freezePower // ignore: cast_nullable_to_non_nullable
              as double?,
      interestAmount: freezed == interestAmount
          ? _value.interestAmount
          : interestAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      receivableInterest: freezed == receivableInterest
          ? _value.receivableInterest
          : receivableInterest // ignore: cast_nullable_to_non_nullable
              as double?,
      interestRate: freezed == interestRate
          ? _value.interestRate
          : interestRate // ignore: cast_nullable_to_non_nullable
              as double?,
      marketType: freezed == marketType
          ? _value.marketType
          : marketType // ignore: cast_nullable_to_non_nullable
              as String?,
      multiple: freezed == multiple
          ? _value.multiple
          : multiple // ignore: cast_nullable_to_non_nullable
              as int?,
      openTime: freezed == openTime
          ? _value.openTime
          : openTime // ignore: cast_nullable_to_non_nullable
              as String?,
      periodType: freezed == periodType
          ? _value.periodType
          : periodType // ignore: cast_nullable_to_non_nullable
              as int?,
      positionAmount: freezed == positionAmount
          ? _value.positionAmount
          : positionAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      settlementStatus: freezed == settlementStatus
          ? _value.settlementStatus
          : settlementStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      todayWinAmount: freezed == todayWinAmount
          ? _value.todayWinAmount
          : todayWinAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      todayWinRate: freezed == todayWinRate
          ? _value.todayWinRate
          : todayWinRate // ignore: cast_nullable_to_non_nullable
              as double?,
      totalAccountAmount: freezed == totalAccountAmount
          ? _value.totalAccountAmount
          : totalAccountAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      totalCash: freezed == totalCash
          ? _value.totalCash
          : totalCash // ignore: cast_nullable_to_non_nullable
              as double?,
      totalFinance: freezed == totalFinance
          ? _value.totalFinance
          : totalFinance // ignore: cast_nullable_to_non_nullable
              as double?,
      totalPower: freezed == totalPower
          ? _value.totalPower
          : totalPower // ignore: cast_nullable_to_non_nullable
              as double?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      useAmount: freezed == useAmount
          ? _value.useAmount
          : useAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      warnRemindAmount: freezed == warnRemindAmount
          ? _value.warnRemindAmount
          : warnRemindAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      withdrawAmount: freezed == withdrawAmount
          ? _value.withdrawAmount
          : withdrawAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      yesterdayAsset: freezed == yesterdayAsset
          ? _value.yesterdayAsset
          : yesterdayAsset // ignore: cast_nullable_to_non_nullable
              as double?,
      winAmount: freezed == winAmount
          ? _value.winAmount
          : winAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      initCash: freezed == initCash
          ? _value.initCash
          : initCash // ignore: cast_nullable_to_non_nullable
              as double?,
      giveAmount: freezed == giveAmount
          ? _value.giveAmount
          : giveAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      contractAssetAmount: freezed == contractAssetAmount
          ? _value.contractAssetAmount
          : contractAssetAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      gapWarnRemindAmount: freezed == gapWarnRemindAmount
          ? _value.gapWarnRemindAmount
          : gapWarnRemindAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      gapCloseRemindAmount: freezed == gapCloseRemindAmount
          ? _value.gapCloseRemindAmount
          : gapCloseRemindAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      isAutoRenew: null == isAutoRenew
          ? _value.isAutoRenew
          : isAutoRenew // ignore: cast_nullable_to_non_nullable
              as bool,
      winRate: freezed == winRate
          ? _value.winRate
          : winRate // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ContractSummaryDataImpl extends _ContractSummaryData {
  const _$ContractSummaryDataImpl(
      {this.id,
      this.accountWinAmount,
      this.allAsset,
      this.closeRemindAmount,
      this.coverLossAmount,
      this.negativeAmount,
      this.currency,
      this.expendAmount,
      this.expireTime,
      this.freezePower,
      this.interestAmount,
      this.receivableInterest,
      this.interestRate,
      this.marketType,
      this.multiple,
      this.openTime,
      this.periodType,
      this.positionAmount,
      this.settlementStatus,
      this.todayWinAmount,
      this.todayWinRate,
      this.totalAccountAmount,
      this.totalCash,
      this.totalFinance,
      this.totalPower,
      this.type,
      this.useAmount,
      this.warnRemindAmount,
      this.withdrawAmount,
      this.yesterdayAsset,
      this.winAmount,
      this.initCash,
      this.giveAmount,
      this.contractAssetAmount,
      this.gapWarnRemindAmount,
      this.gapCloseRemindAmount,
      @JsonKey(name: 'isAutoRenew') this.isAutoRenew = false,
      this.winRate})
      : super._();

  factory _$ContractSummaryDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$ContractSummaryDataImplFromJson(json);

  @override
  final int? id;
  @override
  final double? accountWinAmount;
  @override
  final double? allAsset;
  @override
  final double? closeRemindAmount;
  @override
  final double? coverLossAmount;
  @override
  final double? negativeAmount;
  @override
  final String? currency;
  @override
  final double? expendAmount;
  @override
  final String? expireTime;
  @override
  final double? freezePower;
  @override
  final double? interestAmount;
// 实际支付利息总额 折扣后的
// The actual interest paid after the discount.
  @override
  final double? receivableInterest;
  @override
  final double? interestRate;
// 利率
  @override
  final String? marketType;
  @override
  final int? multiple;
  @override
  final String? openTime;
  @override
  final int? periodType;
// 期限类型 1：按天 2：按周 3：按月 4：免息
  @override
  final double? positionAmount;
  @override
  final int? settlementStatus;
// 结算状态 1未结算 2已结束
  @override
  final double? todayWinAmount;
  @override
  final double? todayWinRate;
  @override
  final double? totalAccountAmount;
  @override
  final double? totalCash;
  @override
  final double? totalFinance;
  @override
  final double? totalPower;
  @override
  final int? type;
//	合约类型 1：普通 2：体验 3：彩金
  @override
  final double? useAmount;
  @override
  final double? warnRemindAmount;
  @override
  final double? withdrawAmount;
  @override
  final double? yesterdayAsset;
  @override
  final double? winAmount;
  @override
  final double? initCash;
  @override
  final double? giveAmount;
  @override
  final double? contractAssetAmount;
// 净资产
  @override
  final double? gapWarnRemindAmount;
// 距离预警线金额
  @override
  final double? gapCloseRemindAmount;
// 距离强平线金额
  @override
  @JsonKey(name: 'isAutoRenew')
  final bool isAutoRenew;
// 合约续期状态: true自动续期 false到期结算
  @override
  final double? winRate;

  @override
  String toString() {
    return 'ContractSummaryData(id: $id, accountWinAmount: $accountWinAmount, allAsset: $allAsset, closeRemindAmount: $closeRemindAmount, coverLossAmount: $coverLossAmount, negativeAmount: $negativeAmount, currency: $currency, expendAmount: $expendAmount, expireTime: $expireTime, freezePower: $freezePower, interestAmount: $interestAmount, receivableInterest: $receivableInterest, interestRate: $interestRate, marketType: $marketType, multiple: $multiple, openTime: $openTime, periodType: $periodType, positionAmount: $positionAmount, settlementStatus: $settlementStatus, todayWinAmount: $todayWinAmount, todayWinRate: $todayWinRate, totalAccountAmount: $totalAccountAmount, totalCash: $totalCash, totalFinance: $totalFinance, totalPower: $totalPower, type: $type, useAmount: $useAmount, warnRemindAmount: $warnRemindAmount, withdrawAmount: $withdrawAmount, yesterdayAsset: $yesterdayAsset, winAmount: $winAmount, initCash: $initCash, giveAmount: $giveAmount, contractAssetAmount: $contractAssetAmount, gapWarnRemindAmount: $gapWarnRemindAmount, gapCloseRemindAmount: $gapCloseRemindAmount, isAutoRenew: $isAutoRenew, winRate: $winRate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContractSummaryDataImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.accountWinAmount, accountWinAmount) ||
                other.accountWinAmount == accountWinAmount) &&
            (identical(other.allAsset, allAsset) ||
                other.allAsset == allAsset) &&
            (identical(other.closeRemindAmount, closeRemindAmount) ||
                other.closeRemindAmount == closeRemindAmount) &&
            (identical(other.coverLossAmount, coverLossAmount) ||
                other.coverLossAmount == coverLossAmount) &&
            (identical(other.negativeAmount, negativeAmount) ||
                other.negativeAmount == negativeAmount) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.expendAmount, expendAmount) ||
                other.expendAmount == expendAmount) &&
            (identical(other.expireTime, expireTime) ||
                other.expireTime == expireTime) &&
            (identical(other.freezePower, freezePower) ||
                other.freezePower == freezePower) &&
            (identical(other.interestAmount, interestAmount) ||
                other.interestAmount == interestAmount) &&
            (identical(other.receivableInterest, receivableInterest) ||
                other.receivableInterest == receivableInterest) &&
            (identical(other.interestRate, interestRate) ||
                other.interestRate == interestRate) &&
            (identical(other.marketType, marketType) ||
                other.marketType == marketType) &&
            (identical(other.multiple, multiple) ||
                other.multiple == multiple) &&
            (identical(other.openTime, openTime) ||
                other.openTime == openTime) &&
            (identical(other.periodType, periodType) ||
                other.periodType == periodType) &&
            (identical(other.positionAmount, positionAmount) ||
                other.positionAmount == positionAmount) &&
            (identical(other.settlementStatus, settlementStatus) ||
                other.settlementStatus == settlementStatus) &&
            (identical(other.todayWinAmount, todayWinAmount) ||
                other.todayWinAmount == todayWinAmount) &&
            (identical(other.todayWinRate, todayWinRate) ||
                other.todayWinRate == todayWinRate) &&
            (identical(other.totalAccountAmount, totalAccountAmount) ||
                other.totalAccountAmount == totalAccountAmount) &&
            (identical(other.totalCash, totalCash) ||
                other.totalCash == totalCash) &&
            (identical(other.totalFinance, totalFinance) ||
                other.totalFinance == totalFinance) &&
            (identical(other.totalPower, totalPower) ||
                other.totalPower == totalPower) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.useAmount, useAmount) ||
                other.useAmount == useAmount) &&
            (identical(other.warnRemindAmount, warnRemindAmount) ||
                other.warnRemindAmount == warnRemindAmount) &&
            (identical(other.withdrawAmount, withdrawAmount) ||
                other.withdrawAmount == withdrawAmount) &&
            (identical(other.yesterdayAsset, yesterdayAsset) ||
                other.yesterdayAsset == yesterdayAsset) &&
            (identical(other.winAmount, winAmount) ||
                other.winAmount == winAmount) &&
            (identical(other.initCash, initCash) ||
                other.initCash == initCash) &&
            (identical(other.giveAmount, giveAmount) ||
                other.giveAmount == giveAmount) &&
            (identical(other.contractAssetAmount, contractAssetAmount) ||
                other.contractAssetAmount == contractAssetAmount) &&
            (identical(other.gapWarnRemindAmount, gapWarnRemindAmount) ||
                other.gapWarnRemindAmount == gapWarnRemindAmount) &&
            (identical(other.gapCloseRemindAmount, gapCloseRemindAmount) ||
                other.gapCloseRemindAmount == gapCloseRemindAmount) &&
            (identical(other.isAutoRenew, isAutoRenew) ||
                other.isAutoRenew == isAutoRenew) &&
            (identical(other.winRate, winRate) || other.winRate == winRate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        accountWinAmount,
        allAsset,
        closeRemindAmount,
        coverLossAmount,
        negativeAmount,
        currency,
        expendAmount,
        expireTime,
        freezePower,
        interestAmount,
        receivableInterest,
        interestRate,
        marketType,
        multiple,
        openTime,
        periodType,
        positionAmount,
        settlementStatus,
        todayWinAmount,
        todayWinRate,
        totalAccountAmount,
        totalCash,
        totalFinance,
        totalPower,
        type,
        useAmount,
        warnRemindAmount,
        withdrawAmount,
        yesterdayAsset,
        winAmount,
        initCash,
        giveAmount,
        contractAssetAmount,
        gapWarnRemindAmount,
        gapCloseRemindAmount,
        isAutoRenew,
        winRate
      ]);

  /// Create a copy of ContractSummaryData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ContractSummaryDataImplCopyWith<_$ContractSummaryDataImpl> get copyWith =>
      __$$ContractSummaryDataImplCopyWithImpl<_$ContractSummaryDataImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ContractSummaryDataImplToJson(
      this,
    );
  }
}

abstract class _ContractSummaryData extends ContractSummaryData {
  const factory _ContractSummaryData(
      {final int? id,
      final double? accountWinAmount,
      final double? allAsset,
      final double? closeRemindAmount,
      final double? coverLossAmount,
      final double? negativeAmount,
      final String? currency,
      final double? expendAmount,
      final String? expireTime,
      final double? freezePower,
      final double? interestAmount,
      final double? receivableInterest,
      final double? interestRate,
      final String? marketType,
      final int? multiple,
      final String? openTime,
      final int? periodType,
      final double? positionAmount,
      final int? settlementStatus,
      final double? todayWinAmount,
      final double? todayWinRate,
      final double? totalAccountAmount,
      final double? totalCash,
      final double? totalFinance,
      final double? totalPower,
      final int? type,
      final double? useAmount,
      final double? warnRemindAmount,
      final double? withdrawAmount,
      final double? yesterdayAsset,
      final double? winAmount,
      final double? initCash,
      final double? giveAmount,
      final double? contractAssetAmount,
      final double? gapWarnRemindAmount,
      final double? gapCloseRemindAmount,
      @JsonKey(name: 'isAutoRenew') final bool isAutoRenew,
      final double? winRate}) = _$ContractSummaryDataImpl;
  const _ContractSummaryData._() : super._();

  factory _ContractSummaryData.fromJson(Map<String, dynamic> json) =
      _$ContractSummaryDataImpl.fromJson;

  @override
  int? get id;
  @override
  double? get accountWinAmount;
  @override
  double? get allAsset;
  @override
  double? get closeRemindAmount;
  @override
  double? get coverLossAmount;
  @override
  double? get negativeAmount;
  @override
  String? get currency;
  @override
  double? get expendAmount;
  @override
  String? get expireTime;
  @override
  double? get freezePower;
  @override
  double? get interestAmount; // 实际支付利息总额 折扣后的
// The actual interest paid after the discount.
  @override
  double? get receivableInterest;
  @override
  double? get interestRate; // 利率
  @override
  String? get marketType;
  @override
  int? get multiple;
  @override
  String? get openTime;
  @override
  int? get periodType; // 期限类型 1：按天 2：按周 3：按月 4：免息
  @override
  double? get positionAmount;
  @override
  int? get settlementStatus; // 结算状态 1未结算 2已结束
  @override
  double? get todayWinAmount;
  @override
  double? get todayWinRate;
  @override
  double? get totalAccountAmount;
  @override
  double? get totalCash;
  @override
  double? get totalFinance;
  @override
  double? get totalPower;
  @override
  int? get type; //	合约类型 1：普通 2：体验 3：彩金
  @override
  double? get useAmount;
  @override
  double? get warnRemindAmount;
  @override
  double? get withdrawAmount;
  @override
  double? get yesterdayAsset;
  @override
  double? get winAmount;
  @override
  double? get initCash;
  @override
  double? get giveAmount;
  @override
  double? get contractAssetAmount; // 净资产
  @override
  double? get gapWarnRemindAmount; // 距离预警线金额
  @override
  double? get gapCloseRemindAmount; // 距离强平线金额
  @override
  @JsonKey(name: 'isAutoRenew')
  bool get isAutoRenew; // 合约续期状态: true自动续期 false到期结算
  @override
  double? get winRate;

  /// Create a copy of ContractSummaryData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ContractSummaryDataImplCopyWith<_$ContractSummaryDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
