import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';

import 'package:injectable/injectable.dart';

import '../../../../core/api/network/endpoint/api_endpoints.dart';
import '../../../../core/api/network/models/result.dart';
import '../../../../core/api/network/network.dart';
import '../../../../core/api/network/network_helper.dart';
import '../models/invite_detail_model.dart';
import '../repository/invite_repository.dart';

@Injectable(as: InviteRepository)
class InviteService implements InviteRepository {
  @override
  Future<ResponseResult<InviteDetailModel>> getInviteDetails() async {
    try {
      final response = await NetworkProvider().get(
        ApiEndpoints.inviteDetail,
        isAuthRequired: true,
      );

      final responseModel = NetworkHelper.mappingResponseData<InviteDetailModel>(response);

      return ResponseResult(
        data: responseModel.data,
        error: responseModel.data == null ? responseModel.msg : null,
      );
    } on DioException catch (e) {
      return ResponseResult(
        error: e.message ?? 'error'.tr(),
      );
    } catch (e) {
      return ResponseResult(
        error: e.toString(),
      );
    }
  }
}
