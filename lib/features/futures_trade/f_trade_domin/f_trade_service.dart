import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/core/api/network/network_helper.dart';
import 'package:gp_stock_app/features/account/domain/models/account_info/account_info_response.dart';
import 'package:gp_stock_app/features/account/domain/models/calculate_config/calculate_config.dart';
import 'package:gp_stock_app/features/account/domain/services/account_service.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_config_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_state_model.dart';

class FTradeService {
  /// 期货单独证劵配置信息
  static Future<FTradeConfigModel?> fetchFTradeConfig({required String instrument}) async {
    String market = '';
    String securityType = '';
    String symbol = '';
    if (instrument.isNotEmpty && instrument.contains('|')) {
      List<String> parts = instrument.split('|');
      if (parts.length >= 3) {
        market = parts[0];
        securityType = parts[1];
        symbol = parts[2];
      }
    }
    final Response response = await NetworkProvider().get(
      ApiEndpoints.getFuturesGetConfig,
      isAuthRequired: true,
      queryParameters: {'market': market, 'securityType': securityType, 'symbol': symbol},
    );
    final responseModel = NetworkHelper.mappingResponseData<FTradeConfigModel>(response);
    return responseModel.data;
  }

  static Future<double?> fetchUsableCash() async {
    final Response response = await NetworkProvider().get(
      ApiEndpoints.getAccountInfo,
      isAuthRequired: true,
    );
    if (response.statusCode == 200 || response.statusCode == 201) {
      if (response.data['code'] == 0) {
        final ResponseResult<AccountInfoResponse> result = ResponseResult(
          data: AccountInfoResponse.fromJson(response.data),
        );
        final usableCash = result.data?.data?.usableCash;
        if (usableCash != null) {
          return usableCash;
        }
      }
    }
    return null;
  }

  // 2：交易中 4：已收盘 6：交易时段间休市 0：待开盘
  static Future<FTradeStateModel?> fetchTradeState(String market, String productCode) async {
    final Response response = await NetworkProvider().get(
      ApiEndpoints.getFuturesMarketStatus,
      isAuthRequired: true,
      queryParameters: {'market': market, 'productCode': productCode},
    );
    final responseModel = NetworkHelper.mappingResponseData<FTradeStateModel>(response);
    return responseModel.data;
  }

  static Future<(List<CalculateConfig>, List<CalculateConfig>)> fetchServeFeeCalculateConfig({
    required String instrument,
  }) async {
    String market = '';
    String securityType = '';
    if (instrument.isNotEmpty && instrument.contains('|')) {
      List<String> parts = instrument.split('|');
      if (parts.length >= 3) {
        market = parts[0];
        securityType = parts[1];
      }
    }
    // 1=>buy 2=>sell
    try {
      final results = await Future.wait([
        AccountService().getCalculateConfig(market: market, direction: "1", securityType: securityType),
        AccountService().getCalculateConfig(market: market, direction: "2", securityType: securityType)
      ]);

      if (results.every((e) => e.isSuccess && e.data != null)) {
        return (results[0].data ?? [], results[1].data ?? []);
      }
    } catch (_) {}
    return (<CalculateConfig>[], <CalculateConfig>[]);
  }
}
