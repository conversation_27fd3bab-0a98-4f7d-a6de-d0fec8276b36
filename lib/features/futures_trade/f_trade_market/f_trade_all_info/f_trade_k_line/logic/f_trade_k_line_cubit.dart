import 'dart:async';
import 'dart:math';
import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_service.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_state_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_depth_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_info_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_k_line_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_k_line_scroll_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_k_line_scroll_service.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_tick_model.dart';

import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

part 'f_trade_k_line_state.dart';

/// 持有和更新一个交易所的数据,其他未显示交易所的数据放入缓存
class FTradeKLineCubit extends Cubit<FTradeKLineState> {
  FTradeKLineCubit(this.fTradeKLineScrollRepo) : super(FTradeKLineState(fTradeTickModel: FTradeTickModel()));
  final FTradeKLineScrollRepository fTradeKLineScrollRepo;

  String _pollInstrument = '';
  Timer? _pollAllInfoTimer;
  Timer? _pollSubInfoTimer;

  CancelToken? _allInfoCancelToken;

  /*
  ===============================
  Polling
  ===============================
  */

  @override
  Future<void> close() {
    _pollAllInfoTimer?.cancel();
    _pollSubInfoTimer?.cancel();
    return super.close();
  }

  void startPolling({required bool allOrSub}) {
    if (allOrSub) {
      _pollAllInfoTimer?.cancel();
      _pollAllInfoTimer = Timer.periodic(const Duration(milliseconds: 3500), (_) {
        if (state.fTradeStateModel?.status != 2) {
          // 非交易中
          return;
        }
        _allInfoCancelToken?.cancel();
        _allInfoCancelToken = CancelToken();
        _fetchAndSaveDepthData(_allInfoCancelToken, instrument: _pollInstrument);
        if (state.isTimeOrKline) {
          _fetchAndSaveTimeLineData(_allInfoCancelToken, instrument: _pollInstrument, period: state.period);
        } else {
          _fetchAndSaveKLineData(_allInfoCancelToken, instrument: _pollInstrument, period: state.period);
        }
        _fetchAndSaveInfoData(_allInfoCancelToken, instrument: _pollInstrument);
        _updateMoreTicksData(instrument: _pollInstrument);
      });
    }
    if (allOrSub == false) {
      _pollSubInfoTimer?.cancel();
      _pollSubInfoTimer = Timer.periodic(const Duration(milliseconds: 3500), (_) {
        if (state.fTradeStateModel?.status != 2) {
          // 非交易中
          return;
        }
        _fetchAndSaveInfoData(null, instrument: _pollInstrument);
        _fetchAndSaveDepthData(null, instrument: _pollInstrument);
      });
    }
  }

  void resetPolling({required bool allOrSub}) {
    startPolling(allOrSub: allOrSub);
  }

  void stopPolling(bool allOrSub) {
    if (allOrSub) {
      _pollAllInfoTimer?.cancel();
    } else {
      _pollSubInfoTimer?.cancel();
    }
  }

  /*
  ===============================
  User Actions
  ===============================
  */

  void selectedPeriod(String instrument, bool isTimeOrKline, String period) {
    emit(state.copyWith(
      isTimeOrKline: isTimeOrKline,
      period: period,
      kLineDataStatus: DataStatus.loading,
    ));
    _allInfoCancelToken?.cancel();
    _allInfoCancelToken = CancelToken();
    fetchFQuotationAllData(instrument: instrument);
    resetPolling(allOrSub: true);
  }

  /*
  ===============================
  Network and Cache
  ===============================
  */

  /// 获取期货行情所有的数据
  ///
  /// loadCache 决定loading效果
  ///
  /// ```
  ///[+]            NO      访问 HTTP
  ///|获取数据     +----->  -----+---->   +------+
  ///|             |             ^        |订阅  | +-+
  ///|             |             |        +------+   |
  ///v 检查缓存    | YES     缓存列表     |更新UI| <-+
  /// +--------> --+---->  ---------->    +------+
  ///```
  Future<void> fetchFQuotationAllData({
    required String instrument,
  }) async {
    _pollInstrument = instrument;

    final cached = fTradeKLineScrollRepo.loadCacheData(instrument);
    if (cached != null) {
      FTradeTickModel cacheTickModel = FTradeTickModel();
      cacheTickModel.hasNext = true;
      cacheTickModel.current = 1;
      cacheTickModel.total = 100;
      cacheTickModel.records = cached.tickItems;
      emit(state.copyWith(
        fTradeInfoModel: cached.fTradeInfoModel,
        kLineMap: cached.kLineMap,
        kLineDataStatus: DataStatus.success,
        fTradeTickModel: cacheTickModel,
        fTradeDepthModel: cached.fTradeDepthModel,
      ));
    } else {
      emit(state.copyWith(
        fTradeInfoModel: null,
        kLineMap: {},
        kLineDataStatus: DataStatus.loading,
        fTradeDepthModel: null,
      ));
    }

    _allInfoCancelToken?.cancel();
    _allInfoCancelToken = CancelToken();

    try {
      final futures = <Future>[
        _fetchAndSaveInfoData(_allInfoCancelToken, instrument: instrument),
        state.isTimeOrKline
            ? _fetchAndSaveTimeLineData(_allInfoCancelToken, instrument: instrument, period: state.period)
            : _fetchAndSaveKLineData(_allInfoCancelToken, instrument: instrument, period: state.period),
        _fetchAndSaveTicksData(instrument: instrument, pageNumber: 1),
        _fetchAndSaveDepthData(_allInfoCancelToken, instrument: instrument),
      ];
      await Future.wait(futures);
    } catch (error) {
      emit(state.copyWith(
        kLineDataStatus: DataStatus.failed,
      ));
      Helper.showFlutterToast(
        'Failed to fetch network error',
      );
      logDev(error, 'FTradeListService.fetch all data error', error: true);
    }
  }

  /// 获取期货交易所需的部分行情数据
  ///
  /// loadCache 决定loading效果
  ///
  /// ```
  ///[+]            NO      访问 HTTP
  ///|获取数据     +----->  -----+---->   +------+
  ///|             |             ^        |订阅  | +-+
  ///|             |             |        +------+   |
  ///v 检查缓存    | YES     缓存列表     |更新UI| <-+
  /// +--------> --+---->  ---------->    +------+
  ///```
  Future<void> fetchFQuotationSubData({
    required String instrument,
  }) async {
    _pollInstrument = instrument;
    final cached = fTradeKLineScrollRepo.loadCacheData(instrument);
    if (cached != null) {
      emit(state.copyWith(
        fTradeInfoModel: cached.fTradeInfoModel,
        fTradeDepthModel: cached.fTradeDepthModel,
      ));
    } else {
      emit(state.copyWith(
        fTradeInfoModel: null,
        kLineMap: {},
        fTradeDepthModel: null,
      ));
    }
    try {
      _fetchAndSaveInfoData(null, instrument: instrument);
      _fetchAndSaveDepthData(null, instrument: instrument);
    } catch (error) {
      Helper.showFlutterToast(
        'Failed to fetch network error',
      );
      logDev(error, 'FTradeListService.fetch all data error', error: true);
    }
  }

  Future<void> fetchTradeState({
    required String market,
    required String productCode,
  }) async {
    FTradeService.fetchTradeState(market, productCode).then((result) {
      if (result != null) {
        emit(state.copyWith(
          fTradeStateModel: result,
        ));
      } else {
        throw Exception('_fetchAndSaveInfoData network error');
      }
    });
  }

  Future<void> _fetchAndSaveInfoData(
    CancelToken? cancelToken, {
    required String instrument,
  }) async {
    FTradeKLineScrollService.fetchFTradeInfo(cancelToken, instrument: instrument).then((result) {
      if (result != null) {
        emit(state.copyWith(
          fTradeInfoModel: result,
        ));
        fTradeKLineScrollRepo.saveCacheData(
          instrument,
          state.toFTradeKLineCache(instrument: instrument),
        );
      } else {
        throw Exception('_fetchAndSaveInfoData network error');
      }
    });
  }

  Future<void> _fetchAndSaveKLineData(
    CancelToken? cancelToken, {
    required String instrument,
    required String period,
  }) async {
    FTradeKLineScrollService.fetchFTradeKLine(cancelToken, instrument: instrument, period: period).then((result) {
      if (result != null) {
        String periodKey = "k_$period";
        final updatedMap = Map<String, FTradeKLineModel>.from(state.kLineMap)..[periodKey] = result;

        emit(state.copyWith(
          kLineMap: updatedMap,
          kLineDataStatus: DataStatus.success,
        ));
        fTradeKLineScrollRepo.saveCacheData(
          instrument,
          state.toFTradeKLineCache(instrument: instrument),
        );
      } else {
        emit(state.copyWith(
          kLineDataStatus: DataStatus.success,
        ));
        throw Exception('_fetchAndSaveKLineData network error');
      }
    });
  }

  Future<void> _fetchAndSaveTimeLineData(
    CancelToken? cancelToken, {
    required String instrument,
    required String period,
  }) async {
    FTradeKLineScrollService.fetchFTradeTimeLine(cancelToken, instrument: instrument, period: period).then((result) {
      if (result != null) {
        String periodKey = "t_$period";
        final updatedMap = Map<String, FTradeKLineModel>.from(state.kLineMap)..[periodKey] = result;

        emit(state.copyWith(
          kLineMap: updatedMap,
          kLineDataStatus: DataStatus.success,
        ));
        fTradeKLineScrollRepo.saveCacheData(
          instrument,
          state.toFTradeKLineCache(instrument: instrument),
        );
      } else {
        emit(state.copyWith(
          kLineDataStatus: DataStatus.success,
        ));
        throw Exception('_fetchAndSaveTimeLineData network error');
      }
    });
  }

  /// 增加新数据到列表前
  ///
  /// 轮询时使用
  Future<void> _updateMoreTicksData({
    required String instrument,
  }) async {
    FTradeKLineScrollService.fetchTickList(instrument: instrument, pageNumber: 1).then((result) {
      if (result != null) {
        final mergeTicks = _mergeTradeTickRecords(state.fTradeTickModel.records, result.records);
        FTradeTickModel newTickModels = result.copyWith(records: mergeTicks);
        emit(state.copyWith(
          fTradeTickModel: newTickModels,
        ));
        fTradeKLineScrollRepo.saveCacheData(
          instrument,
          state.toFTradeKLineCache(instrument: instrument),
        );
      }
    }).catchError((e) {
      logDev(e, 'FTradeListService.fetchTableData error', error: true);
    });
  }

  List<FTradeTickRecords> _mergeTradeTickRecords(List<FTradeTickRecords> oldList, List<FTradeTickRecords> newList) {
    final existingTimes = oldList.map((e) => e.tradeTime).toSet();
    List<FTradeTickRecords> mergedList = List.from(oldList);

    for (var record in newList) {
      if (!existingTimes.contains(record.tradeTime)) {
        mergedList.add(record);
        existingTimes.add(record.tradeTime);
      }
    }
    mergedList.sort((a, b) => b.tradeTime.compareTo(a.tradeTime));
    if (mergedList.length > 500) {
      mergedList = mergedList.sublist(0, 500);
    }
    return mergedList;
  }

  /// 和产品讨论后确定没有上拉载入更多
  // Future<void> fetchMoreTicksData({
  //   required String instrument,
  // }) async {
  //   int pageNumber = state.fTradeTickModel.current + 1;
  //   FTradeKLineScrollService.fetchTickList(instrument: instrument, pageNumber: pageNumber).then((result) {
  //     if (result != null) {
  //       FTradeTickModel newTickModel = result.copyWith(records: state.fTradeTickModel.records + result.records);
  //       emit(state.copyWith(
  //         fTradeTickModel: newTickModel,
  //       ));
  //     } else {
  //       Helper.showFlutterToast(
  //         'Failed to fetch network error',
  //       );
  //     }
  //   }).catchError((e) {
  //     Helper.showFlutterToast(
  //       'Failed to fetch network error',
  //     );
  //     logDev(e, 'FTradeListService.fetchTableData error', error: true);
  //   });
  // }

  Future<void> _fetchAndSaveTicksData({
    required String instrument,
    required int pageNumber,
  }) async {
    FTradeKLineScrollService.fetchTickList(instrument: instrument, pageNumber: pageNumber).then((result) {
      if (result != null) {
        emit(state.copyWith(
          fTradeTickModel: result,
        ));
        fTradeKLineScrollRepo.saveCacheData(
          instrument,
          state.toFTradeKLineCache(instrument: instrument),
        );
      } else {
        throw Exception('_fetchAndSaveTicksData network error');
      }
    });
  }

  Future<void> _fetchAndSaveDepthData(
    CancelToken? cancelToken, {
    required String instrument,
  }) async {
    FTradeKLineScrollService.fetchFTradeDepth(cancelToken, instrument: instrument).then((result) {
      if (result != null) {
        emit(state.copyWith(
          fTradeDepthModel: result,
        ));
        fTradeKLineScrollRepo.saveCacheData(
          instrument,
          state.toFTradeKLineCache(instrument: instrument),
        );
      } else {
        throw Exception('_fetchAndSaveDepthData network error');
      }
    });
  }
}
