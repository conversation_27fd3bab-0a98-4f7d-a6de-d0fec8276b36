import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_depth_model.dart';

import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';

class FTradeKLineBuySellView extends StatelessWidget {
  final bool needShowPaddingAndShadow;
  final FTradeDepthModel depthModel;
  const FTradeKLineBuySellView({
    super.key,
    required this.needShowPaddingAndShadow,
    required this.depthModel,
  });

  @override
  Widget build(BuildContext context) {
    final bidList = depthModel.bid.map((bid) => bid.toAsk()).toList();
    return Container(
      padding: needShowPaddingAndShadow ? EdgeInsets.symmetric(vertical: 12, horizontal: 8) : EdgeInsets.zero,
      margin: needShowPaddingAndShadow ? EdgeInsets.all(10) : EdgeInsets.zero,
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: needShowPaddingAndShadow ? BorderRadius.circular(8) : BorderRadius.zero,
        boxShadow: needShowPaddingAndShadow
            ? [
                BoxShadow(
                  color: Color(0x14354677), // #35467714，8%
                  offset: Offset(0, 4),
                  blurRadius: 8,
                  spreadRadius: 0,
                ),
              ]
            : [],
      ),
      child: AnimationLimiter(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          spacing: 12,
          children: [
            _buildBuySellHeader(context: context, depthModel: depthModel),
            Row(
              children: [
                Expanded(
                  child: _buildList('buy', depthModel.ask),
                ),
                Expanded(
                  child: _buildList('sell', bidList),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBuySellHeader({
    required BuildContext context,
    required FTradeDepthModel depthModel,
  }) {
    double sellPer = 50; // Default to equal distribution
    double buyPer = 50;
    final buy = depthModel.bid.isNotEmpty ? depthModel.bid[0].vol : 0;
    final sell = depthModel.ask.isNotEmpty ? depthModel.ask[0].vol : 0;
    final total = buy + sell;

    if (total > 0) {
      buyPer = (buy / total * 100);
      sellPer = (100 - buyPer);
    }

    return Column(
      spacing: 5.gh,
      children: [
        Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'ask'.tr(),
                  style: FontPalette.normal12.copyWith(color: ColorPalette.greenColor),
                ),
                Expanded(child: SizedBox()),
                Text(
                  'bid'.tr(),
                  style: FontPalette.normal12.copyWith(color: ColorPalette.redColor),
                ),
                Expanded(child: SizedBox()),
              ],
            ),
          ],
        ),
        Row(
          children: [
            Expanded(
              flex: 12,
              child: Text(
                '${sellPer.toStringAsFixed(2)}%',
                style: FontPalette.semiBold10.copyWith(color: ColorPalette.greenColor, fontFamily: 'Akzidenz-Grotesk'),
              ),
            ),
            Expanded(
              flex: 90,
              child: Container(
                height: 10.gh,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(3),
                ),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    if (sellPer == 1) {
                      return Container(
                        height: 10.gh,
                        decoration: BoxDecoration(
                          color: ColorPalette.greenColor,
                          borderRadius: BorderRadius.horizontal(
                            left: Radius.circular(10),
                            right: sellPer == 1 ? Radius.circular(10) : Radius.zero,
                          ),
                        ),
                      );
                    } else if (buyPer == 1) {
                      return Container(
                        height: 10.gh,
                        decoration: BoxDecoration(
                          color: ColorPalette.redColor,
                          borderRadius: BorderRadius.horizontal(
                            right: Radius.circular(10),
                            left: buyPer == 1 ? Radius.circular(10) : Radius.zero,
                          ),
                        ),
                      );
                    }
                    return Row(
                      children: [
                        Expanded(
                          flex: sellPer.round(),
                          child: Container(
                            height: 10.gh,
                            decoration: BoxDecoration(
                              color: ColorPalette.greenColor,
                              borderRadius: BorderRadius.horizontal(
                                left: Radius.circular(10),
                                right: Radius.zero,
                              ),
                            ),
                          ),
                        ),
                        CustomPaint(
                          size: Size(5.gh, 10.gh),
                          painter: LeftTrianglePainter(color: ColorPalette.greenColor),
                        ),
                        CustomPaint(
                          size: Size(5.gh, 10.gh),
                          painter: RightTrianglePainter(color: ColorPalette.redColor),
                        ),
                        Expanded(
                          flex: buyPer.round(),
                          child: Container(
                            height: 10.gh,
                            decoration: BoxDecoration(
                              color: ColorPalette.redColor,
                              borderRadius: BorderRadius.horizontal(
                                right: Radius.circular(10),
                                left: Radius.zero,
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
            5.horizontalSpace,
            Expanded(
              flex: 12,
              child: Text(
                '${buyPer.toStringAsFixed(2)}%',
                style: FontPalette.semiBold10.copyWith(color: ColorPalette.redColor, fontFamily: 'Akzidenz-Grotesk'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildList(String type, List<FTradeDepthAsk> data) {
    return Builder(
      builder: (context) {
        final isBuy = type == 'buy';
        final itemCount = 1;

        return AnimationLimiter(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(
              data.length >= itemCount ? itemCount : data.length,
              (index) => _buildListItem(context, data[index], index, isBuy),
            ),
          ),
        );
      },
    );
  }

  Widget _buildListItem(BuildContext context, FTradeDepthAsk item, int index, bool isBuy) {
    final itemBg = isBuy
        ? myColorScheme(context).greenColor.withValues(alpha: 0.1)
        : myColorScheme(context).redColor.withValues(alpha: 0.1);
    final serialBg = isBuy ? myColorScheme(context).greenColor : myColorScheme(context).redColor;
    final textColor = isBuy ? myColorScheme(context).greenColor : myColorScheme(context).redColor;

    return AnimationConfiguration.staggeredList(
      position: index,
      duration: const Duration(milliseconds: 300),
      child: SlideAnimation(
        verticalOffset: 20.0,
        child: FadeInAnimation(
          child: Container(
            height: 30.gh,
            color: itemBg,
            padding: EdgeInsets.symmetric(horizontal: 16.gw),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  height: 18.gh,
                  width: 18.gw,
                  decoration: BoxDecoration(
                    color: serialBg,
                    borderRadius: BorderRadius.circular(2.gr),
                  ),
                  child: Center(
                    child: Text(
                      item.depthNo.toString(),
                      style: FontPalette.semiBold12.copyWith(
                        color: myColorScheme(context).cardColor,
                        fontFamily: 'Akzidenz-Grotesk',
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      item.price.toString(),
                      style: FontPalette.semiBold12.copyWith(
                        color: textColor,
                        fontFamily: 'Akzidenz-Grotesk',
                      ),
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      '${formatNumberWithChineseUnits(item.vol.toDouble() / 100)} (${item.no})',
                      style: FontPalette.semiBold12.copyWith(
                        fontFamily: 'Akzidenz-Grotesk',
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class LeftTrianglePainter extends CustomPainter {
  final Color color;
  LeftTrianglePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final topLeft = Offset(0, 0);
    final bottomRight = Offset(size.width, size.height);
    final bottomLeft = Offset(0, size.height);

    final path = Path()
      ..moveTo(bottomLeft.dx, bottomLeft.dy)
      ..lineTo(topLeft.dx, topLeft.dy)
      ..lineTo(bottomRight.dx, bottomRight.dy)
      ..close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class RightTrianglePainter extends CustomPainter {
  final Color color;
  RightTrianglePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final topLeft = Offset(0, 0);
    final topRight = Offset(size.width, 0);
    final bottomRight = Offset(size.width, size.height);

    final path = Path()
      ..moveTo(topLeft.dx, topLeft.dy)
      ..lineTo(topRight.dx, topRight.dy)
      ..lineTo(bottomRight.dx, bottomRight.dy)
      ..close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
