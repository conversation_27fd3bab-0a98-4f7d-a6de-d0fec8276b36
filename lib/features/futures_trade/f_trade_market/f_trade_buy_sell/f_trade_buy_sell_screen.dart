import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_depth_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_info_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/logic/f_trade_k_line_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_buy_sell/logic/f_trade_buy_sell_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_buy_sell/subviews/f_trade_buy_sell_Dialog.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_buy_sell/subviews/subviews.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';

class FTradeBuySellScreen extends StatefulWidget {
  final FTradeMarketType type;
  final FTradeListItemModel data;
  final void Function(int) onChangeAllInfoScreenTitlesAction;

  const FTradeBuySellScreen({
    super.key,
    required this.data,
    required this.onChangeAllInfoScreenTitlesAction,
    required this.type,
  });

  @override
  State<FTradeBuySellScreen> createState() => _FTradeBuySellScreenState();
}

class _FTradeBuySellScreenState extends State<FTradeBuySellScreen> {
  late String instrument;

  /// 交易价格
  late TextEditingController priceEditingController;
  late FocusNode priceFocusNode;

  /// 交易X手
  late TextEditingController numberEditingController;
  late FocusNode numberFocusNode;

  @override
  void initState() {
    super.initState();
    priceEditingController = TextEditingController();
    priceFocusNode = FocusNode();
    numberEditingController = TextEditingController();
    numberFocusNode = FocusNode();

    instrument = widget.data.makeInstrument();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startAfterBuild();
    });
  }

  Future<void> _startAfterBuild() async {
    context.read<FTradeBuySellCubit>().fetchUsableCash();
    context.read<FTradeBuySellCubit>().startUsableCashPolling();
    context.read<FTradeKLineCubit>().fetchFQuotationSubData(instrument: instrument);
    await context.read<FTradeBuySellCubit>().fetchConfigData(widget.data);
    if (!mounted) return;
    if (context.read<FTradeBuySellCubit>().state.fTradeStateModel?.status == 2) {
      context.read<FTradeKLineCubit>().startPolling(allOrSub: false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 14.gw, vertical: 8.gh),
      child: AnimationLimiter(
        child: Column(
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 600),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(child: widget),
            ),
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 12.gh),
                decoration: BoxDecoration(
                  color: myColorScheme(context).cardColor,
                  borderRadius: BorderRadius.circular(10.gr),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildTopHeader(),
                    8.verticalSpace,
                    _buildActionsView(),
                  ],
                ),
              ),
              16.verticalSpace,
              _buildBottomList()
              // TradingDataTable(
              //   market: instrument.market,
              //   securityType: instrument.securityType,
              //   symbol: instrument.symbol,
              //   isIndexTrading: isIndexTrading,
              // ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopHeader() {
    return BlocSelector<FTradeKLineCubit, FTradeKLineState, (FTradeInfoModel, FTradeDepthModel?)>(
      selector: (state) => (state.fTradeInfoModel ?? widget.data.toFTradeInfoModel(), state.fTradeDepthModel),
      builder: (context, state) {
        return FTradeBuySellHeaderView(data: state.$1, type: widget.type, depthData: state.$2);
      },
    );
  }

  Widget _buildActionsView() {
    return BlocConsumer<FTradeBuySellCubit, FTradeBuySellState>(listener: (context, state) {
      final double price = state.userInputController.price;
      final double number = state.userInputController.number;
      priceEditingController.text = price.toStringAsFixed(3);
      numberEditingController.text = number.toStringAsFixed(0);
    }, builder: (context, state) {
      return FTradeBuySellInputView(
        accountUsableCash: state.accountUsableCash,
        latestPrice: state.fTradeLatestPrice ?? 0.0,
        dataStatus: DataStatus.success,
        fTradeConfigModel: state.fTradeConfigModel,
        userInputState: state.userInputController,
        priceEditingController: priceEditingController,
        priceFocusNode: priceFocusNode,
        numberEditingController: numberEditingController,
        numberFocusNode: numberFocusNode,
        onUserInputControllerChanged: (oldInputCtrl) {
          context.read<FTradeBuySellCubit>().handleUserInputControllerChanged(oldInputCtrl);
        },
        buyActionsController: state.buyActionsController.copyWith(onConfirmPressed: () {
          final confirmList = context.read<FTradeBuySellCubit>().makeConfirmList(true);
          showDialog(
            context: context,
            builder: (_) {
              return FTradeBuySellDialog(confirmList: confirmList);
            },
          );
        }),
        sellActionsController: state.sellActionsController.copyWith(onConfirmPressed: () {
          final confirmList = context.read<FTradeBuySellCubit>().makeConfirmList(false);
          showDialog(
            context: context,
            builder: (_) {
              return FTradeBuySellDialog(confirmList: confirmList);
            },
          );
        }),
      );
    });
  }

  Widget _buildBottomList() {
    return Container();
  }
}
