import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_direction_button.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_config_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_buy_sell/logic/f_trade_buy_sell_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_buy_sell/subviews/f_trade_from_limit_view.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_buy_sell/subviews/subviews.dart';

import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/dropdown/dropdown_value.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/counter_textfield.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

class FTradeBuySellInputView extends StatelessWidget {
  final double? accountUsableCash;
  final double latestPrice;
  final DataStatus dataStatus;
  final FTradeConfigModel? fTradeConfigModel;
  final UserInputController userInputState;
  final void Function(UserInputController oldFTradeUserInputController) onUserInputControllerChanged;
  final TextEditingController priceEditingController;
  final FocusNode priceFocusNode;
  final TextEditingController numberEditingController;
  final FocusNode numberFocusNode;
  final FtradebuysellActionsController buyActionsController;
  final FtradebuysellActionsController sellActionsController;
  const FTradeBuySellInputView({
    super.key,
    this.accountUsableCash,
    required this.latestPrice,
    required this.dataStatus,
    required this.fTradeConfigModel,
    required this.userInputState,
    required this.onUserInputControllerChanged,
    required this.priceEditingController,
    required this.priceFocusNode,
    required this.numberEditingController,
    required this.numberFocusNode,
    required this.buyActionsController,
    required this.sellActionsController,
  });

  @override
  Widget build(BuildContext context) {
    return AnimationLimiter(
      child: Column(
          children: AnimationConfiguration.toStaggeredList(
        duration: const Duration(milliseconds: 400),
        childAnimationBuilder: (widget) => SlideAnimation(
          horizontalOffset: 50.0,
          child: FadeInAnimation(
            child: widget,
          ),
        ),
        children: [
          _buildFrom(context),
          FtradebuysellActionsView(
            buyActionsController: buyActionsController,
            cellActionsController: sellActionsController,
          )
        ],
      )),
    );
  }

  Widget _buildFrom(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      spacing: 8,
      children: [
        _buildTradeDirectionSection(
          context: context,
          selectedTradeDirection: userInputState.tradeDirection,
          onTradeDirectionChanged: (newTradeDirection) {
            final temp = userInputState.copyWith(tradeDirection: newTradeDirection);
            onUserInputControllerChanged(temp);
          },
        ),
        Row(
          spacing: 8,
          children: [
            Expanded(
              child: CommonDropdown(
                height: 35.gh,
                showSearchBox: false,
                isEnabled: false,
                selectedItem: null,
                dropDownValue: [DropDownValue()],
                onChanged: (value) {},
                hintText: 'spotTrading'.tr(),
                borderRadius: 5.gr,
                textStyle: FontPalette.normal13.copyWith(color: myColorScheme(context).titleColor),
              ),
            ),
            Expanded(
              child: userInputState.priceType == PriceType.market
                  ? Container(
                      height: 35.gh,
                      padding: EdgeInsets.symmetric(horizontal: 12.gw),
                      decoration: BoxDecoration(
                        color: myColorScheme(context).textFillColor,
                        borderRadius: BorderRadius.circular(5.gr),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'realTimePrice'.tr(),
                            style: FontPalette.normal13.copyWith(
                              color: myColorScheme(context).titleColor,
                            ),
                          ),
                        ],
                      ),
                    )
                  : CounterTextfield(
                      onDecrementPressed: () {
                        double currentValue = userInputState.price - 1;
                        if (currentValue <= 0) {
                          currentValue = 0;
                        }
                        onUserInputControllerChanged(userInputState.copyWith(price: currentValue));
                      },
                      onIncrementPressed: () {
                        double currentValue = userInputState.price + 1;
                        if (currentValue > 99999999) {
                          currentValue = 99999999;
                        }
                        onUserInputControllerChanged(userInputState.copyWith(price: currentValue));
                      },
                      controller: priceEditingController,
                      focusNode: priceFocusNode,
                      onFocusChanged: () {
                        double currentValue = double.tryParse(priceEditingController.text) ?? 0.0;
                        if (currentValue <= 0) {
                          currentValue = 0;
                        } else if (currentValue > 99999999) {
                          currentValue = 99999999;
                        }
                        onUserInputControllerChanged(userInputState.copyWith(price: currentValue));
                      },
                    ),
            ),
          ],
        ),
        Row(
          spacing: 8,
          children: [
            Expanded(
              child: CommonDropdown(
                height: 35.gh,
                showSearchBox: false,
                isEnabled: fTradeConfigModel == null ? false : true,
                selectedItem: userInputState.priceType == PriceType.market
                    ? DropDownValue(id: 'marketOrder', value: 'marketOrder'.tr())
                    : DropDownValue(id: 'limitOrder', value: 'limitOrder'.tr()),
                dropDownValue: [
                  DropDownValue(id: 'marketOrder', value: 'marketOrder'.tr()),
                  DropDownValue(id: 'limitOrder', value: 'limitOrder'.tr()),
                ],
                onChanged: (value) {
                  if (value.id == 'marketOrder') {
                    final temp = userInputState.copyWith(priceType: PriceType.market);
                    onUserInputControllerChanged(temp);
                  }
                  if (value.id == 'limitOrder') {
                    final temp = userInputState.copyWith(priceType: PriceType.limit);
                    onUserInputControllerChanged(temp);
                  }
                },
                hintText: 'trading_methods'.tr(),
                borderRadius: 5.gr,
                textStyle: FontPalette.normal13.copyWith(color: myColorScheme(context).titleColor),
              ),
            ),
            if (fTradeConfigModel == null || userInputState.number == -1)
              Expanded(
                child: Container(
                  height: 35.gh,
                  padding: EdgeInsets.symmetric(horizontal: 12.gw),
                  decoration: BoxDecoration(
                    color: myColorScheme(context).textFillColor,
                    borderRadius: BorderRadius.circular(5.gr),
                  ),
                ),
              ),
            // 网络延迟导致服务器数据不足 会先计算出0 然后计算出正确值 导致闪烁
            if (latestPrice != 0 && fTradeConfigModel != null && userInputState.number != -1)
              Expanded(
                child: CounterTextfield(
                  onDecrementPressed: userInputState.canNumberDecrement == false
                      ? null
                      : () {
                          double currentValue = userInputState.number - 10; // - fTradeConfigModel!.minTradeQuantity;
                          onUserInputControllerChanged(userInputState.copyWith(
                            inputNumberEnum: FTradeBuySellUserInputNumberEnum.input,
                            orderFraction: null,
                            number: currentValue,
                          ));
                        },
                  onIncrementPressed: userInputState.canNumberIncrement == false
                      ? null
                      : () {
                          double currentValue = userInputState.number + 10; //+ fTradeConfigModel!.minTradeQuantity;
                          onUserInputControllerChanged(userInputState.copyWith(
                            inputNumberEnum: FTradeBuySellUserInputNumberEnum.input,
                            orderFraction: null,
                            number: currentValue,
                          ));
                        },
                  controller: numberEditingController,
                  focusNode: numberFocusNode,
                  onFocusChanged: () {
                    final currentValue = double.tryParse(numberEditingController.text) ?? 0.0;
                    onUserInputControllerChanged(userInputState.copyWith(
                      inputNumberEnum: FTradeBuySellUserInputNumberEnum.input,
                      orderFraction: null,
                      number: currentValue,
                    ));
                  },
                ),
              ),
          ],
        ),
        _BalanceLabel(balance: accountUsableCash, currency: null),
        _FractionSection(
          selectedFraction: userInputState.orderFraction,
          onFractionSelected: (selected) {
            onUserInputControllerChanged(userInputState.copyWith(
              inputNumberEnum: FTradeBuySellUserInputNumberEnum.selcted,
              orderFraction: selected,
            ));
          },
        ),
      ],
    );
  }

  Widget _buildTradeDirectionSection({
    required BuildContext context,
    required TradeDirection selectedTradeDirection,
    required void Function(TradeDirection oldTradeDirection) onTradeDirectionChanged,
  }) {
    final labels = (buy: 'open'.tr(), sell: 'sell'.tr());
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'tradeDirection'.tr(),
          style: FontPalette.normal13,
        ),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              spacing: 8,
              children: [
                TradeDirectionButton(
                  text: labels.buy,
                  color: context.upColor,
                  onPressed: () => onTradeDirectionChanged(TradeDirection.buy),
                  isSelected: selectedTradeDirection == TradeDirection.buy,
                ),
                TradeDirectionButton(
                  text: labels.sell,
                  color: context.downColor,
                  onPressed: () => onTradeDirectionChanged(TradeDirection.sell),
                  isSelected: selectedTradeDirection == TradeDirection.sell,
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }
}

/*
===============================
BalanceLabel
===============================
*/

class _BalanceLabel extends StatelessWidget {
  /// 余额
  final double? balance;

  /// 货币单位
  final String? currency;

  const _BalanceLabel({
    required this.balance,
    required this.currency,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'balance'.tr(),
              style: FontPalette.normal13.copyWith(color: ColorPalette.subTitleColor),
            ),
            if (balance == null) ShimmerWidget(height: 18, width: 88),
            if (balance != null)
              AnimatedFlipCounter(
                thousandSeparator: ',',
                fractionDigits: 2,
                suffix: ' ${currency ?? 'CNY'}',
                textStyle: FontPalette.extraBold14.copyWith(
                  color: myColorScheme(context).primaryColor,
                  fontFamily: 'Akzidenz-Grotesk',
                  height: 1,
                ),
                value: balance!,
              )
          ],
        ),
      ],
    );
  }
}

/*
===============================
FractionSection
===============================
*/

class _FractionSection extends StatelessWidget {
  final OrderFraction? selectedFraction;
  final void Function(OrderFraction?) onFractionSelected;

  const _FractionSection({
    required this.selectedFraction,
    required this.onFractionSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: List.generate(OrderFraction.values.length, (index) {
        final fraction = OrderFraction.values[index];
        final isSelected = selectedFraction == fraction;
        return _FractionButton(
          fraction: fraction,
          isSelected: isSelected,
          onTap: () => onFractionSelected(isSelected ? null : fraction),
        );
      }),
    );
  }
}

class _FractionButton extends StatelessWidget {
  const _FractionButton({
    required this.fraction,
    required this.isSelected,
    required this.onTap,
  });

  final OrderFraction fraction;
  final bool isSelected;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 30.gh,
        width: 75.gw,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.gr),
          color: isSelected ? myColorScheme(context).primaryColor : myColorScheme(context).textFillColor,
        ),
        child: Center(
          child: Text(
            fraction.label,
            style: FontPalette.normal14.copyWith(
              color: isSelected ? myColorScheme(context).cardColor : myColorScheme(context).titleColor,
            ),
          ),
        ),
      ),
    );
  }
}
