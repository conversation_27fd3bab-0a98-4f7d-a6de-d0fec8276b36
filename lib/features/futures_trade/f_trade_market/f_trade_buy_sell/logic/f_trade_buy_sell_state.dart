part of 'f_trade_buy_sell_cubit.dart';

class FTradeBuySellState extends Equatable {
  /// 用户可用余额 来着其他cubit
  final double? accountUsableCash;

  /// 期货证劵最新价格
  final double? fTradeLatestPrice;

  /// 期货证券最新一笔交易时间
  final int? fTradeLastTradeTime;

  /// 配置信息数据获取状态
  final DataStatus fTradeConfigDataStatus;

  /// 买入手续费
  final List<CalculateConfig> buyCalculateConfigList;

  /// 卖出手续费
  final List<CalculateConfig> sellCalculateConfigList;

  /// 配置数据
  final FTradeConfigModel? fTradeConfigModel;

  /// 市场状态(开关盘信息)
  final FTradeStateModel? fTradeStateModel;

  /// 用户输入控制器
  final UserInputController userInputController;

  /// 买入控制器
  final FtradebuysellActionsController buyActionsController;

  /// 卖出控制器
  final FtradebuysellActionsController sellActionsController;

  const FTradeBuySellState({
    this.accountUsableCash,
    this.fTradeLatestPrice,
    this.fTradeLastTradeTime,
    this.fTradeConfigDataStatus = DataStatus.loading,
    this.fTradeConfigModel,
    this.fTradeStateModel,
    this.buyCalculateConfigList = const [],
    this.sellCalculateConfigList = const [],
    this.userInputController = const UserInputController(),
    this.buyActionsController = const FtradebuysellActionsController(),
    this.sellActionsController = const FtradebuysellActionsController(),
  });

  FTradeBuySellState copyWith({
    double? accountUsableCash,
    double? fTradeLatestPrice,
    int? fTradeLastTradeTime,
    DataStatus? fTradeConfigDataStatus,
    FTradeConfigModel? fTradeConfigModel,
    FTradeStateModel? fTradeStateModel,
    List<CalculateConfig>? buyCalculateConfigList,
    List<CalculateConfig>? sellCalculateConfigList,
    UserInputController? userInputController,
    FtradebuysellActionsController? buyActionsController,
    FtradebuysellActionsController? sellActionsController,
  }) {
    return FTradeBuySellState(
      accountUsableCash: accountUsableCash ?? this.accountUsableCash,
      fTradeLatestPrice: fTradeLatestPrice ?? this.fTradeLatestPrice,
      fTradeLastTradeTime: fTradeLastTradeTime ?? this.fTradeLastTradeTime,
      fTradeConfigDataStatus: fTradeConfigDataStatus ?? this.fTradeConfigDataStatus,
      buyCalculateConfigList: buyCalculateConfigList ?? this.buyCalculateConfigList,
      sellCalculateConfigList: sellCalculateConfigList ?? this.sellCalculateConfigList,
      fTradeConfigModel: fTradeConfigModel ?? this.fTradeConfigModel,
      fTradeStateModel: fTradeStateModel ?? this.fTradeStateModel,
      userInputController: userInputController ?? this.userInputController,
      buyActionsController: buyActionsController ?? this.buyActionsController,
      sellActionsController: sellActionsController ?? this.sellActionsController,
    );
  }

  @override
  List<Object?> get props => [
        accountUsableCash,
        fTradeLatestPrice,
        fTradeLastTradeTime,
        fTradeConfigDataStatus,
        buyCalculateConfigList,
        sellCalculateConfigList,
        fTradeConfigModel,
        fTradeStateModel,
        userInputController,
        buyActionsController,
        sellActionsController,
      ];
}

/*
===============================
FtradebuysellActionsController
===============================
*/

/// 期货交易用户输入数量模式枚举
enum FTradeBuySellUserInputNumberEnum {
  /// 初始化
  init,

  /// 选择 1/4 1/3 1/2 全仓中进行选择
  selcted,

  /// 用键盘编辑
  input
}

class UserInputController extends Equatable {
  /// 开仓或平仓
  final TradeDirection tradeDirection;

  /// 数量编辑模式
  final FTradeBuySellUserInputNumberEnum inputNumberEnum;

  /// 价格
  final double price;

  /// 数量
  final double number;

  /// 分仓比例
  final OrderFraction? orderFraction;

  /// 市价单 限价单
  final PriceType priceType;

  /// 数量能否减
  final bool canNumberDecrement;

  /// 数量能否加
  final bool canNumberIncrement;

  /// 价格能否减
  final bool canPriceDecrement;

  const UserInputController({
    this.tradeDirection = TradeDirection.buy,
    this.inputNumberEnum = FTradeBuySellUserInputNumberEnum.init,
    this.price = -1,
    this.number = -1,
    this.orderFraction,
    this.priceType = PriceType.market,
    this.canNumberDecrement = false,
    this.canNumberIncrement = false,
    this.canPriceDecrement = false,
  });

  UserInputController copyWith({
    TradeDirection? tradeDirection,
    FTradeBuySellUserInputNumberEnum? inputNumberEnum,
    double? price,
    double? number,
    OrderFraction? orderFraction,
    PriceType? priceType,
    bool? canNumberDecrement,
    bool? canNumberIncrement,
    bool? canPriceDecrement,
  }) {
    return UserInputController(
      tradeDirection: tradeDirection ?? this.tradeDirection,
      inputNumberEnum: inputNumberEnum ?? this.inputNumberEnum,
      price: price ?? this.price,
      number: number ?? this.number,
      orderFraction: orderFraction ?? this.orderFraction,
      priceType: priceType ?? this.priceType,
      canNumberDecrement: canNumberDecrement ?? this.canNumberDecrement,
      canNumberIncrement: canNumberIncrement ?? this.canNumberIncrement,
      canPriceDecrement: canPriceDecrement ?? this.canPriceDecrement,
    );
  }

  @override
  List<Object?> get props => [
        tradeDirection,
        inputNumberEnum,
        price,
        number,
        orderFraction,
        priceType,
        canNumberDecrement,
        canNumberIncrement,
        canPriceDecrement
      ];
}

/*
===============================
FtradebuysellActionsController
===============================
*/

/// 交易买卖操作控制器，封装 UI 所需的参数和交互回调
class FtradebuysellActionsController extends Equatable {
  /// 是否为买入操作（true=买入，false=卖出）
  final bool isBuy;

  int isBuyIntKey() {
    if (isBuy) {
      return 1;
    }
    return 2;
  }

  /// 显示在 UI 中的每一行信息（如价格、数量等）
  /// { 'title': 'DDD',
  ///   'amount': 789.34,
  ///   'currency': 'USDT',
  ///   'fractionDigits': 2,         =>小数点位数
  ///   'showTotalToolTip': true,}   =>是否显示问号(?)和弹窗
  final List<Map<String, dynamic>> displayRowInfoList;

  /// 确认按钮是否处于加载状态
  final bool isLoadingConfirmBtn;

  ///
  final String confirmTitle;

  /// 点击确认按钮时的回调函数
  final VoidCallback? onConfirmPressed;

  /// 选项标题列表，例如 ["市价", "限价"]
  final List<String> optionTitles;

  /// 当前选中的选项标题
  final String? selectedOptionTitle;

  /// 当选项被点击选中时的回调，传入的是下标索引
  final ValueChanged<int>? onOptionCellSelected;

  const FtradebuysellActionsController({
    this.isBuy = true,
    this.displayRowInfoList = const [],
    this.isLoadingConfirmBtn = true,
    this.confirmTitle = '',
    this.onConfirmPressed,
    this.optionTitles = const [],
    this.selectedOptionTitle,
    this.onOptionCellSelected,
  });

  static List<Map<String, dynamic>> makeDisplayRowInfoList(
    String buyOrSell,
    double numberRow0,
    double numberRow1,
    double numberRow2,
    double numberRow3,
  ) {
    return [
      {
        'title': buyOrSell == "buy" ? 'availableToOpen'.tr() : 'availableToClose'.tr(),
        'amount': numberRow0,
        'suffix': 'lotForStockIndex'.tr(), // 'lotForSecurities'.tr()
        'fractionDigits': 0,
      },
      {'title': 'fee'.tr(), 'amount': numberRow1},
      {'title': 'orderAmount'.tr(), 'amount': numberRow2},
      {'title': 'totalPrice'.tr(), 'amount': numberRow3, 'showTotalToolTip': true},
    ];
  }

  /// 默认值初始化函数
  static FtradebuysellActionsController defaultInit(bool isBuy) {
    return FtradebuysellActionsController(
      isBuy: isBuy,
      displayRowInfoList: makeDisplayRowInfoList('buy', 0.0, 0.0, 0.0, 0.0),
      isLoadingConfirmBtn: true,
      confirmTitle: '',
      onConfirmPressed: () {},
      optionTitles: [],
      selectedOptionTitle: null,
      onOptionCellSelected: null,
    );
  }

  FtradebuysellActionsController copyWith({
    bool? isBuy,
    List<Map<String, dynamic>>? displayRowInfoList,
    bool? isLoadingConfirmBtn,
    String? confirmTitle,
    VoidCallback? onConfirmPressed,
    List<String>? optionTitles,
    String? selectedOptionTitle,
    ValueChanged<int>? onOptionCellSelected,
  }) {
    return FtradebuysellActionsController(
      isBuy: isBuy ?? this.isBuy,
      displayRowInfoList: displayRowInfoList ?? this.displayRowInfoList,
      isLoadingConfirmBtn: isLoadingConfirmBtn ?? this.isLoadingConfirmBtn,
      confirmTitle: confirmTitle ?? this.confirmTitle,
      onConfirmPressed: onConfirmPressed ?? this.onConfirmPressed,
      optionTitles: optionTitles ?? this.optionTitles,
      selectedOptionTitle: selectedOptionTitle ?? this.selectedOptionTitle,
      onOptionCellSelected: onOptionCellSelected ?? this.onOptionCellSelected,
    );
  }

  @override
  List<Object?> get props => [
        isBuy,
        displayRowInfoList,
        isLoadingConfirmBtn,
        confirmTitle,
        optionTitles,
        selectedOptionTitle,
      ];
}
