import 'dart:async';
import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/account/domain/models/calculate_config/calculate_config.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_config_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_service.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_state_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/logic/f_trade_k_line_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

part 'f_trade_buy_sell_state.dart';

/// 持有和更新一个交易所的数据,其他未显示交易所的数据放入缓存
class FTradeBuySellCubit extends Cubit<FTradeBuySellState> {
  final FTradeKLineCubit _fTradeKLineCubit;
  late final StreamSubscription _fTradeKlineSubscription;

  Timer? _pollUsableCashTimer;

  FTradeBuySellCubit(super.initialState, this._fTradeKLineCubit) {
    emit(state.copyWith(
      buyActionsController: FtradebuysellActionsController.defaultInit(true),
      sellActionsController: FtradebuysellActionsController.defaultInit(false),
    ));
    _fTradeKlineSubscription = _fTradeKLineCubit.stream.listen((fTradeKLineState) {
      double? fTradelatestPrice = fTradeKLineState.fTradeInfoModel?.latestPrice;
      int? fTradeLastTradeTime = fTradeKLineState.fTradeInfoModel?.lastTradeTime;
      emit(state.copyWith(fTradeLatestPrice: fTradelatestPrice, fTradeLastTradeTime: fTradeLastTradeTime));
      _updateTradeOrderDetail();
    });
  }

  /*
  ===============================
  Polling
  ===============================
  */

  @override
  Future<void> close() {
    _pollUsableCashTimer?.cancel();
    return super.close();
  }

  void startUsableCashPolling() {
    _pollUsableCashTimer?.cancel();
    _pollUsableCashTimer = Timer.periodic(const Duration(milliseconds: 3500), (_) {
      fetchUsableCash();
    });
  }

  /*
  ===============================
  User Actions
  ===============================
  */

  void handleUserInputControllerChanged(UserInputController newCtrl) {
    // 切换 开仓[买] / 平仓[卖]
    // TODO: 比较仓位数据
    // if (newCtrl.tradeDirection == TradeDirection.sell) {
    //   GPEasyLoading.showToast('noAvailablePosition'.tr());
    //   return;
    // }
    emit(state.copyWith(userInputController: newCtrl));
    _updateTradeOrderDetail();
  }

  // TODO: 切换仓位 更新全部 仓位下拉菜单
  void changeActivePosition() {
    _updateTradeOrderDetail();
  }

  /*
  ===============================
  Counting logic
  ===============================
  */

  /// 计算更新交易订单的详细信息
  void _updateTradeOrderDetail() {
    final selectPrice = _countUserInputBaseState('price');
    final allCanBuyNumber = _countUserInputBaseState('allCanBuyNumber');
    final costPerUnit = _countUserInputBaseState('costPerUnit');
    //
    double tempNumber;
    OrderFraction? orderFraction;
    switch (state.userInputController.inputNumberEnum) {
      case FTradeBuySellUserInputNumberEnum.init:
        orderFraction = null;
        // 网络延迟导致服务器数据不足 会先计算出0 然后计算出正确值 导致闪烁
        if (state.accountUsableCash != null && // 余额接口正常
            state.fTradeConfigModel != null && // 证券配置正常
            (state.buyCalculateConfigList.isNotEmpty || state.sellCalculateConfigList.isNotEmpty) && // 买卖手续配置正常
            state.userInputController.number == -1 /**初始化默认值 */) {
          tempNumber = allCanBuyNumber;
        } else {
          tempNumber = state.userInputController.number;
        }
        break;
      case FTradeBuySellUserInputNumberEnum.selcted:
        orderFraction = state.userInputController.orderFraction;
        tempNumber = _countUserInputBaseState('number');
        break;
      case FTradeBuySellUserInputNumberEnum.input:
        orderFraction = null;
        tempNumber = state.userInputController.number;
        break;
    }
    final (:number, :canDe, :canIn) = _countUserInputNumber(tempNumber);
    //
    final double fee = _calculateFee(
      buyOrSell: state.userInputController.tradeDirection == TradeDirection.buy,
      tradeAmount: costPerUnit * number,
      tradeNum: number,
    );
    //
    final buyConfirmTitle =
        state.userInputController.tradeDirection == TradeDirection.buy ? 'openLong'.tr() : 'openShort'.tr();
    final sellConfirmTitle =
        state.userInputController.tradeDirection == TradeDirection.buy ? 'sellShort'.tr() : 'sellLong'.tr();
    //
    emit(state.copyWith(
      userInputController: state.userInputController.copyWith(
        price: selectPrice,
        number: number,
        canNumberDecrement: canDe,
        canNumberIncrement: canIn,
        orderFraction: orderFraction,
      ),
      buyActionsController: state.buyActionsController.copyWith(
        confirmTitle: buyConfirmTitle,
        isLoadingConfirmBtn: state.fTradeConfigDataStatus == DataStatus.loading,
        displayRowInfoList: FtradebuysellActionsController.makeDisplayRowInfoList(
            'buy', allCanBuyNumber, fee, costPerUnit * number, costPerUnit * number + fee),
      ),
      sellActionsController: state.sellActionsController.copyWith(
        confirmTitle: sellConfirmTitle,
        isLoadingConfirmBtn: state.fTradeConfigDataStatus == DataStatus.loading,
        displayRowInfoList: FtradebuysellActionsController.makeDisplayRowInfoList(
            'sell', allCanBuyNumber, fee, costPerUnit * number, costPerUnit * number + fee),
      ),
    ));
  }

  ({double number, bool canDe, bool canIn}) _countUserInputNumber(double number) {
    // 初始化值 不做处理
    if (number < 0) {
      return (number: number, canDe: false, canIn: false);
    }
    bool canNumberDecrement = true;
    bool canNumberIncrement = true;
    final maxTradeQuantity = state.fTradeConfigModel?.maxTradeQuantity ?? 0;
    final minTradeQuantity = state.fTradeConfigModel?.minTradeQuantity ?? 0;

    if (maxTradeQuantity > 0 && number >= maxTradeQuantity) {
      number = maxTradeQuantity;
      canNumberDecrement = true;
      canNumberIncrement = false;
    } else if (minTradeQuantity > 0 && number <= minTradeQuantity) {
      number = minTradeQuantity;
      canNumberDecrement = false;
      canNumberIncrement = true;
    }
    return (number: number, canDe: canNumberDecrement, canIn: canNumberIncrement);
  }

  double _countUserInputBaseState(String countTag) {
    final double selectedPrice;
    if (state.userInputController.priceType == PriceType.market) {
      selectedPrice = state.fTradeLatestPrice ?? 0.0;
    } else {
      selectedPrice = state.userInputController.price;
    }
    if (countTag == 'price') {
      return selectedPrice;
    }
    if (state.accountUsableCash == null || state.accountUsableCash! <= 0.0 || state.fTradeConfigModel == null) {
      return 0.0;
    }
    final double multiple = max(state.fTradeConfigModel!.multiple.toDouble(), 1); // 杠杆倍率 例如:30倍
    final double marginRadio = max(state.fTradeConfigModel!.marginRadio.toDouble(), 1) / 100; // 保证金比例 例如:0.5表示0.5%
    final double costPerUnit = selectedPrice * marginRadio * multiple; // 杠杆缩减过后的价格
    final bool buyOrSell = state.userInputController.tradeDirection == TradeDirection.buy;
    // count 'costPerUnit'
    if (countTag == 'costPerUnit') {
      return costPerUnit;
    }
    // count 'number'
    if (countTag == 'number') {
      final OrderFraction selectedOrderFraction;
      if (state.userInputController.orderFraction == null) {
        selectedOrderFraction = OrderFraction.full;
      } else {
        selectedOrderFraction = state.userInputController.orderFraction!;
      }
      final double selectedUsableCash = state.accountUsableCash! * selectedOrderFraction.fraction;
      return _calculateTradeAmount(selectedUsableCash, costPerUnit, buyOrSell);
    }
    // count 'allCanBuyNumber'
    if (countTag == 'allCanBuyNumber') {
      return _calculateTradeAmount(state.accountUsableCash!, costPerUnit, buyOrSell);
    }
    assert(false, 'Error countTag');
    return 0.0;
  }

  /// 计算可买数量
  ///
  /// availableCash=> 可支配资金
  /// costPerUnit=> 杠杆后每单位价格
  double _calculateTradeAmount(double availableCash, double costPerUnit, bool buyOrSell) {
    final double estimatedNumber = availableCash / costPerUnit;
    final double estimatedFee = _calculateFee(
      buyOrSell: buyOrSell,
      tradeAmount: estimatedNumber.truncateToDouble() * costPerUnit,
      tradeNum: estimatedNumber.truncateToDouble(),
    );
    final double finalAmount = availableCash - estimatedFee;
    return (finalAmount / costPerUnit).truncateToDouble();
  }

  /// 计算手续费
  ///
  /// tradeAmount=> 交易的金额
  ///
  /// tradeNum=> 交易的数量
  ///
  /// chargeList=> 配置的交易函数
  double _calculateFee({required bool buyOrSell, required double tradeAmount, required double tradeNum}) {
    if (buyOrSell && state.sellCalculateConfigList.isEmpty) {
      return 0.0;
    }
    if (buyOrSell == false && state.sellCalculateConfigList.isEmpty) {
      return 0.0;
    }
    final feeRecord = calculateHandleFee(
      tradeAmount: tradeAmount,
      tradeNum: tradeNum,
      chargeList: buyOrSell ? state.buyCalculateConfigList : state.sellCalculateConfigList,
    );
    return buyOrSell ? feeRecord.buyFee : feeRecord.sellFee;
  }

  /*
  ===============================
  Network
  ===============================
  */

  void fetchUsableCash() {
    FTradeService.fetchUsableCash().then((result) {
      emit(state.copyWith(accountUsableCash: result));
      _updateTradeOrderDetail();
    });
  }

  Future<void> fetchConfigData(FTradeListItemModel fTradeListItemModel) async {
    final instrument = fTradeListItemModel.makeInstrument();
    FTradeService.fetchFTradeConfig(instrument: instrument).then((result) {
      if (result != null) {
        emit(state.copyWith(
          fTradeConfigModel: result,
          fTradeConfigDataStatus: DataStatus.success,
        ));
      } else {
        emit(state.copyWith(
          fTradeConfigDataStatus: DataStatus.failed,
        ));
        Helper.showFlutterToast(
          'Failed to fetch network',
        );
      }
      _updateTradeOrderDetail();
    });
    FTradeService.fetchTradeState(fTradeListItemModel.market, fTradeListItemModel.productCode).then((result) {
      if (result != null) {
        emit(state.copyWith(
          fTradeStateModel: result,
        ));
      }
    });
    FTradeService.fetchServeFeeCalculateConfig(instrument: instrument).then((result) {
      if (result.$1.isNotEmpty || result.$2.isNotEmpty) {
        emit(state.copyWith(
          buyCalculateConfigList: result.$1,
          sellCalculateConfigList: result.$2,
          fTradeConfigDataStatus: DataStatus.success,
        ));
      } else {
        emit(state.copyWith(
          fTradeConfigDataStatus: DataStatus.failed,
        ));
        Helper.showFlutterToast(
          'Failed to fetch network',
        );
      }
      _updateTradeOrderDetail();
    });
  }

  /*
  ===============================
  create order
  ===============================
  */

  List<({String label, String value, Color? valueColor, String? currency, bool showTotalToolTip})> makeConfirmList(
      bool buyOrSell) {
    final costPerUnit = _countUserInputBaseState('costPerUnit');
    final number = state.userInputController.number;
    final double fee = _calculateFee(
      buyOrSell: state.userInputController.tradeDirection == TradeDirection.buy,
      tradeAmount: costPerUnit * number,
      tradeNum: number,
    );

    final buyText = state.userInputController.tradeDirection == TradeDirection.buy ? 'openLong'.tr() : 'openShort'.tr();
    final sellText =
        state.userInputController.tradeDirection == TradeDirection.buy ? 'sellShort'.tr() : 'sellLong'.tr();

    return [
      (
        label: "transactionDirection".tr(),
        valueColor: state.userInputController.tradeDirection == TradeDirection.buy ? Colors.red : Colors.green,
        value: buyOrSell ? buyText : sellText,
        currency: null,
        showTotalToolTip: false,
      ),
      (
        label: "accountType".tr(),
        valueColor: null,
        value: "marketTitle7".tr(),
        currency: null,
        showTotalToolTip: false,
      ),
      (
        label: "orderType".tr(),
        valueColor: null,
        value: state.userInputController.priceType == PriceType.market ? "marketOrder".tr() : "limitOrder".tr(),
        currency: null,
        showTotalToolTip: false,
      ),
      (
        label: "leverage_f".tr(),
        valueColor: null,
        value: state.fTradeConfigModel?.multiple.toString() ?? '--',
        currency: null,
        showTotalToolTip: false,
      ),
      (
        label: "price".tr(),
        valueColor: null,
        value: state.fTradeLatestPrice.toString(),
        currency: null,
        showTotalToolTip: false,
      ),
      (
        label: "quantity".tr(),
        valueColor: null,
        value: number.toString(),
        currency: null,
        showTotalToolTip: false,
      ),
      (
        label: "margin_ratio".tr(),
        valueColor: null,
        value: '${(state.fTradeConfigModel?.marginRadio ?? 0) * 0.01}%',
        currency: null,
        showTotalToolTip: false,
      ),
      (
        label: "quantity".tr(),
        valueColor: null,
        value: state.userInputController.number.toString(),
        currency: null,
        showTotalToolTip: false,
      ),
      (
        label: "transactionFee".tr(),
        valueColor: null,
        value: fee.toString(),
        currency: 'CNY',
        showTotalToolTip: false,
      ),
      (
        label: "required_margin".tr(),
        valueColor: null,
        value: (costPerUnit * number).toString(),
        currency: 'CNY',
        showTotalToolTip: false,
      ),
      (
        label: "totalPrice".tr(),
        valueColor: null,
        value: (costPerUnit * number + fee).toString(),
        currency: 'CNY',
        showTotalToolTip: true,
      ),
    ];
  }
}
