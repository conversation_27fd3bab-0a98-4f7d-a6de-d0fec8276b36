import 'dart:async';
import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/core/api/network/network_helper.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';

class FTradeListService {
  /// *  param marketType 交易所唯一ID (Unique ID of the exchange).
  /// *  param field 排序关键字 (Sorting key). 可选值 (Options):
  ///   - `gain`: 按涨跌额排序 (Sort by price change amount).
  ///   - `latestPrice`: 按最新价格排序 (Sort by latest price).
  ///   - `name`: 按名称排序 (Sort by name).
  /// *  param order 排序方式 (Sorting order). 可选值 (Options):
  ///   - `DESC`: 降序 (Descending order).
  ///   - `ASC`: 升序 (Ascending order).
  /// *  param pageNumber 页码 (Page number for pagination).
  /// *  param pageSize 每页数量 (Number of items per page).
  static Future<FTradeListModel?> fetchTableData(
    CancelToken? cancelToken, {
    required Map<String, dynamic> queryParameters,
  }) async {
    queryParameters["securityType"] = 4;
    final Response response = await NetworkProvider().get(
      ApiEndpoints.getFuturesMarketGetMarketPlate,
      queryParameters: queryParameters,
      cancelToken: cancelToken,
    );
    final responseModel = NetworkHelper.mappingResponseData<FTradeListModel>(response);
    return responseModel.data;
  }
}
