import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/models/route_arguments/trading_arguments.dart';
import 'package:gp_stock_app/shared/routes/routes.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../shared/app/extension/helper.dart';
import '../../shared/constants/enums.dart';
import '../../shared/theme/font_pallette.dart';
import '../../shared/theme/my_color_scheme.dart';
import '../../shared/widgets/pagination/common_refresher.dart';
import '../../shared/widgets/shimmer/shimmer_widget.dart';
import '../../shared/widgets/sort_header.dart';
import '../account/widgets/table_empty.dart';
import 'domain/models/plate_info_request/plate_info_request.dart';
import 'domain/models/plate_info_response.dart/plate_info_response.dart';
import 'domain/models/stock_reponse/stock_info_response.dart';
import 'logic/market/market_cubit.dart';
import 'widgets/plate_info_table_row.dart';

class PlateInfoScreen extends StatefulWidget {
  final PlateInfoRequest plateInfoRequest;

  const PlateInfoScreen({
    super.key,
    required this.plateInfoRequest,
  });

  @override
  State<PlateInfoScreen> createState() => _PlateInfoScreenState();
}

class _PlateInfoScreenState extends State<PlateInfoScreen> {
  final _refreshController = RefreshController();

  @override
  void initState() {
    super.initState();
    Helper.afterInit(_fetchData);
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  void _fetchData() {
    context.read<MarketCubit>().resetPlateData();
    context.read<MarketCubit>().fetchPlateInfo(
          request: widget.plateInfoRequest,
        );
  }

  void _onRefresh(PlateInfoResponse? response) {
    _fetchData();
    _refreshController.resetNoData();
    _refreshController.refreshCompleted();
  }

  void _onLoadMore(PlateInfoResponse? response) {
    _refreshController.loadNoData();
    context.read<MarketCubit>().fetchPlateInfo(
          request: widget.plateInfoRequest,
          isLoadMore: true,
        );
    _refreshController.loadComplete();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.plateInfoRequest.title),
        surfaceTintColor: Colors.transparent,
      ),
      body: _buildTableContainer(),
    );
  }

  Widget _buildTableContainer() {
    return Container(
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(4.gr),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withNewOpacity(0.08),
            blurRadius: 15,
            offset: const Offset(0, 4),
            spreadRadius: 2,
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(20.gr),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTableHeader(),
            12.verticalSpace,
            Divider(
              color: myColorScheme(context).dividerColor.withNewOpacity(0.8),
              thickness: 1.2,
            ),
            8.verticalSpace,
            Expanded(child: _buildTableContent()),
          ],
        ),
      ),
    );
  }

  Widget _buildTableHeader() {
    return BlocBuilder<MarketCubit, MarketState>(
      builder: (context, state) {
        return Row(
          children: [
            // Name header
            Expanded(
              flex: 2,
              child: Text(
                'name'.tr(),
                style: _getHeaderStyle(),
              ),
            ),
            // Latest price header
            Expanded(
              child: SortHeader(
                title: 'currentPrice'.tr(),
                sortType: state.sortByPricePlateInfoAsc,
                onTap: () => context.read<MarketCubit>().handleSortByPricePlateInfo(
                      request: widget.plateInfoRequest,
                    ),
                alignment: MainAxisAlignment.center,
                textStyle: FontPalette.medium12,
              ),
            ),
            // Change header
            Expanded(
              child: SortHeader(
                title: 'change'.tr(),
                sortType: state.sortByChangePlateInfoAsc,
                onTap: () => context.read<MarketCubit>().handleSortByChangePlateInfo(
                      request: widget.plateInfoRequest,
                    ),
                alignment: MainAxisAlignment.center,
                textStyle: FontPalette.medium12,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildTableContent() {
    return BlocSelector<MarketCubit, MarketState, (DataStatus, PlateInfoResponse?)>(
      selector: (state) => (
        state.plateInfoFetchStatus,
        state.plateInfoResponse,
      ),
      builder: (context, data) {
        final (status, response) = data;
        if (status == DataStatus.loading) _buildLoadingList();
        if (response?.data.isEmpty ?? true) TableEmptyWidget();
        return CommonRefresher(
          bgColor: myColorScheme(context).cardColor,
          controller: _refreshController,
          onRefresh: () => _onRefresh(response),
          onLoading: () => _onLoadMore(response),
          enablePullUp: true,
          enablePullDown: true,
          child: ListView.builder(
            physics: const BouncingScrollPhysics(),
            padding: EdgeInsets.symmetric(vertical: 8.gh),
            itemCount: response?.data.length ?? 0,
            itemBuilder: (context, index) => PlateInfoTableRow(
              data: response?.data[index] ?? StockInfoData(),
              onTap: () => _onRowTap(response!.data[index]),
            ),
          ),
        );
      },
    );
  }

  void _onRowTap(StockInfoData data) {
    Navigator.pushNamed(context, routeTradingCenter,
        arguments: TradingArguments(instrumentInfo: data.instrumentInfo, selectedIndex: 1));
  }

  Widget _buildLoadingList() {
    return ListView.separated(
      itemCount: 6,
      padding: EdgeInsets.symmetric(vertical: 8.gh),
      separatorBuilder: (_, __) => 10.verticalSpace,
      itemBuilder: (_, __) => ClipRRect(
        borderRadius: BorderRadius.circular(8.gr),
        child: ShimmerWidget(
          height: 48.gh,
          width: double.infinity,
        ),
      ),
    );
  }

  TextStyle _getHeaderStyle() => FontPalette.medium14.copyWith(
        color: myColorScheme(context).titleColor,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.3,
      );
}
