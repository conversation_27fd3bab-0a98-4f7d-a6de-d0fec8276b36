import 'dart:async';

import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/features/market/watch_list/domain/models/watch_model.dart';
import 'package:gp_stock_app/features/market/watch_list/domain/repository/watch_list_repo.dart';
import 'package:gp_stock_app/features/profile/logic/auth_aware_cubit.dart';
import 'package:gp_stock_app/features/sign_in/domain/models/login/login_response.dart';

import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/utils/log.dart';
import 'watch_list_state.dart';

@singleton
class WatchListCubit extends AuthAwareCubit<WatchListState> {
  final WatchListRepository _repository;

  WatchListCubit(this._repository) : super(const WatchListState());

  @override
  void onLoggedIn(LoginResponse loginResponse) => getWatchList();

  @override
  void onLoggedOut() => emit(const WatchListState());

  Future<void> addToWatchList({
    required String symbol,
    required String market,
    int putSort = 0,
    required String securityType,
    int sort = 0,
  }) async {
    emit(state.copyWith(addToWatchListStatus: DataStatus.loading, watchListByInstrument: WatchModel.dummy()));
    try {
      final result = await _repository.addToWatchList(
        symbol: symbol,
        market: market,
        putSort: putSort,
        securityType: securityType,
        sort: sort,
      );
      if (result.data != null && result.data!) {
        emit(state.copyWith(addToWatchListStatus: DataStatus.success, watchListByInstrument: WatchModel.dummy()));
      } else {
        emit(state.copyWith(addToWatchListStatus: DataStatus.failed, error: result.error, watchListByInstrument: null));
      }
    } catch (e) {
      emit(state.copyWith(addToWatchListStatus: DataStatus.failed, watchListByInstrument: null));
      logDev(e, 'addToWatchList', error: true);
    }
  }

  Future<void> removeFromWatchList(int choiceId) async {
    final watchModel = state.watchListByInstrument;
    emit(state.copyWith(removeFromWatchListStatus: DataStatus.loading, watchListByInstrument: null));
    try {
      final result = await _repository.removeFromWatchList(choiceId);
      if (result.data != null && result.data!) {
        emit(state.copyWith(removeFromWatchListStatus: DataStatus.success, watchListByInstrument: null));
      } else {
        emit(state.copyWith(
          removeFromWatchListStatus: DataStatus.failed,
          error: result.error,
          watchListByInstrument: watchModel,
        ));
      }
    } catch (e) {
      emit(state.copyWith(removeFromWatchListStatus: DataStatus.failed, watchListByInstrument: watchModel));
      logDev(e, 'removeFromWatchList', error: true);
    }
  }

  Future<void> getWatchList({bool loadMore = false, String? market}) async {
    if (loadMore && !state.hasMore) return;
    if (state.getWatchListStatus.isLoading) return;
    emit(
      state.copyWith(
        getWatchListStatus: DataStatus.loading,
        currentPage: loadMore ? state.currentPage + 1 : 1,
        isLoadMore: loadMore,
      ),
    );

    try {
      final result = await _repository.getWatchList(
        pageNumber: state.currentPage,
        pageSize: 20,
        market: market,
      );

      if (result.data != null) {
        final newWatchList = loadMore ? [...?state.watchList, ...?result.data?.records] : result.data?.records;

        emit(state.copyWith(
          getWatchListStatus: DataStatus.success,
          watchList: newWatchList,
          hasMore: (newWatchList?.length ?? 0) < (result.data?.total ?? 0),
          totalPages: result.data?.total ?? state.totalPages,
          currentPage: result.data?.current ?? state.currentPage,
        ));
      } else {
        emit(state.copyWith(
          getWatchListStatus: DataStatus.failed,
          error: result.error,
        ));
      }
    } catch (e) {
      emit(state.copyWith(getWatchListStatus: DataStatus.failed));
      logDev(e, 'getWatchList', error: true);
    }
  }

  Future<void> getWatchListDetail(String choiceId) async {
    emit(state.copyWith(getWatchListDetailStatus: DataStatus.loading));
    try {
      final result = await _repository.getWatchListDetail(choiceId);
      if (result.data != null) {
        emit(state.copyWith(
          getWatchListDetailStatus: DataStatus.success,
          watchListDetail: result.data,
        ));
      } else {
        emit(state.copyWith(
          getWatchListDetailStatus: DataStatus.failed,
          error: result.error,
        ));
      }
    } catch (e) {
      emit(state.copyWith(getWatchListDetailStatus: DataStatus.failed));
      logDev(e, 'getWatchListDetail', error: true);
    }
  }

  Future<void> getWatchListByInstrument(String instrument) async {
    emit(state.copyWith(getWatchListByInstrumentStatus: DataStatus.loading));
    try {
      final result = await _repository.getWatchListByInstrument(instrument);
      if (result.data != null && result.data!.id != 0) {
        emit(state.copyWith(
          getWatchListByInstrumentStatus: DataStatus.success,
          watchListByInstrument: result.data,
        ));
      } else {
        emit(state.copyWith(
          getWatchListByInstrumentStatus: DataStatus.failed,
          error: result.error,
          watchListByInstrument: null,
        ));
      }
    } catch (e) {
      emit(state.copyWith(getWatchListByInstrumentStatus: DataStatus.failed));
      logDev(e, 'getWatchListByInstrument', error: true);
    }
  }

  void handleSortByPrice() {
    final nextSortType = state.sortByPriceAsc == null
        ? SortType.ASC
        : state.sortByPriceAsc == SortType.ASC
            ? SortType.DESC
            : null;
    emit(state.copyWith(sortByPriceAsc: nextSortType));
  }

  void handleSortByChange() {
    final nextSortType = state.sortByChangeAsc == null
        ? SortType.ASC
        : state.sortByChangeAsc == SortType.ASC
            ? SortType.DESC
            : null;
    emit(state.copyWith(sortByChangeAsc: nextSortType));
  }
}
