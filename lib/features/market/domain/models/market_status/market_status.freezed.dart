// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'market_status.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MarketStatus _$MarketStatusFromJson(Map<String, dynamic> json) {
  return _MarketStatus.fromJson(json);
}

/// @nodoc
mixin _$MarketStatus {
  @JsonKey(name: 'statusInfo')
  MarketStatusInfo get statusInfo => throw _privateConstructorUsedError;
  @JsonKey(name: 'marketType')
  String get marketType => throw _privateConstructorUsedError;

  /// Serializes this MarketStatus to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MarketStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MarketStatusCopyWith<MarketStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MarketStatusCopyWith<$Res> {
  factory $MarketStatusCopyWith(
          MarketStatus value, $Res Function(MarketStatus) then) =
      _$MarketStatusCopyWithImpl<$Res, MarketStatus>;
  @useResult
  $Res call(
      {@JsonKey(name: 'statusInfo') MarketStatusInfo statusInfo,
      @JsonKey(name: 'marketType') String marketType});

  $MarketStatusInfoCopyWith<$Res> get statusInfo;
}

/// @nodoc
class _$MarketStatusCopyWithImpl<$Res, $Val extends MarketStatus>
    implements $MarketStatusCopyWith<$Res> {
  _$MarketStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MarketStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? statusInfo = null,
    Object? marketType = null,
  }) {
    return _then(_value.copyWith(
      statusInfo: null == statusInfo
          ? _value.statusInfo
          : statusInfo // ignore: cast_nullable_to_non_nullable
              as MarketStatusInfo,
      marketType: null == marketType
          ? _value.marketType
          : marketType // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of MarketStatus
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MarketStatusInfoCopyWith<$Res> get statusInfo {
    return $MarketStatusInfoCopyWith<$Res>(_value.statusInfo, (value) {
      return _then(_value.copyWith(statusInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MarketStatusImplCopyWith<$Res>
    implements $MarketStatusCopyWith<$Res> {
  factory _$$MarketStatusImplCopyWith(
          _$MarketStatusImpl value, $Res Function(_$MarketStatusImpl) then) =
      __$$MarketStatusImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'statusInfo') MarketStatusInfo statusInfo,
      @JsonKey(name: 'marketType') String marketType});

  @override
  $MarketStatusInfoCopyWith<$Res> get statusInfo;
}

/// @nodoc
class __$$MarketStatusImplCopyWithImpl<$Res>
    extends _$MarketStatusCopyWithImpl<$Res, _$MarketStatusImpl>
    implements _$$MarketStatusImplCopyWith<$Res> {
  __$$MarketStatusImplCopyWithImpl(
      _$MarketStatusImpl _value, $Res Function(_$MarketStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of MarketStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? statusInfo = null,
    Object? marketType = null,
  }) {
    return _then(_$MarketStatusImpl(
      statusInfo: null == statusInfo
          ? _value.statusInfo
          : statusInfo // ignore: cast_nullable_to_non_nullable
              as MarketStatusInfo,
      marketType: null == marketType
          ? _value.marketType
          : marketType // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MarketStatusImpl implements _MarketStatus {
  const _$MarketStatusImpl(
      {@JsonKey(name: 'statusInfo') required this.statusInfo,
      @JsonKey(name: 'marketType') required this.marketType});

  factory _$MarketStatusImpl.fromJson(Map<String, dynamic> json) =>
      _$$MarketStatusImplFromJson(json);

  @override
  @JsonKey(name: 'statusInfo')
  final MarketStatusInfo statusInfo;
  @override
  @JsonKey(name: 'marketType')
  final String marketType;

  @override
  String toString() {
    return 'MarketStatus(statusInfo: $statusInfo, marketType: $marketType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MarketStatusImpl &&
            (identical(other.statusInfo, statusInfo) ||
                other.statusInfo == statusInfo) &&
            (identical(other.marketType, marketType) ||
                other.marketType == marketType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, statusInfo, marketType);

  /// Create a copy of MarketStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MarketStatusImplCopyWith<_$MarketStatusImpl> get copyWith =>
      __$$MarketStatusImplCopyWithImpl<_$MarketStatusImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MarketStatusImplToJson(
      this,
    );
  }
}

abstract class _MarketStatus implements MarketStatus {
  const factory _MarketStatus(
      {@JsonKey(name: 'statusInfo') required final MarketStatusInfo statusInfo,
      @JsonKey(name: 'marketType')
      required final String marketType}) = _$MarketStatusImpl;

  factory _MarketStatus.fromJson(Map<String, dynamic> json) =
      _$MarketStatusImpl.fromJson;

  @override
  @JsonKey(name: 'statusInfo')
  MarketStatusInfo get statusInfo;
  @override
  @JsonKey(name: 'marketType')
  String get marketType;

  /// Create a copy of MarketStatus
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MarketStatusImplCopyWith<_$MarketStatusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

MarketStatusInfo _$MarketStatusInfoFromJson(Map<String, dynamic> json) {
  return _MarketStatusInfo.fromJson(json);
}

/// @nodoc
mixin _$MarketStatusInfo {
  @JsonKey(name: 'statusStr')
  String get statusStr => throw _privateConstructorUsedError;
  @JsonKey(name: 'closeTime')
  int get closeTime => throw _privateConstructorUsedError;
  @JsonKey(name: 'openTime')
  int get openTime => throw _privateConstructorUsedError;

  /// 市场状态1：盘前交易 2：交易中 3：盘后交易 4：已收盘 6：午间休市 0：待开盘
  @JsonKey(name: 'status')
  int get status => throw _privateConstructorUsedError;

  /// Serializes this MarketStatusInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MarketStatusInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MarketStatusInfoCopyWith<MarketStatusInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MarketStatusInfoCopyWith<$Res> {
  factory $MarketStatusInfoCopyWith(
          MarketStatusInfo value, $Res Function(MarketStatusInfo) then) =
      _$MarketStatusInfoCopyWithImpl<$Res, MarketStatusInfo>;
  @useResult
  $Res call(
      {@JsonKey(name: 'statusStr') String statusStr,
      @JsonKey(name: 'closeTime') int closeTime,
      @JsonKey(name: 'openTime') int openTime,
      @JsonKey(name: 'status') int status});
}

/// @nodoc
class _$MarketStatusInfoCopyWithImpl<$Res, $Val extends MarketStatusInfo>
    implements $MarketStatusInfoCopyWith<$Res> {
  _$MarketStatusInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MarketStatusInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? statusStr = null,
    Object? closeTime = null,
    Object? openTime = null,
    Object? status = null,
  }) {
    return _then(_value.copyWith(
      statusStr: null == statusStr
          ? _value.statusStr
          : statusStr // ignore: cast_nullable_to_non_nullable
              as String,
      closeTime: null == closeTime
          ? _value.closeTime
          : closeTime // ignore: cast_nullable_to_non_nullable
              as int,
      openTime: null == openTime
          ? _value.openTime
          : openTime // ignore: cast_nullable_to_non_nullable
              as int,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MarketStatusInfoImplCopyWith<$Res>
    implements $MarketStatusInfoCopyWith<$Res> {
  factory _$$MarketStatusInfoImplCopyWith(_$MarketStatusInfoImpl value,
          $Res Function(_$MarketStatusInfoImpl) then) =
      __$$MarketStatusInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'statusStr') String statusStr,
      @JsonKey(name: 'closeTime') int closeTime,
      @JsonKey(name: 'openTime') int openTime,
      @JsonKey(name: 'status') int status});
}

/// @nodoc
class __$$MarketStatusInfoImplCopyWithImpl<$Res>
    extends _$MarketStatusInfoCopyWithImpl<$Res, _$MarketStatusInfoImpl>
    implements _$$MarketStatusInfoImplCopyWith<$Res> {
  __$$MarketStatusInfoImplCopyWithImpl(_$MarketStatusInfoImpl _value,
      $Res Function(_$MarketStatusInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of MarketStatusInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? statusStr = null,
    Object? closeTime = null,
    Object? openTime = null,
    Object? status = null,
  }) {
    return _then(_$MarketStatusInfoImpl(
      statusStr: null == statusStr
          ? _value.statusStr
          : statusStr // ignore: cast_nullable_to_non_nullable
              as String,
      closeTime: null == closeTime
          ? _value.closeTime
          : closeTime // ignore: cast_nullable_to_non_nullable
              as int,
      openTime: null == openTime
          ? _value.openTime
          : openTime // ignore: cast_nullable_to_non_nullable
              as int,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MarketStatusInfoImpl implements _MarketStatusInfo {
  const _$MarketStatusInfoImpl(
      {@JsonKey(name: 'statusStr') required this.statusStr,
      @JsonKey(name: 'closeTime') required this.closeTime,
      @JsonKey(name: 'openTime') required this.openTime,
      @JsonKey(name: 'status') required this.status});

  factory _$MarketStatusInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$MarketStatusInfoImplFromJson(json);

  @override
  @JsonKey(name: 'statusStr')
  final String statusStr;
  @override
  @JsonKey(name: 'closeTime')
  final int closeTime;
  @override
  @JsonKey(name: 'openTime')
  final int openTime;

  /// 市场状态1：盘前交易 2：交易中 3：盘后交易 4：已收盘 6：午间休市 0：待开盘
  @override
  @JsonKey(name: 'status')
  final int status;

  @override
  String toString() {
    return 'MarketStatusInfo(statusStr: $statusStr, closeTime: $closeTime, openTime: $openTime, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MarketStatusInfoImpl &&
            (identical(other.statusStr, statusStr) ||
                other.statusStr == statusStr) &&
            (identical(other.closeTime, closeTime) ||
                other.closeTime == closeTime) &&
            (identical(other.openTime, openTime) ||
                other.openTime == openTime) &&
            (identical(other.status, status) || other.status == status));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, statusStr, closeTime, openTime, status);

  /// Create a copy of MarketStatusInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MarketStatusInfoImplCopyWith<_$MarketStatusInfoImpl> get copyWith =>
      __$$MarketStatusInfoImplCopyWithImpl<_$MarketStatusInfoImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MarketStatusInfoImplToJson(
      this,
    );
  }
}

abstract class _MarketStatusInfo implements MarketStatusInfo {
  const factory _MarketStatusInfo(
          {@JsonKey(name: 'statusStr') required final String statusStr,
          @JsonKey(name: 'closeTime') required final int closeTime,
          @JsonKey(name: 'openTime') required final int openTime,
          @JsonKey(name: 'status') required final int status}) =
      _$MarketStatusInfoImpl;

  factory _MarketStatusInfo.fromJson(Map<String, dynamic> json) =
      _$MarketStatusInfoImpl.fromJson;

  @override
  @JsonKey(name: 'statusStr')
  String get statusStr;
  @override
  @JsonKey(name: 'closeTime')
  int get closeTime;
  @override
  @JsonKey(name: 'openTime')
  int get openTime;

  /// 市场状态1：盘前交易 2：交易中 3：盘后交易 4：已收盘 6：午间休市 0：待开盘
  @override
  @JsonKey(name: 'status')
  int get status;

  /// Create a copy of MarketStatusInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MarketStatusInfoImplCopyWith<_$MarketStatusInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
