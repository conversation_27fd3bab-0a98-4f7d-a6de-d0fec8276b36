import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/api/network/models/result.dart';
import '../../../../core/api/network/network.dart';
import '../../../../core/api/network/network_helper.dart';
import '../../../../shared/constants/enums.dart';
import '../repository/forgot_repository.dart';

@Injectable(as: ForgotRepository)
class ForgotService implements ForgotRepository {
  @override
  Future<ResponseResult<bool>> resetPassword({
    required String mobile,
    required String newPassword,
    required String smsCode,
    required String verifyType,
    required PasswordType passwordType,
  }) async {
    try {
      final Map<String, dynamic> requestData = {
        'mobile': mobile,
        'newPassword': newPassword,
        'smsCode': smsCode,
        'verifyType': verifyType,
        'type': passwordType.value,
      };

      final Response response = await NetworkProvider().post(
        ApiEndpoints.changePassword,
        data: requestData,
      );

      final responseModel = NetworkHelper.mappingResponseData<bool>(response);

      return ResponseResult(
        data: responseModel.data != null ? true : null,
        error: responseModel.data == null ? responseModel.msg : null,
      );
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
