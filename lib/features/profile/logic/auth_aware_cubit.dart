import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/features/sign_in/domain/models/login/login_response.dart';
import 'package:gp_stock_app/features/sign_in/domain/services/auth_service.dart';

abstract class AuthAwareCubit<T extends Object> extends Cubit<T> {
  late StreamSubscription<LoginResponse?> _loginSubscription;

  AuthAwareCubit(super.initialState) {
    _loginSubscription = AuthService.loginStream.listen((loginResponse) {
      if (loginResponse != null) {
        onLoggedIn(loginResponse);
      } else {
        onLoggedOut();
      }
    });
  }

  void onLoggedIn(LoginResponse loginResponse);

  void onLoggedOut();

  @override
  Future<void> close() {
    _loginSubscription.cancel();
    return super.close();
  }
}
