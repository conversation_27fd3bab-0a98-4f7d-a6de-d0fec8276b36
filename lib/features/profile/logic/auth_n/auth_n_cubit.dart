import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/profile/logic/auth_aware_cubit.dart';
import 'package:gp_stock_app/features/sign_in/domain/models/login/login_response.dart';

import 'package:image_picker/image_picker.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/services/image_picker/image_picker_repository.dart';
import '../../../../shared/constants/enums.dart';
import '../../../../shared/models/dropdown/dropdown_value.dart';
import '../../domain/models/auth_n/info/auth_n_info.dart';
import '../../domain/repository/profile_repository.dart';

part 'auth_n_state.dart';

@singleton
class AuthNCubit extends AuthAwareCubit<AuthNState> {
  final ImagePickerRepository _imagePickerService;
  final ProfileRepository _profileService;

  AuthNCubit(this._imagePickerService, this._profileService) : super(const AuthNState());

  @override
  void onLoggedIn(LoginResponse loginResponse) => getAuthNInfo();

  @override
  void onLoggedOut() => emit(const AuthNState());

  void updateDocumentType(DropDownValue? documentType) {
    emit(state.copyWith(documentType: documentType));
  }

  // Check if the user is verified based on the status
  bool get isVerified => state.authNInfo?.status == 1;

  bool get isLoading => state.getAuthNInfoStatus.isLoading;

  // Check if ID card type is selected
  bool get isIdCardType => state.documentType?.code?.toLowerCase() == CardType.id.name.toLowerCase();

  // Check if passport type is selected
  bool get isPassportType => state.documentType?.code?.toLowerCase() == CardType.passport.name.toLowerCase();

  Future<bool> uploadIdentityCard({required File? file, required CardTypeView fileType}) async {
    // Skip upload if file is null
    if (file == null) {
      emit(state.copyWith(uploadStatus: DataStatus.failed, error: 'No image file selected'));
      return false;
    }

    emit(state.copyWith(uploadStatus: DataStatus.loading));

    final response = await _profileService.fileUpload(file: file, type: fileType.name);

    if (response.data != null) {
      try {
        final fileUploadData = response.data;
        final rePath = fileUploadData?.data?.rePath?.replaceAll('.png', '') ?? '';

        // Update state based on file type
        switch (fileType) {
          case CardTypeView.front:
            emit(state.copyWith(rePathFront: rePath));
            break;
          case CardTypeView.back:
            emit(state.copyWith(rePathBack: rePath));
            break;
        }

        emit(state.copyWith(uploadStatus: DataStatus.success));
        return true;
      } catch (e) {
        log('Error parsing response: $e');
        emit(state.copyWith(uploadStatus: DataStatus.failed, error: 'Failed to process upload response'));
        return false;
      }
    } else {
      emit(state.copyWith(uploadStatus: DataStatus.failed, error: response.error));
      return false;
    }
  }

  Future<void> submitCardInformation({
    required String cardNumber,
    required String cardName,
    required String withdrawPassword,
    bool hideDocumentVerification = true,
    required String? bankMobile, // Currently not required
    required String bankCardNumber, // Currently not required
  }) async {
    // Validate input - bankMobile and bankCardNumber are temporarily not required
    if (cardNumber.isEmpty || cardName.isEmpty || withdrawPassword.isEmpty) {
      emit(state.copyWith(submitCardInformationStatus: DataStatus.failed, error: 'pleaseEnterRequiredFields'.tr()));
      return;
    }

    if (!hideDocumentVerification && state.documentType == null) {
      emit(state.copyWith(submitCardInformationStatus: DataStatus.failed, error: 'pleaseSelectDocumentType'.tr()));
      return;
    }

    emit(state.copyWith(submitCardInformationStatus: DataStatus.loading));

    try {
      if (hideDocumentVerification) {
        // Simplified submission without document verification
        final result = await _profileService.authApply(
          idCard: cardNumber,
          realName: cardName,
          certificateType: 1,
          certificateFront: '',
          certificateBack: '',
          withdrawPassword: withdrawPassword.toBase64(),
          bankMobile: bankMobile,
          bankCardNo: bankCardNumber,
        );

        if (result.data != null) {
          emit(state.copyWith(submitCardInformationStatus: DataStatus.success));
        } else {
          emit(state.copyWith(submitCardInformationStatus: DataStatus.failed, error: result.error));
        }
      } else {
        final frontUpload = await uploadIdentityCard(file: state.imageFileFront, fileType: CardTypeView.front);

        if (!frontUpload) return;

        bool backUpload = true;
        if (isIdCardType) {
          backUpload = await uploadIdentityCard(file: state.imageFileBack, fileType: CardTypeView.back);
          if (!backUpload) return;
        }

        final result = await _profileService.authApply(
          certificateFront: state.rePathFront,
          certificateBack: state.rePathBack,
          certificateType: isIdCardType ? 1 : 2,
          idCard: cardNumber,
          realName: cardName,
          withdrawPassword: withdrawPassword.toBase64(),
          bankMobile: bankMobile,
          bankCardNo: cardNumber,
        );

        if (result.data != null) {
          emit(state.copyWith(submitCardInformationStatus: DataStatus.success));
        } else {
          emit(state.copyWith(submitCardInformationStatus: DataStatus.failed, error: result.error));
        }
      }
    } catch (e) {
      emit(state.copyWith(submitCardInformationStatus: DataStatus.failed, error: e.toString()));
    }
  }

  // Simplified image setting methods
  void setImageFileFront(File? imageFile) {
    emit(state.copyWith(imageFileFront: imageFile));
  }

  void setImageFileBack(File? imageFile) {
    emit(state.copyWith(imageFileBack: imageFile));
  }

  Future<void> getCardImage({required CardTypeView type}) async {
    final image = await _imagePickerService.getImage(ImageSource.gallery);

    if (image != null) {
      type == CardTypeView.front ? setImageFileFront(image) : setImageFileBack(image);
    }
  }

  void resetFile() {
    emit(state.copyWith(resetFile: true, imageFileFront: null, imageFileBack: null, rePathFront: '', rePathBack: ''));
  }

  Future<bool> getAuthNInfo({bool shouldRefresh = true}) async {
    if (state.getAuthNInfoStatus.isSuccess && !shouldRefresh) {
      return isVerified;
    }

    emit(state.copyWith(
      getAuthNInfoStatus: DataStatus.loading,
      isRefreshing: shouldRefresh,
    ));

    try {
      final result = await _profileService.authNInfo();

      if (result.data != null) {
        final authInfo = result.data;

        // Only set documentType if certificateType is not 0
        final documentType = (authInfo?.certificateType != null && authInfo?.certificateType != 0)
            ? DropDownValue(
                value: authInfo?.certificateType == 1 ? CardType.id.translationKey : CardType.passport.translationKey,
                id: authInfo?.certificateType == 1 ? '1' : '2',
                code: authInfo?.certificateType == 1 ? CardType.id.name : CardType.passport.name,
              )
            : null;

        emit(state.copyWith(
          getAuthNInfoStatus: DataStatus.success,
          authNInfo: authInfo,
          documentType: documentType,
          error: '',
          isRefreshing: false,
        ));

        return isVerified;
      } else {
        emit(state.copyWith(
          getAuthNInfoStatus: DataStatus.failed,
          error: result.error ?? 'Failed to load authentication information',
          isRefreshing: false,
        ));
        return false;
      }
    } catch (e) {
      log('Error fetching auth info: $e');
      emit(state.copyWith(
        getAuthNInfoStatus: DataStatus.failed,
        error: e.toString(),
        isRefreshing: false,
      ));
      return false;
    }
  }
}
