// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ai_chat_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AIChatModel _$AIChatModelFromJson(Map<String, dynamic> json) {
  return _AIChatModel.fromJson(json);
}

/// @nodoc
mixin _$AIChatModel {
  @JsonKey(name: 'createTime')
  String get createTime => throw _privateConstructorUsedError;
  @JsonKey(name: 'creator')
  String get creator => throw _privateConstructorUsedError;
  @JsonKey(name: 'id')
  int get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'question')
  String get question => throw _privateConstructorUsedError;
  @JsonKey(name: 'reply')
  String get reply => throw _privateConstructorUsedError;
  @JsonKey(name: 'siteId')
  int get siteId => throw _privateConstructorUsedError;
  @JsonKey(name: 'updateTime')
  String get updateTime => throw _privateConstructorUsedError;
  @JsonKey(name: 'updater')
  String get updater => throw _privateConstructorUsedError;
  @JsonKey(name: 'userId')
  int get userId => throw _privateConstructorUsedError;

  /// Serializes this AIChatModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AIChatModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AIChatModelCopyWith<AIChatModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AIChatModelCopyWith<$Res> {
  factory $AIChatModelCopyWith(
          AIChatModel value, $Res Function(AIChatModel) then) =
      _$AIChatModelCopyWithImpl<$Res, AIChatModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'createTime') String createTime,
      @JsonKey(name: 'creator') String creator,
      @JsonKey(name: 'id') int id,
      @JsonKey(name: 'question') String question,
      @JsonKey(name: 'reply') String reply,
      @JsonKey(name: 'siteId') int siteId,
      @JsonKey(name: 'updateTime') String updateTime,
      @JsonKey(name: 'updater') String updater,
      @JsonKey(name: 'userId') int userId});
}

/// @nodoc
class _$AIChatModelCopyWithImpl<$Res, $Val extends AIChatModel>
    implements $AIChatModelCopyWith<$Res> {
  _$AIChatModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AIChatModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? createTime = null,
    Object? creator = null,
    Object? id = null,
    Object? question = null,
    Object? reply = null,
    Object? siteId = null,
    Object? updateTime = null,
    Object? updater = null,
    Object? userId = null,
  }) {
    return _then(_value.copyWith(
      createTime: null == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String,
      creator: null == creator
          ? _value.creator
          : creator // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      question: null == question
          ? _value.question
          : question // ignore: cast_nullable_to_non_nullable
              as String,
      reply: null == reply
          ? _value.reply
          : reply // ignore: cast_nullable_to_non_nullable
              as String,
      siteId: null == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as int,
      updateTime: null == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String,
      updater: null == updater
          ? _value.updater
          : updater // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AIChatModelImplCopyWith<$Res>
    implements $AIChatModelCopyWith<$Res> {
  factory _$$AIChatModelImplCopyWith(
          _$AIChatModelImpl value, $Res Function(_$AIChatModelImpl) then) =
      __$$AIChatModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'createTime') String createTime,
      @JsonKey(name: 'creator') String creator,
      @JsonKey(name: 'id') int id,
      @JsonKey(name: 'question') String question,
      @JsonKey(name: 'reply') String reply,
      @JsonKey(name: 'siteId') int siteId,
      @JsonKey(name: 'updateTime') String updateTime,
      @JsonKey(name: 'updater') String updater,
      @JsonKey(name: 'userId') int userId});
}

/// @nodoc
class __$$AIChatModelImplCopyWithImpl<$Res>
    extends _$AIChatModelCopyWithImpl<$Res, _$AIChatModelImpl>
    implements _$$AIChatModelImplCopyWith<$Res> {
  __$$AIChatModelImplCopyWithImpl(
      _$AIChatModelImpl _value, $Res Function(_$AIChatModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of AIChatModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? createTime = null,
    Object? creator = null,
    Object? id = null,
    Object? question = null,
    Object? reply = null,
    Object? siteId = null,
    Object? updateTime = null,
    Object? updater = null,
    Object? userId = null,
  }) {
    return _then(_$AIChatModelImpl(
      createTime: null == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String,
      creator: null == creator
          ? _value.creator
          : creator // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      question: null == question
          ? _value.question
          : question // ignore: cast_nullable_to_non_nullable
              as String,
      reply: null == reply
          ? _value.reply
          : reply // ignore: cast_nullable_to_non_nullable
              as String,
      siteId: null == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as int,
      updateTime: null == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String,
      updater: null == updater
          ? _value.updater
          : updater // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AIChatModelImpl implements _AIChatModel {
  const _$AIChatModelImpl(
      {@JsonKey(name: 'createTime') required this.createTime,
      @JsonKey(name: 'creator') required this.creator,
      @JsonKey(name: 'id') required this.id,
      @JsonKey(name: 'question') required this.question,
      @JsonKey(name: 'reply') required this.reply,
      @JsonKey(name: 'siteId') required this.siteId,
      @JsonKey(name: 'updateTime') required this.updateTime,
      @JsonKey(name: 'updater') required this.updater,
      @JsonKey(name: 'userId') required this.userId});

  factory _$AIChatModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AIChatModelImplFromJson(json);

  @override
  @JsonKey(name: 'createTime')
  final String createTime;
  @override
  @JsonKey(name: 'creator')
  final String creator;
  @override
  @JsonKey(name: 'id')
  final int id;
  @override
  @JsonKey(name: 'question')
  final String question;
  @override
  @JsonKey(name: 'reply')
  final String reply;
  @override
  @JsonKey(name: 'siteId')
  final int siteId;
  @override
  @JsonKey(name: 'updateTime')
  final String updateTime;
  @override
  @JsonKey(name: 'updater')
  final String updater;
  @override
  @JsonKey(name: 'userId')
  final int userId;

  @override
  String toString() {
    return 'AIChatModel(createTime: $createTime, creator: $creator, id: $id, question: $question, reply: $reply, siteId: $siteId, updateTime: $updateTime, updater: $updater, userId: $userId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AIChatModelImpl &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.creator, creator) || other.creator == creator) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.question, question) ||
                other.question == question) &&
            (identical(other.reply, reply) || other.reply == reply) &&
            (identical(other.siteId, siteId) || other.siteId == siteId) &&
            (identical(other.updateTime, updateTime) ||
                other.updateTime == updateTime) &&
            (identical(other.updater, updater) || other.updater == updater) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, createTime, creator, id,
      question, reply, siteId, updateTime, updater, userId);

  /// Create a copy of AIChatModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AIChatModelImplCopyWith<_$AIChatModelImpl> get copyWith =>
      __$$AIChatModelImplCopyWithImpl<_$AIChatModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AIChatModelImplToJson(
      this,
    );
  }
}

abstract class _AIChatModel implements AIChatModel {
  const factory _AIChatModel(
      {@JsonKey(name: 'createTime') required final String createTime,
      @JsonKey(name: 'creator') required final String creator,
      @JsonKey(name: 'id') required final int id,
      @JsonKey(name: 'question') required final String question,
      @JsonKey(name: 'reply') required final String reply,
      @JsonKey(name: 'siteId') required final int siteId,
      @JsonKey(name: 'updateTime') required final String updateTime,
      @JsonKey(name: 'updater') required final String updater,
      @JsonKey(name: 'userId') required final int userId}) = _$AIChatModelImpl;

  factory _AIChatModel.fromJson(Map<String, dynamic> json) =
      _$AIChatModelImpl.fromJson;

  @override
  @JsonKey(name: 'createTime')
  String get createTime;
  @override
  @JsonKey(name: 'creator')
  String get creator;
  @override
  @JsonKey(name: 'id')
  int get id;
  @override
  @JsonKey(name: 'question')
  String get question;
  @override
  @JsonKey(name: 'reply')
  String get reply;
  @override
  @JsonKey(name: 'siteId')
  int get siteId;
  @override
  @JsonKey(name: 'updateTime')
  String get updateTime;
  @override
  @JsonKey(name: 'updater')
  String get updater;
  @override
  @JsonKey(name: 'userId')
  int get userId;

  /// Create a copy of AIChatModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AIChatModelImplCopyWith<_$AIChatModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
