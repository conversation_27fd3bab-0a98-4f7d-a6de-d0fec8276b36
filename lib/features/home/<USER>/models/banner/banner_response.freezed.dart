// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'banner_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BannerResponse _$BannerResponseFromJson(Map<String, dynamic> json) {
  return _BannerResponse.fromJson(json);
}

/// @nodoc
mixin _$BannerResponse {
  int? get code => throw _privateConstructorUsedError;
  List<BannerData>? get data => throw _privateConstructorUsedError;
  String? get msg => throw _privateConstructorUsedError;

  /// Serializes this BannerResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BannerResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BannerResponseCopyWith<BannerResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BannerResponseCopyWith<$Res> {
  factory $BannerResponseCopyWith(
          BannerResponse value, $Res Function(BannerResponse) then) =
      _$BannerResponseCopyWithImpl<$Res, BannerResponse>;
  @useResult
  $Res call({int? code, List<BannerData>? data, String? msg});
}

/// @nodoc
class _$BannerResponseCopyWithImpl<$Res, $Val extends BannerResponse>
    implements $BannerResponseCopyWith<$Res> {
  _$BannerResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BannerResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
  }) {
    return _then(_value.copyWith(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<BannerData>?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BannerResponseImplCopyWith<$Res>
    implements $BannerResponseCopyWith<$Res> {
  factory _$$BannerResponseImplCopyWith(_$BannerResponseImpl value,
          $Res Function(_$BannerResponseImpl) then) =
      __$$BannerResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? code, List<BannerData>? data, String? msg});
}

/// @nodoc
class __$$BannerResponseImplCopyWithImpl<$Res>
    extends _$BannerResponseCopyWithImpl<$Res, _$BannerResponseImpl>
    implements _$$BannerResponseImplCopyWith<$Res> {
  __$$BannerResponseImplCopyWithImpl(
      _$BannerResponseImpl _value, $Res Function(_$BannerResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of BannerResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
  }) {
    return _then(_$BannerResponseImpl(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<BannerData>?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BannerResponseImpl implements _BannerResponse {
  const _$BannerResponseImpl(
      {this.code, final List<BannerData>? data, this.msg})
      : _data = data;

  factory _$BannerResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$BannerResponseImplFromJson(json);

  @override
  final int? code;
  final List<BannerData>? _data;
  @override
  List<BannerData>? get data {
    final value = _data;
    if (value == null) return null;
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? msg;

  @override
  String toString() {
    return 'BannerResponse(code: $code, data: $data, msg: $msg)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BannerResponseImpl &&
            (identical(other.code, code) || other.code == code) &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.msg, msg) || other.msg == msg));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, code, const DeepCollectionEquality().hash(_data), msg);

  /// Create a copy of BannerResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BannerResponseImplCopyWith<_$BannerResponseImpl> get copyWith =>
      __$$BannerResponseImplCopyWithImpl<_$BannerResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BannerResponseImplToJson(
      this,
    );
  }
}

abstract class _BannerResponse implements BannerResponse {
  const factory _BannerResponse(
      {final int? code,
      final List<BannerData>? data,
      final String? msg}) = _$BannerResponseImpl;

  factory _BannerResponse.fromJson(Map<String, dynamic> json) =
      _$BannerResponseImpl.fromJson;

  @override
  int? get code;
  @override
  List<BannerData>? get data;
  @override
  String? get msg;

  /// Create a copy of BannerResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BannerResponseImplCopyWith<_$BannerResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BannerData _$BannerDataFromJson(Map<String, dynamic> json) {
  return _BannerData.fromJson(json);
}

/// @nodoc
mixin _$BannerData {
  String? get createTime => throw _privateConstructorUsedError;
  String? get creator => throw _privateConstructorUsedError;
  int? get id => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  int? get jumpType => throw _privateConstructorUsedError;
  String? get jumpUrl => throw _privateConstructorUsedError;
  int? get siteId => throw _privateConstructorUsedError;
  int? get sort => throw _privateConstructorUsedError;
  bool? get status => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get updateTime => throw _privateConstructorUsedError;
  String? get updater => throw _privateConstructorUsedError;

  /// Serializes this BannerData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BannerData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BannerDataCopyWith<BannerData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BannerDataCopyWith<$Res> {
  factory $BannerDataCopyWith(
          BannerData value, $Res Function(BannerData) then) =
      _$BannerDataCopyWithImpl<$Res, BannerData>;
  @useResult
  $Res call(
      {String? createTime,
      String? creator,
      int? id,
      String? imageUrl,
      int? jumpType,
      String? jumpUrl,
      int? siteId,
      int? sort,
      bool? status,
      String? title,
      String? updateTime,
      String? updater});
}

/// @nodoc
class _$BannerDataCopyWithImpl<$Res, $Val extends BannerData>
    implements $BannerDataCopyWith<$Res> {
  _$BannerDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BannerData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? createTime = freezed,
    Object? creator = freezed,
    Object? id = freezed,
    Object? imageUrl = freezed,
    Object? jumpType = freezed,
    Object? jumpUrl = freezed,
    Object? siteId = freezed,
    Object? sort = freezed,
    Object? status = freezed,
    Object? title = freezed,
    Object? updateTime = freezed,
    Object? updater = freezed,
  }) {
    return _then(_value.copyWith(
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      creator: freezed == creator
          ? _value.creator
          : creator // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpType: freezed == jumpType
          ? _value.jumpType
          : jumpType // ignore: cast_nullable_to_non_nullable
              as int?,
      jumpUrl: freezed == jumpUrl
          ? _value.jumpUrl
          : jumpUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      siteId: freezed == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as int?,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as bool?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      updateTime: freezed == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      updater: freezed == updater
          ? _value.updater
          : updater // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BannerDataImplCopyWith<$Res>
    implements $BannerDataCopyWith<$Res> {
  factory _$$BannerDataImplCopyWith(
          _$BannerDataImpl value, $Res Function(_$BannerDataImpl) then) =
      __$$BannerDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? createTime,
      String? creator,
      int? id,
      String? imageUrl,
      int? jumpType,
      String? jumpUrl,
      int? siteId,
      int? sort,
      bool? status,
      String? title,
      String? updateTime,
      String? updater});
}

/// @nodoc
class __$$BannerDataImplCopyWithImpl<$Res>
    extends _$BannerDataCopyWithImpl<$Res, _$BannerDataImpl>
    implements _$$BannerDataImplCopyWith<$Res> {
  __$$BannerDataImplCopyWithImpl(
      _$BannerDataImpl _value, $Res Function(_$BannerDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of BannerData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? createTime = freezed,
    Object? creator = freezed,
    Object? id = freezed,
    Object? imageUrl = freezed,
    Object? jumpType = freezed,
    Object? jumpUrl = freezed,
    Object? siteId = freezed,
    Object? sort = freezed,
    Object? status = freezed,
    Object? title = freezed,
    Object? updateTime = freezed,
    Object? updater = freezed,
  }) {
    return _then(_$BannerDataImpl(
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      creator: freezed == creator
          ? _value.creator
          : creator // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpType: freezed == jumpType
          ? _value.jumpType
          : jumpType // ignore: cast_nullable_to_non_nullable
              as int?,
      jumpUrl: freezed == jumpUrl
          ? _value.jumpUrl
          : jumpUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      siteId: freezed == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as int?,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as bool?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      updateTime: freezed == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      updater: freezed == updater
          ? _value.updater
          : updater // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BannerDataImpl implements _BannerData {
  const _$BannerDataImpl(
      {this.createTime,
      this.creator,
      this.id,
      this.imageUrl,
      this.jumpType,
      this.jumpUrl,
      this.siteId,
      this.sort,
      this.status,
      this.title,
      this.updateTime,
      this.updater});

  factory _$BannerDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$BannerDataImplFromJson(json);

  @override
  final String? createTime;
  @override
  final String? creator;
  @override
  final int? id;
  @override
  final String? imageUrl;
  @override
  final int? jumpType;
  @override
  final String? jumpUrl;
  @override
  final int? siteId;
  @override
  final int? sort;
  @override
  final bool? status;
  @override
  final String? title;
  @override
  final String? updateTime;
  @override
  final String? updater;

  @override
  String toString() {
    return 'BannerData(createTime: $createTime, creator: $creator, id: $id, imageUrl: $imageUrl, jumpType: $jumpType, jumpUrl: $jumpUrl, siteId: $siteId, sort: $sort, status: $status, title: $title, updateTime: $updateTime, updater: $updater)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BannerDataImpl &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.creator, creator) || other.creator == creator) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.jumpType, jumpType) ||
                other.jumpType == jumpType) &&
            (identical(other.jumpUrl, jumpUrl) || other.jumpUrl == jumpUrl) &&
            (identical(other.siteId, siteId) || other.siteId == siteId) &&
            (identical(other.sort, sort) || other.sort == sort) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.updateTime, updateTime) ||
                other.updateTime == updateTime) &&
            (identical(other.updater, updater) || other.updater == updater));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      createTime,
      creator,
      id,
      imageUrl,
      jumpType,
      jumpUrl,
      siteId,
      sort,
      status,
      title,
      updateTime,
      updater);

  /// Create a copy of BannerData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BannerDataImplCopyWith<_$BannerDataImpl> get copyWith =>
      __$$BannerDataImplCopyWithImpl<_$BannerDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BannerDataImplToJson(
      this,
    );
  }
}

abstract class _BannerData implements BannerData {
  const factory _BannerData(
      {final String? createTime,
      final String? creator,
      final int? id,
      final String? imageUrl,
      final int? jumpType,
      final String? jumpUrl,
      final int? siteId,
      final int? sort,
      final bool? status,
      final String? title,
      final String? updateTime,
      final String? updater}) = _$BannerDataImpl;

  factory _BannerData.fromJson(Map<String, dynamic> json) =
      _$BannerDataImpl.fromJson;

  @override
  String? get createTime;
  @override
  String? get creator;
  @override
  int? get id;
  @override
  String? get imageUrl;
  @override
  int? get jumpType;
  @override
  String? get jumpUrl;
  @override
  int? get siteId;
  @override
  int? get sort;
  @override
  bool? get status;
  @override
  String? get title;
  @override
  String? get updateTime;
  @override
  String? get updater;

  /// Create a copy of BannerData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BannerDataImplCopyWith<_$BannerDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
