// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'home_notification_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

HomeNotificationModel _$HomeNotificationModelFromJson(
    Map<String, dynamic> json) {
  return _HomeNotificationModel.fromJson(json);
}

/// @nodoc
mixin _$HomeNotificationModel {
  int get id => throw _privateConstructorUsedError;
  String get content => throw _privateConstructorUsedError;
  String get endTime => throw _privateConstructorUsedError;
  String get imageUrl => throw _privateConstructorUsedError;
  int? get jumpType => throw _privateConstructorUsedError;
  String? get jumpUrl => throw _privateConstructorUsedError;
  int get siteId => throw _privateConstructorUsedError;
  String get startTime => throw _privateConstructorUsedError;
  bool get status => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  int get type => throw _privateConstructorUsedError;

  /// Serializes this HomeNotificationModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of HomeNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HomeNotificationModelCopyWith<HomeNotificationModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HomeNotificationModelCopyWith<$Res> {
  factory $HomeNotificationModelCopyWith(HomeNotificationModel value,
          $Res Function(HomeNotificationModel) then) =
      _$HomeNotificationModelCopyWithImpl<$Res, HomeNotificationModel>;
  @useResult
  $Res call(
      {int id,
      String content,
      String endTime,
      String imageUrl,
      int? jumpType,
      String? jumpUrl,
      int siteId,
      String startTime,
      bool status,
      String title,
      int type});
}

/// @nodoc
class _$HomeNotificationModelCopyWithImpl<$Res,
        $Val extends HomeNotificationModel>
    implements $HomeNotificationModelCopyWith<$Res> {
  _$HomeNotificationModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HomeNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? content = null,
    Object? endTime = null,
    Object? imageUrl = null,
    Object? jumpType = freezed,
    Object? jumpUrl = freezed,
    Object? siteId = null,
    Object? startTime = null,
    Object? status = null,
    Object? title = null,
    Object? type = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      endTime: null == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      jumpType: freezed == jumpType
          ? _value.jumpType
          : jumpType // ignore: cast_nullable_to_non_nullable
              as int?,
      jumpUrl: freezed == jumpUrl
          ? _value.jumpUrl
          : jumpUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      siteId: null == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as int,
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as bool,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$HomeNotificationModelImplCopyWith<$Res>
    implements $HomeNotificationModelCopyWith<$Res> {
  factory _$$HomeNotificationModelImplCopyWith(
          _$HomeNotificationModelImpl value,
          $Res Function(_$HomeNotificationModelImpl) then) =
      __$$HomeNotificationModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String content,
      String endTime,
      String imageUrl,
      int? jumpType,
      String? jumpUrl,
      int siteId,
      String startTime,
      bool status,
      String title,
      int type});
}

/// @nodoc
class __$$HomeNotificationModelImplCopyWithImpl<$Res>
    extends _$HomeNotificationModelCopyWithImpl<$Res,
        _$HomeNotificationModelImpl>
    implements _$$HomeNotificationModelImplCopyWith<$Res> {
  __$$HomeNotificationModelImplCopyWithImpl(_$HomeNotificationModelImpl _value,
      $Res Function(_$HomeNotificationModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of HomeNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? content = null,
    Object? endTime = null,
    Object? imageUrl = null,
    Object? jumpType = freezed,
    Object? jumpUrl = freezed,
    Object? siteId = null,
    Object? startTime = null,
    Object? status = null,
    Object? title = null,
    Object? type = null,
  }) {
    return _then(_$HomeNotificationModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      endTime: null == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      jumpType: freezed == jumpType
          ? _value.jumpType
          : jumpType // ignore: cast_nullable_to_non_nullable
              as int?,
      jumpUrl: freezed == jumpUrl
          ? _value.jumpUrl
          : jumpUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      siteId: null == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as int,
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as bool,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$HomeNotificationModelImpl implements _HomeNotificationModel {
  const _$HomeNotificationModelImpl(
      {required this.id,
      required this.content,
      required this.endTime,
      required this.imageUrl,
      required this.jumpType,
      required this.jumpUrl,
      required this.siteId,
      required this.startTime,
      required this.status,
      required this.title,
      required this.type});

  factory _$HomeNotificationModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$HomeNotificationModelImplFromJson(json);

  @override
  final int id;
  @override
  final String content;
  @override
  final String endTime;
  @override
  final String imageUrl;
  @override
  final int? jumpType;
  @override
  final String? jumpUrl;
  @override
  final int siteId;
  @override
  final String startTime;
  @override
  final bool status;
  @override
  final String title;
  @override
  final int type;

  @override
  String toString() {
    return 'HomeNotificationModel(id: $id, content: $content, endTime: $endTime, imageUrl: $imageUrl, jumpType: $jumpType, jumpUrl: $jumpUrl, siteId: $siteId, startTime: $startTime, status: $status, title: $title, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HomeNotificationModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.jumpType, jumpType) ||
                other.jumpType == jumpType) &&
            (identical(other.jumpUrl, jumpUrl) || other.jumpUrl == jumpUrl) &&
            (identical(other.siteId, siteId) || other.siteId == siteId) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, content, endTime, imageUrl,
      jumpType, jumpUrl, siteId, startTime, status, title, type);

  /// Create a copy of HomeNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HomeNotificationModelImplCopyWith<_$HomeNotificationModelImpl>
      get copyWith => __$$HomeNotificationModelImplCopyWithImpl<
          _$HomeNotificationModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$HomeNotificationModelImplToJson(
      this,
    );
  }
}

abstract class _HomeNotificationModel implements HomeNotificationModel {
  const factory _HomeNotificationModel(
      {required final int id,
      required final String content,
      required final String endTime,
      required final String imageUrl,
      required final int? jumpType,
      required final String? jumpUrl,
      required final int siteId,
      required final String startTime,
      required final bool status,
      required final String title,
      required final int type}) = _$HomeNotificationModelImpl;

  factory _HomeNotificationModel.fromJson(Map<String, dynamic> json) =
      _$HomeNotificationModelImpl.fromJson;

  @override
  int get id;
  @override
  String get content;
  @override
  String get endTime;
  @override
  String get imageUrl;
  @override
  int? get jumpType;
  @override
  String? get jumpUrl;
  @override
  int get siteId;
  @override
  String get startTime;
  @override
  bool get status;
  @override
  String get title;
  @override
  int get type;

  /// Create a copy of HomeNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HomeNotificationModelImplCopyWith<_$HomeNotificationModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
