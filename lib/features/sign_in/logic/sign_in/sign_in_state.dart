part of 'sign_in_cubit.dart';

class SignInState extends Equatable {
  final int selectedTab;
  final LoginResponse? loginResponse;
  final DataStatus loginFetchStatus;
  final bool isRememberPassword;
  final bool isAcceptedTerms;
  final String? token;
  final String? error;
  final DataStatus otpReqStatus;
  final dynamic globalConfig;
  const SignInState({
    this.selectedTab = 0,
    this.loginResponse,
    this.loginFetchStatus = DataStatus.idle,
    this.isRememberPassword = false,
    this.isAcceptedTerms = true,
    this.token,
    this.error,
    this.otpReqStatus = DataStatus.idle,
    this.globalConfig,
  });

  bool get isSignedIn => loginResponse != null;

  @override
  List<Object?> get props =>
      [selectedTab, loginResponse, loginFetchStatus, isRememberPassword, isAcceptedTerms, otpReqStatus, globalConfig];

  SignInState copyWith({
    int? selectedTab,
    LoginResponse? loginResponse,
    DataStatus? loginFetchStatus,
    String? token,
    String? error,
    bool? isRememberPassword,
    bool? isAcceptedTerms = true,
    bool? clearData = false,
    DataStatus? otpReqStatus,
    dynamic globalConfig,
  }) {
    return SignInState(
      selectedTab: selectedTab ?? this.selectedTab,
      loginResponse: clearData == true ? null : loginResponse ?? this.loginResponse,
      loginFetchStatus: clearData == true ? DataStatus.idle : loginFetchStatus ?? this.loginFetchStatus,
      token: clearData == true ? null : token ?? this.token,
      error: error ?? this.error,
      isRememberPassword: isRememberPassword ?? this.isRememberPassword,
      isAcceptedTerms: isAcceptedTerms ?? this.isAcceptedTerms,
      otpReqStatus: otpReqStatus ?? this.otpReqStatus,
      globalConfig: globalConfig ?? this.globalConfig,
    );
  }
}
