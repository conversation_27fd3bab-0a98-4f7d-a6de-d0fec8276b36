import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/core/utils/secure_storage_helper.dart';
import 'package:gp_stock_app/core/utils/wangyi_captcha_util.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/keys.dart';
import 'package:gp_stock_app/shared/services/web_socket/web_scoket_interface.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/utils/auth/auth_utils.dart';
import '../../../../shared/constants/enums.dart';
import '../../domain/models/login/login_response.dart';
import '../../domain/repository/sign_in_repository.dart';

part 'sign_in_state.dart';

@lazySingleton
class SignInCubit extends Cubit<SignInState> {
  final SignInRepository _signInService;
  final WebSocketService _webSocketService;

  SignInCubit(this._signInService, this._webSocketService) : super(const SignInState());

  void selectTab(int index) {
    emit(state.copyWith(selectedTab: index));
  }

  bool get isLoggedIn => state.isSignedIn;

  Future<void> login({
    required String username,
    required String password,
    required String mobile,
    required String smsCode,
  }) async {
    emit(state.copyWith(loginFetchStatus: DataStatus.loading));
    final loginMode = LoginMode.values[state.selectedTab];
    final accountName = _getAccountName(loginMode, username, mobile);

    if (await _isCaptchaRequired()) {
      _handleCaptcha(accountName, password, smsCode);
    } else {
      _commitLogin(accountName, password, smsCode);
    }
  }

  String _getAccountName(LoginMode loginMode, String username, String mobile) {
    return loginMode == LoginMode.account ? username : mobile;
  }

  Future<bool> _isCaptchaRequired() async {
    return await _signInService.requestWangYiCaptchaRequired();
  }

  void _handleCaptcha(String accountName, String password, String smsCode) {
    WangYiCaptchaUtil().show(
      captchaId: AppConfig.instance.wangYiCaptchaKey,
      account: accountName,
      onSuccess: (code) => _commitLogin(accountName, password, smsCode, validate: code),
      onValidateFailClose: _onCaptchaValidationFailed,
      onError: _onCaptchaError,
    );
  }

  void _onCaptchaValidationFailed() {
    if (!isClosed) {
      emit(state.copyWith(loginFetchStatus: DataStatus.idle));
    }
  }

  void _onCaptchaError() {
    if (!isClosed) {
      GPEasyLoading.showToast('验证失败'.tr());
      emit(state.copyWith(loginFetchStatus: DataStatus.failed));
    }
  }

  Future<void> _commitLogin(String accountName, String password, String smsCode, {String? validate}) async {
    try {
      final result = await _signInService.login(
        mobile: accountName,
        password: password,
        mode: LoginMode.values[state.selectedTab],
        smsCode: smsCode,
        validate: validate,
      );
      _handleLoginResult(result, accountName, password);
    } on Exception catch (e) {
      emit(state.copyWith(loginFetchStatus: DataStatus.failed, error: e.toString()));
    }
  }

  void _handleLoginResult(ResponseResult<LoginResponse> result, String accountName, String password) {
    if (result.isSuccess) {
      _onLoginSuccess(result, accountName, password);
    } else {
      emit(state.copyWith(loginFetchStatus: DataStatus.failed, error: result.error));
    }
  }

  Future<void> _onLoginSuccess(ResponseResult<LoginResponse> result, String accountName, String password) async {
    await _updateLocalStorage(result);
    if (_shouldRememberPassword()) {
      await AuthUtils.instance.rememberPassword(true, username: accountName, password: password);
    }
    _signInService.addLoginResponse(result.data);
    _webSocketService.send({'type': "auth", 'data': result.token});
    emit(state.copyWith(loginFetchStatus: DataStatus.success, loginResponse: result.data, token: result.token));
  }

  bool _shouldRememberPassword() {
    return LoginMode.values[state.selectedTab] == LoginMode.account && state.isRememberPassword;
  }

  void init() {
    SecureStorageHelper().readSecureData(LocalStorageKeys.user).then((value) async {
      if (value != null) {
        final token = await SecureStorageHelper().readSecureData(LocalStorageKeys.token);
        final userData = UserData.fromJson(jsonDecode(value));
        _signInService.addLoginResponse(LoginResponse(data: userData));
        _webSocketService.send({
          'type': "auth",
          'data': token,
        });
        emit(
          state.copyWith(
            loginFetchStatus: DataStatus.success,
            loginResponse: LoginResponse(
              data: userData,
            ),
            token: token,
          ),
        );
      }
    });
  }

  Future<void> logout() async {
    await SecureStorageHelper().deleteSecureData(LocalStorageKeys.token);
    await SecureStorageHelper().deleteAllExcept();
    AuthUtils.instance.notifyUpdate(token: null);
    _signInService.addLoginResponse(null);
    NetworkProvider.clearCache();
    emit(state.copyWith(clearData: true));
  }

  Future<void> rememberPassword(bool value, {String? username, String? password}) async {
    emit(state.copyWith(isRememberPassword: value));
    if (value) {
      await AuthUtils.instance.rememberPassword(value, username: username, password: password);
    } else {
      await AuthUtils.instance.clearRememberedCredentials();
    }
  }

  Future<(bool, String?, String?)> getRememberPassword() async {
    final value = await AuthUtils.instance.isRememberPassword;
    emit(state.copyWith(isRememberPassword: value.$1));
    return value;
  }

  void acceptTerms(bool value) {
    emit(state.copyWith(isAcceptedTerms: value));
  }

  Future<void> _updateLocalStorage(ResponseResult<LoginResponse>? loginResponse) async {
    if (loginResponse != null) {
      final userData = loginResponse.data?.data ?? UserData();
      await SecureStorageHelper().writeSecureData(LocalStorageKeys.user, jsonEncode(userData));
      await SecureStorageHelper().writeSecureData(LocalStorageKeys.token, loginResponse.token ?? '');
      AuthUtils.instance.notifyUpdate(token: loginResponse.token);
    }
  }

  Future<void> requestOTP({required String phoneNumber, required String sendType}) async {
    emit(state.copyWith(otpReqStatus: DataStatus.loading));
    try {
      final result = await _signInService.requestOTP(phoneNumber: phoneNumber, sendType: sendType);
      if (result.isSuccess) {
        emit(state.copyWith(otpReqStatus: DataStatus.success));
      } else {
        emit(state.copyWith(otpReqStatus: DataStatus.failed, error: result.error));
      }
    } on Exception catch (e) {
      emit(state.copyWith(otpReqStatus: DataStatus.failed, error: e.toString()));
    }
  }
}
