import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/core/utils/secure_storage_helper.dart';
import 'package:gp_stock_app/core/utils/wangyi_captcha_util.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/keys.dart';
import 'package:gp_stock_app/shared/services/web_socket/web_scoket_interface.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/utils/auth/auth_utils.dart';
import '../../../../shared/constants/enums.dart';
import '../../domain/models/login/login_response.dart';
import '../../domain/services/auth_service.dart';

part 'sign_in_state.dart';

@lazySingleton
class SignInCubit extends Cubit<SignInState> {
  final WebSocketService _webSocketService;

  SignInCubit(this._webSocketService) : super(const SignInState());

  void selectTab(int index) {
    emit(state.copyWith(selectedTab: index));
  }

  bool get isLoggedIn => state.isSignedIn;

  /// Login method, used for login
  /// 登录方法，用于登录
  Future<void> login({
    required String username,
    required String password,
    required String mobile,
    required String smsCode,
  }) async {
    emit(state.copyWith(loginFetchStatus: DataStatus.loading));
    final loginMode = LoginMode.values[state.selectedTab];
    final accountName = _getAccountName(loginMode, username, mobile);

    if (await _isCaptchaRequired()) {
      _handleCaptcha(accountName, password, smsCode);
    } else {
      _commitLogin(accountName, password, smsCode);
    }
  }

  /// Get account name method, used for getting account name
  /// 获取账号名称方法，用于获取账号名称
  String _getAccountName(LoginMode loginMode, String username, String mobile) {
    return loginMode == LoginMode.account ? username : mobile;
  }

  /// Is captcha required method, used for checking if captcha is required
  /// 检查验证码是否需要方法，用于检查验证码是否需要
  Future<bool> _isCaptchaRequired() async {
    return await AuthService.requestWangYiCaptchaRequired();
  }

  /// Handle captcha method, used for handling captcha
  /// 处理验证码方法，用于处理验证码
  void _handleCaptcha(String accountName, String password, String smsCode) {
    WangYiCaptchaUtil().show(
      captchaId: AppConfig.instance.wangYiCaptchaKey,
      account: accountName,
      onSuccess: (code) => _commitLogin(accountName, password, smsCode, validate: code),
      onValidateFailClose: _onCaptchaValidationFailed,
      onError: _onCaptchaError,
    );
  }

  /// On captcha validation failed method, used for handling captcha validation failed
  /// 处理验证码验证失败方法，用于处理验证码验证失败
  void _onCaptchaValidationFailed() {
    if (!isClosed) {
      emit(state.copyWith(loginFetchStatus: DataStatus.idle));
    }
  }

  /// On captcha error method, used for handling captcha error
  /// 处理验证码错误方法，用于处理验证码错误
  void _onCaptchaError() {
    if (!isClosed) {
      GPEasyLoading.showToast('验证失败'.tr());
      emit(state.copyWith(loginFetchStatus: DataStatus.failed));
    }
  }

  /// Commit login method, used for committing login API call
  /// 提交登录方法，用于提交登录API调用
  Future<void> _commitLogin(String accountName, String password, String smsCode, {String? validate}) async {
    try {
      final result = await AuthService.login(
        mobile: accountName,
        password: password,
        mode: LoginMode.values[state.selectedTab],
        smsCode: smsCode,
        validate: validate,
      );
      _handleLoginResult(result, accountName, password);
    } on Exception catch (e) {
      emit(state.copyWith(loginFetchStatus: DataStatus.failed, error: e.toString()));
    }
  }

  /// Handle login result method, used for handling login result
  /// 处理登录结果方法，用于处理登录结果
  void _handleLoginResult(ResponseResult<LoginResponse?> result, String accountName, String password) {
    if (result.isSuccess) {
      _onLoginSuccess(result, accountName, password);
    } else {
      emit(state.copyWith(loginFetchStatus: DataStatus.failed, error: result.error));
    }
  }

  /// On login success method, used for handling login success
  /// 处理登录成功方法，用于处理登录成功
  Future<void> _onLoginSuccess(ResponseResult<LoginResponse?> result, String accountName, String password) async {
    await _updateLocalStorage(result);
    if (_shouldRememberPassword()) {
      await AuthUtils.instance.rememberPassword(true, username: accountName, password: password);
    }
    AuthService.addLoginResponse(result.data);
    _webSocketService.send({'type': "auth", 'data': result.token});
    emit(state.copyWith(loginFetchStatus: DataStatus.success, loginResponse: result.data, token: result.token));
  }

  /// Should remember password method, used for checking if should remember password
  /// 检查是否应该记住密码方法，用于检查是否应该记住密码
  bool _shouldRememberPassword() {
    return LoginMode.values[state.selectedTab] == LoginMode.account && state.isRememberPassword;
  }

  /// Init method, used for initializing
  /// 初始化方法，用于初始化
  void init() {
    SecureStorageHelper().readSecureData(LocalStorageKeys.user).then((value) async {
      if (value != null) {
        final token = await SecureStorageHelper().readSecureData(LocalStorageKeys.token);
        final userData = UserData.fromJson(jsonDecode(value));
        AuthService.addLoginResponse(LoginResponse()..data = userData);
        _webSocketService.send({
          'type': "auth",
          'data': token,
        });
        emit(
          state.copyWith(
            loginFetchStatus: DataStatus.success,
            loginResponse: LoginResponse()..data = userData,
            token: token,
          ),
        );
      }
    });
  }

  /// Logout method, used for logging out
  /// 登出方法，用于登出
  Future<void> logout() async {
    await SecureStorageHelper().deleteSecureData(LocalStorageKeys.token);
    await SecureStorageHelper().deleteAllExcept();
    AuthUtils.instance.notifyUpdate(token: null);
    AuthService.addLoginResponse(null);
    NetworkProvider.clearCache();
    emit(state.copyWith(clearData: true));
  }

  /// Remember password method, used for remembering password
  /// 记住密码方法，用于记住密码
  Future<void> rememberPassword(bool value, {String? username, String? password}) async {
    emit(state.copyWith(isRememberPassword: value));
    if (value) {
      await AuthUtils.instance.rememberPassword(value, username: username, password: password);
    } else {
      await AuthUtils.instance.clearRememberedCredentials();
    }
  }

  /// Get remember password method, used for getting remember password
  /// 获取记住密码方法，用于获取记住密码
  Future<(bool, String?, String?)> getRememberPassword() async {
    final value = await AuthUtils.instance.isRememberPassword;
    emit(state.copyWith(isRememberPassword: value.$1));
    return value;
  }

  /// Accept terms method, used for accepting terms
  /// 接受条款方法，用于接受条款
  void acceptTerms(bool value) {
    emit(state.copyWith(isAcceptedTerms: value));
  }

  /// Update local storage method, used for updating local storage
  /// 更新本地存储方法，用于更新本地存储
  Future<void> _updateLocalStorage(ResponseResult<LoginResponse?> loginResponse) async {
    if (loginResponse.isSuccess) {
      final userData = loginResponse.data?.data ?? UserData();
      await SecureStorageHelper().writeSecureData(LocalStorageKeys.user, jsonEncode(userData));
      await SecureStorageHelper().writeSecureData(LocalStorageKeys.token, loginResponse.token ?? '');
      AuthUtils.instance.notifyUpdate(token: loginResponse.token);
    }
  }

  /// Request OTP method, used for requesting OTP
  /// 请求OTP方法，用于请求OTP
  Future<void> requestOTP({required String phoneNumber, required String sendType}) async {
    emit(state.copyWith(otpReqStatus: DataStatus.loading));
    try {
      final result = await AuthService.requestOTP(phoneNumber: phoneNumber, sendType: sendType);
      if (result.isSuccess) {
        emit(state.copyWith(otpReqStatus: DataStatus.success));
      } else {
        emit(state.copyWith(otpReqStatus: DataStatus.failed, error: result.error));
      }
    } on Exception catch (e) {
      emit(state.copyWith(otpReqStatus: DataStatus.failed, error: e.toString()));
    }
  }
}
