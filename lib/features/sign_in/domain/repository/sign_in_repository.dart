import 'dart:async';

import 'package:gp_stock_app/shared/constants/enums.dart';

import '../../../../core/api/network/models/result.dart';
import '../models/login/login_response.dart';

abstract class SignInRepository {
  const SignInRepository();

  Future<ResponseResult<LoginResponse?>> login({
    required String mobile,
    required String password,
    required String smsCode,
    required LoginMode mode,
    String? validate,
  });

  Future<ResponseResult<bool>> logout();

  Stream<LoginResponse?> get loginStream;

  void addLoginResponse(LoginResponse? loginResponse);

  Future<ResponseResult<bool>> requestOTP({
    required String phoneNumber,
    required String sendType,
  });

  Future<bool> requestWangYiCaptchaRequired();

}
