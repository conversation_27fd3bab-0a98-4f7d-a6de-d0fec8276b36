import 'dart:async';

import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:injectable/injectable.dart';
import 'package:rxdart/rxdart.dart';

import '../../../../core/api/network/models/result.dart';
import 'package:gp_stock_app/core/models/apis/auth.dart';
import '../models/login/login_response.dart';
import '../repository/sign_in_repository.dart';

@Singleton(as: SignInRepository)
class SignInService implements SignInRepository {
  final BehaviorSubject<LoginResponse?> _loginController = BehaviorSubject<LoginResponse?>();

  @override
  Stream<LoginResponse?> get loginStream => _loginController.stream;

  @override
  void addLoginResponse(LoginResponse? loginResponse) {
    _loginController.add(loginResponse);
  }

  @override
  Future<ResponseResult<LoginResponse>> login({
    required String mobile,
    required String password,
    required String smsCode,
    required LoginMode mode,
    String? validate,
  }) async {
    return await AuthApi.login(
      mobile: mobile,
      password: password,
      smsCode: smsCode,
      mode: mode,
      validate: validate,
    );
  }

  @override
  Future<ResponseResult<bool>> logout() async {
    return await AuthApi.logout();
  }

  @override
  Future<ResponseResult<bool>> requestOTP({
    required String phoneNumber,
    required String sendType,
  }) async {
    return await AuthApi.requestOTP(
      phoneNumber: phoneNumber,
      sendType: sendType,
    );
  }

  @override
  Future<bool> requestWangYiCaptchaRequired() async {
    return await AuthApi.requestWangYiCaptchaRequired();
  }
}
