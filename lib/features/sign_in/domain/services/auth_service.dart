import 'dart:async';

import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:rxdart/rxdart.dart';

import '../../../../core/api/network/models/result.dart';
import 'package:gp_stock_app/core/models/apis/auth.dart';
import '../models/login/login_response.dart';

class AuthService {
  static final BehaviorSubject<LoginResponse?> _loginController = BehaviorSubject<LoginResponse?>();

  /// Static login stream for direct access
  /// 静态登录流，用于直接访问
  static Stream<LoginResponse?> get loginStream => _loginController.stream;

  /// Static method to add login response
  /// 静态方法，用于添加登录响应
  static void addLoginResponse(LoginResponse? loginResponse) {
    _loginController.add(loginResponse);
  }

  /// Static login method
  /// 静态登录方法，用于登录
  static Future<ResponseResult<LoginResponse?>> login({
    required String mobile,
    required String password,
    required String smsCode,
    required LoginMode mode,
    String? validate,
  }) async {
    return await AuthApi.login(
      mobile: mobile,
      password: password,
      smsCode: smsCode,
      mode: mode,
      validate: validate,
    );
  }

  /// Static logout method
  /// 静态登出方法，用于登出
  static Future<ResponseResult<bool>> logout() async {
    return await AuthApi.logout();
  }

  /// Static request OTP method
  /// 静态请求OTP方法，用于请求OTP
  static Future<ResponseResult<bool>> requestOTP({
    required String phoneNumber,
    required String sendType,
  }) async {
    return await AuthApi.requestOTP(
      phoneNumber: phoneNumber,
      sendType: sendType,
    );
  }

  /// Static check captcha required method
  /// 静态检查验证码是否需要方法，用于检查验证码是否需要
  static Future<bool> requestWangYiCaptchaRequired() async {
    return await AuthApi.requestWangYiCaptchaRequired();
  }
}
