import 'dart:async';

import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:rxdart/rxdart.dart';

import '../../../../core/api/network/models/result.dart';
import 'package:gp_stock_app/core/models/apis/auth.dart';
import '../models/login/login_response.dart';

/// Static AuthService class similar to FTradeService
/// Provides static methods for authentication operations
class AuthService {
  static final BehaviorSubject<LoginResponse?> _loginController = BehaviorSubject<LoginResponse?>();

  /// Static login stream for direct access
  static Stream<LoginResponse?> get loginStream => _loginController.stream;

  /// Static method to add login response
  static void addLoginResponse(LoginResponse? loginResponse) {
    _loginController.add(loginResponse);
  }

  /// Static login method like FTradeService.fetchFTradeConfig
  static Future<ResponseResult<LoginResponse?>> login({
    required String mobile,
    required String password,
    required String smsCode,
    required LoginMode mode,
    String? validate,
  }) async {
    return await AuthApi.login(
      mobile: mobile,
      password: password,
      smsCode: smsCode,
      mode: mode,
      validate: validate,
    );
  }

  /// Static logout method
  static Future<ResponseResult<bool>> logout() async {
    return await AuthApi.logout();
  }

  /// Static request OTP method
  static Future<ResponseResult<bool>> requestOTP({
    required String phoneNumber,
    required String sendType,
  }) async {
    return await AuthApi.requestOTP(
      phoneNumber: phoneNumber,
      sendType: sendType,
    );
  }

  /// Static check captcha required method
  static Future<bool> requestWangYiCaptchaRequired() async {
    return await AuthApi.requestWangYiCaptchaRequired();
  }
}
