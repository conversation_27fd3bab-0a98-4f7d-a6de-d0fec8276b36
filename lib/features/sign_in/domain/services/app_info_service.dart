import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/api/network/endpoint/api_endpoints.dart';
import '../../../../core/api/network/models/result.dart';
import '../../../../core/api/network/network.dart';
import '../../../../core/api/network/network_helper.dart';
import '../models/app_info/app_info.dart';
import '../repository/app_info_repository.dart';

@Injectable(as: AppInfoRepository)
class AppInfoService implements AppInfoRepository {
  @override
  Future<ResponseResult<AppInfo>> getInfoById(int id) async {
    try {
      final response = await NetworkProvider().get(
        ApiEndpoints.appInfo,
        queryParameters: {'id': id},
      );

      final responseModel = NetworkHelper.mappingResponseData<AppInfo>(response);

      return ResponseResult(
        data: responseModel.data,
        error: responseModel.msg,
      );
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
