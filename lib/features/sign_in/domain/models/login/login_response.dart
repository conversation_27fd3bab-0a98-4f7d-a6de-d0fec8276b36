import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/login_response.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/login_response.g.dart';

@JsonSerializable()
class LoginResponse {
  int? code;
  UserData? data;
  String? msg;
  String? token;

  LoginResponse();

  factory LoginResponse.fromJson(Map<String, dynamic> json) => $LoginResponseFromJson(json);

  Map<String, dynamic> toJson() => $LoginResponseToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class UserData {
  bool? auth;
  int? authStatus;
  String? avatar;
  String? countryCode;
  String? email;
  int? fromType;
  int? id;
  String? idCard;
  String? inviteCode;
  bool? isPayment;
  int? level;
  String? mobile;
  String? nickname;
  int? pid;
  String? profiles;
  String? realName;
  int? score;
  int? sex;
  bool? status;
  int? tradeStatus;
  int? type;

  UserData();

  factory UserData.fromJson(Map<String, dynamic> json) => $UserDataFromJson(json);

  Map<String, dynamic> toJson() => $UserDataToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
