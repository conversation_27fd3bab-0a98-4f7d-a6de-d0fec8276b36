import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/app_info.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/app_info.g.dart';

@JsonSerializable()
class AppInfo {
  String? content;
  int? id;
  String? title;

  AppInfo();

  factory AppInfo.fromJson(Map<String, dynamic> json) => $AppInfoFromJson(json);

  Map<String, dynamic> toJson() => $AppInfoToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
