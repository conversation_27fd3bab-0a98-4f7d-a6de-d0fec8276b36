import '../../../../core/models/apis/tasks.dart';

import '../../../../core/api/network/models/result.dart';
import '../models/reward/reward.dart';
import '../models/tasks/tasks.dart';

/// Provides static methods for activity operations
class ActivityService {
  /// Static method to get tasks
  static Future<ResponseResult<TasksResponse>> getTasks() async {
    return await TasksApi.getTasks();
  }

  /// Static method to collect reward
  static Future<ResponseResult<RewardResponse>> collectReward(int taskId) async {
    return await TasksApi.collectReward(taskId);
  }
}
