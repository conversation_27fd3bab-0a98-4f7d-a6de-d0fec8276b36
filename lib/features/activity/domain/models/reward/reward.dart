/// RewardResponse POJO class following json2bean pattern
class RewardResponse {
  double? amount;
  int? type;

  RewardResponse({this.amount, this.type});

  RewardResponse.fromJson(Map<String, dynamic> json) {
    amount = json['amount']?.toDouble();
    type = json['type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['amount'] = amount;
    data['type'] = type;
    return data;
  }

  RewardResponse copyWith({
    double? amount,
    int? type,
  }) {
    return RewardResponse(
      amount: amount ?? this.amount,
      type: type ?? this.type,
    );
  }

  @override
  String toString() {
    return 'RewardResponse{amount: $amount, type: $type}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RewardResponse && runtimeType == other.runtimeType && amount == other.amount && type == other.type;

  @override
  int get hashCode => amount.hashCode ^ type.hashCode;
}
