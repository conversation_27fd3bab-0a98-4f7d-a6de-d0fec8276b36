/// TasksResponse POJO class following json2bean pattern
class TasksResponse {
  List<TaskItem>? daily;
  List<TaskItem>? invite;
  List<TaskItem>? newUser;
  List<TaskItem>? trade;

  TasksResponse({this.daily, this.invite, this.newUser, this.trade});

  TasksResponse.fromJson(Map<String, dynamic> json) {
    if (json['daily'] != null) {
      daily = <TaskItem>[];
      json['daily'].forEach((v) {
        daily!.add(TaskItem.fromJson(v));
      });
    }
    if (json['invite'] != null) {
      invite = <TaskItem>[];
      json['invite'].forEach((v) {
        invite!.add(TaskItem.fromJson(v));
      });
    }
    if (json['newUser'] != null) {
      newUser = <TaskItem>[];
      json['newUser'].forEach((v) {
        newUser!.add(TaskItem.fromJson(v));
      });
    }
    if (json['trade'] != null) {
      trade = <TaskItem>[];
      json['trade'].forEach((v) {
        trade!.add(TaskItem.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (daily != null) {
      data['daily'] = daily!.map((v) => v.toJson()).toList();
    }
    if (invite != null) {
      data['invite'] = invite!.map((v) => v.toJson()).toList();
    }
    if (newUser != null) {
      data['newUser'] = newUser!.map((v) => v.toJson()).toList();
    }
    if (trade != null) {
      data['trade'] = trade!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  TasksResponse copyWith({
    List<TaskItem>? daily,
    List<TaskItem>? invite,
    List<TaskItem>? newUser,
    List<TaskItem>? trade,
  }) {
    return TasksResponse(
      daily: daily ?? this.daily,
      invite: invite ?? this.invite,
      newUser: newUser ?? this.newUser,
      trade: trade ?? this.trade,
    );
  }

  @override
  String toString() {
    return 'TasksResponse{daily: $daily, invite: $invite, newUser: $newUser, trade: $trade}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TasksResponse &&
          runtimeType == other.runtimeType &&
          daily == other.daily &&
          invite == other.invite &&
          newUser == other.newUser &&
          trade == other.trade;

  @override
  int get hashCode => daily.hashCode ^ invite.hashCode ^ newUser.hashCode ^ trade.hashCode;
}

/// TaskItem POJO class following json2bean pattern
class TaskItem {
  double? amount;
  int? amountType;
  int? availableNum;
  Map<String, dynamic>? content;
  String? endTime;
  int? expireType;
  String? icon;
  int? id;
  bool? isAutoReceive;
  bool? isCompleted;
  bool? isReceived;
  Map<String, dynamic>? name;
  Map<String, dynamic>? rule;
  String? startTime;
  bool? status;
  int? taskType;
  int? type;

  TaskItem({
    this.amount,
    this.amountType,
    this.availableNum,
    this.content,
    this.endTime,
    this.expireType,
    this.icon,
    this.id,
    this.isAutoReceive,
    this.isCompleted,
    this.isReceived,
    this.name,
    this.rule,
    this.startTime,
    this.status,
    this.taskType,
    this.type,
  });

  TaskItem.fromJson(Map<String, dynamic> json) {
    amount = json['amount']?.toDouble();
    amountType = json['amountType'];
    availableNum = json['availableNum'];
    content = json['content'] != null ? Map<String, dynamic>.from(json['content']) : null;
    endTime = json['endTime'];
    expireType = json['expireType'];
    icon = json['icon'];
    id = json['id'];
    isAutoReceive = json['isAutoReceive'];
    isCompleted = json['isCompleted'];
    isReceived = json['isReceived'];
    name = json['name'] != null ? Map<String, dynamic>.from(json['name']) : null;
    rule = json['rule'] != null ? Map<String, dynamic>.from(json['rule']) : null;
    startTime = json['startTime'];
    status = json['status'];
    taskType = json['taskType'];
    type = json['type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['amount'] = amount;
    data['amountType'] = amountType;
    data['availableNum'] = availableNum;
    data['content'] = content;
    data['endTime'] = endTime;
    data['expireType'] = expireType;
    data['icon'] = icon;
    data['id'] = id;
    data['isAutoReceive'] = isAutoReceive;
    data['isCompleted'] = isCompleted;
    data['isReceived'] = isReceived;
    data['name'] = name;
    data['rule'] = rule;
    data['startTime'] = startTime;
    data['status'] = status;
    data['taskType'] = taskType;
    data['type'] = type;
    return data;
  }

  TaskItem copyWith({
    double? amount,
    int? amountType,
    int? availableNum,
    Map<String, dynamic>? content,
    String? endTime,
    int? expireType,
    String? icon,
    int? id,
    bool? isAutoReceive,
    bool? isCompleted,
    bool? isReceived,
    Map<String, dynamic>? name,
    Map<String, dynamic>? rule,
    String? startTime,
    bool? status,
    int? taskType,
    int? type,
  }) {
    return TaskItem(
      amount: amount ?? this.amount,
      amountType: amountType ?? this.amountType,
      availableNum: availableNum ?? this.availableNum,
      content: content ?? this.content,
      endTime: endTime ?? this.endTime,
      expireType: expireType ?? this.expireType,
      icon: icon ?? this.icon,
      id: id ?? this.id,
      isAutoReceive: isAutoReceive ?? this.isAutoReceive,
      isCompleted: isCompleted ?? this.isCompleted,
      isReceived: isReceived ?? this.isReceived,
      name: name ?? this.name,
      rule: rule ?? this.rule,
      startTime: startTime ?? this.startTime,
      status: status ?? this.status,
      taskType: taskType ?? this.taskType,
      type: type ?? this.type,
    );
  }

  @override
  String toString() {
    return 'TaskItem{amount: $amount, amountType: $amountType, availableNum: $availableNum, content: $content, endTime: $endTime, expireType: $expireType, icon: $icon, id: $id, isAutoReceive: $isAutoReceive, isCompleted: $isCompleted, isReceived: $isReceived, name: $name, rule: $rule, startTime: $startTime, status: $status, taskType: $taskType, type: $type}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TaskItem &&
          runtimeType == other.runtimeType &&
          amount == other.amount &&
          amountType == other.amountType &&
          availableNum == other.availableNum &&
          content == other.content &&
          endTime == other.endTime &&
          expireType == other.expireType &&
          icon == other.icon &&
          id == other.id &&
          isAutoReceive == other.isAutoReceive &&
          isCompleted == other.isCompleted &&
          isReceived == other.isReceived &&
          name == other.name &&
          rule == other.rule &&
          startTime == other.startTime &&
          status == other.status &&
          taskType == other.taskType &&
          type == other.type;

  @override
  int get hashCode =>
      amount.hashCode ^
      amountType.hashCode ^
      availableNum.hashCode ^
      content.hashCode ^
      endTime.hashCode ^
      expireType.hashCode ^
      icon.hashCode ^
      id.hashCode ^
      isAutoReceive.hashCode ^
      isCompleted.hashCode ^
      isReceived.hashCode ^
      name.hashCode ^
      rule.hashCode ^
      startTime.hashCode ^
      status.hashCode ^
      taskType.hashCode ^
      type.hashCode;
}
