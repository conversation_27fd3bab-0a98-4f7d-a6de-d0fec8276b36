import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/features/profile/logic/auth_aware_cubit.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/app/extension/helper.dart';
import '../../../../shared/constants/enums.dart';
import '../../../sign_in/domain/models/login/login_response.dart';

import '../../domain/models/tasks/tasks.dart';
import '../../domain/services/activity_service.dart';

part 'activity_state.dart';

@injectable
class ActivityCubit extends AuthAwareCubit<ActivityState> {
  ActivityCubit() : super(const ActivityState());

  @override
  void onLoggedIn(LoginResponse loginResponse) => getTasks();

  @override
  void onLoggedOut() => emit(const ActivityState());

  void updateTab(int index) => emit(state.copyWith(selectedTab: index));

  void updateSelectedTab(int index) => emit(state.copyWith(selectedEventIndex: index));

  Future<void> getTasks() async {
    emit(state.copyWith(tasksFetchStatus: DataStatus.loading));
    final result = await ActivityService.getTasks();
    if (result.data != null) {
      emit(state.copyWith(
        tasksFetchStatus: DataStatus.success,
        newUserTasks: result.data?.newUser,
        tradeTasks: result.data?.trade,
        dailyTasks: result.data?.daily,
        error: null,
      ));
    } else {
      emit(state.copyWith(tasksFetchStatus: DataStatus.failed, error: result.error));
    }
  }

  Future<void> collectReward(TaskItem task) async {
    if (task.isCompleted != true || task.isReceived == true) return;
    if (task.id == null) return;

    emit(state.copyWith(rewardCollectionStatus: DataStatus.loading));
    final result = await ActivityService.collectReward(task.id!);

    if (result.data != null) {
      Helper.showFlutterToast(
        'rewardCollected'.tr(),
      );
      await getTasks();
      emit(state.copyWith(rewardCollectionStatus: DataStatus.success));
    } else {
      emit(state.copyWith(rewardCollectionStatus: DataStatus.failed, error: result.error));
      Helper.showFlutterToast(
        result.error ?? 'Failed to collect reward',
      );
    }
  }
}
