import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/core/api/network/network_helper.dart';
import 'package:gp_stock_app/features/activity/domain/models/reward/reward.dart';
import 'package:gp_stock_app/features/activity/domain/models/tasks/tasks.dart';

/// Handles all tasks and rewards-related API calls.
class TasksApi {
  /// Get tasks
  /// 获取任务列表
  static Future<ResponseResult<TasksResponse>> getTasks() async {
    try {
      final response = await NetworkProvider().get(
        ApiEndpoints.tasks,
        isAuthRequired: true,
      );

      final responseModel = NetworkHelper.mappingResponseData<TasksResponse>(response);

      return ResponseResult(
        data: responseModel.data,
        error: responseModel.data == null ? responseModel.msg : null,
      );
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Collect reward
  /// 收集奖励
  static Future<ResponseResult<RewardResponse>> collectReward(int taskId) async {
    try {
      final response = await NetworkProvider().get(
        ApiEndpoints.collectReward,
        queryParameters: {'taskId': taskId},
        isAuthRequired: true,
      );

      final responseModel = NetworkHelper.mappingResponseData<RewardResponse>(response);

      return ResponseResult(
        data: responseModel.data,
        error: responseModel.data == null ? responseModel.msg : null,
      );
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
