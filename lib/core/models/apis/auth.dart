import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/core/services/http/http_config.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/sign_in/domain/models/login/login_response.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class AuthApi {
  /// User login
  /// 用户登录
  static Future<LoginResponse?> login({
    required String mobile,
    required String password,
    required String smsCode,
    required LoginMode mode,
    String? validate,
  }) async {
    final res = await Http().request<LoginResponse>(
      ApiEndpoints.login,
      method: HttpMethod.post,
      params: {
        "mobile": mobile,
        "password": password.toBase64(),
        "verifyType": mode.text,
        "smsCode": smsCode,
        if (validate != null) "validate": validate,
      },
      needSignIn: false, // Login doesn't require authentication
      shouldRetry: true, // Enable retry for login
    );
    return res.data;
  }

  /// User logout
  /// 用户退出登录
  static Future<bool> logout() async {
    final res = await Http().request(
      ApiEndpoints.logout,
      method: HttpMethod.post,
      needSignIn: true, // Logout requires authentication
      shouldRetry: false, // Don't retry logout
    );
    return res.isSuccess;
  }

  /// Request OTP/SMS code
  /// 请求OTP/SMS验证码
  static Future<bool> requestOTP({
    required String phoneNumber,
    required String sendType,
  }) async {
    final res = await Http().request(
      ApiEndpoints.sendMsg,
      method: HttpMethod.post,
      params: {
        "mobile": phoneNumber,
        "sendType": sendType,
      },
      needSignIn: false, // OTP request doesn't require authentication
      shouldRetry: true, // Enable retry for OTP
    );
    return res.isSuccess;
  }

  /// Check if WangYi captcha is required
  /// 检查是否需要网易验证码
  static Future<bool> requestWangYiCaptchaRequired() async {
    final res = await Http().request<bool>(
      ApiEndpoints.wangYiCaptcha,
      method: HttpMethod.get,
      needSignIn: false, // Captcha check doesn't require authentication
      shouldRetry: false, // Don't retry captcha check
      needShowToast: false, // Don't show toast for captcha check failures
    );
    return res.data == true;
  }
}
