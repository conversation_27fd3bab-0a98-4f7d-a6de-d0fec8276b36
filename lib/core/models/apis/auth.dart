import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/sign_in/domain/models/login/login_response.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class AuthApi {

  /// User login
  /// 用户登录
  static Future<ResponseResult<LoginResponse>> login({
    required String mobile,
    required String password,
    required String smsCode,
    required LoginMode mode,
    String? validate,
  }) async {
    try {
      final Response response = await NetworkProvider().post(ApiEndpoints.login, data: {
        "mobile": mobile,
        "password": password.toBase64(),
        "verifyType": mode.text,
        "smsCode": smsCode,
        if (validate != null) "validate": validate,
      });
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: LoginResponse.fromJson(response.data),
            token: response.headers['authorization']?.first,
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to login');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// User logout
  /// 用户退出登录
  static Future<ResponseResult<bool>> logout() async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.logout,
        options: Options(
          headers: {'auth': true},
        ),
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: true,
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to logout');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Request OTP/SMS code
  /// 请求OTP/SMS验证码
  static Future<ResponseResult<bool>> requestOTP({
    required String phoneNumber,
    required String sendType,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.sendMsg,
        data: {
          "mobile": phoneNumber,
          "sendType": sendType,
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: true,
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to send otp');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Check if WangYi captcha is required
  /// 检查是否需要网易验证码
  static Future<bool> requestWangYiCaptchaRequired() async {
    try {
      final Response response = await NetworkProvider().get(ApiEndpoints.wangYiCaptcha);
      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data['data'] == true;
      } else {
        return false;
      }
    } on DioException {
      return false;
    }
  }
}
