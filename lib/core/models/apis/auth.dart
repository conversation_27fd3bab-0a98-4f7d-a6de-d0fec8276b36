import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/core/api/network/network_helper.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/sign_in/domain/models/login/login_response.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class AuthApi {
  /// User login
  /// 用户登录
  static Future<ResponseResult<LoginResponse>> login({
    required String mobile,
    required String password,
    required String smsCode,
    required LoginMode mode,
    String? validate,
  }) async {
    try {
      final Response response = await NetworkProvider().post(ApiEndpoints.login, data: {
        "mobile": mobile,
        "password": password.toBase64(),
        "verifyType": mode.text,
        "smsCode": smsCode,
        if (validate != null) "validate": validate,
      });

      final loginResponse = LoginResponse.fromJson(response.data);

      return ResponseResult(
        data: loginResponse,
        token: response.headers['authorization']?.first,
        error: loginResponse.data == null ? loginResponse.msg : null,
      );
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// User logout
  /// 用户退出登录
  static Future<ResponseResult<bool>> logout() async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.logout,
        options: Options(
          headers: {'auth': true},
        ),
      );

      final responseModel = NetworkHelper.mappingResponseData<bool>(response);

      return ResponseResult(
        data: responseModel.data != null ? true : null,
        error: responseModel.data == null ? responseModel.msg : null,
      );
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Request OTP/SMS code
  /// 请求OTP/SMS验证码
  static Future<ResponseResult<bool>> requestOTP({
    required String phoneNumber,
    required String sendType,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.sendMsg,
        data: {
          "mobile": phoneNumber,
          "sendType": sendType,
        },
      );

      final responseModel = NetworkHelper.mappingResponseData<bool>(response);

      return ResponseResult(
        data: responseModel.data != null ? true : null,
        error: responseModel.data == null ? responseModel.msg : null,
      );
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Check if WangYi captcha is required
  /// 检查是否需要网易验证码
  static Future<bool> requestWangYiCaptchaRequired() async {
    try {
      final Response response = await NetworkProvider().get(ApiEndpoints.wangYiCaptcha);
      final responseModel = NetworkHelper.mappingResponseData<bool>(response);

      return responseModel.data == true;
    } on DioException {
      return false;
    }
  }
}
