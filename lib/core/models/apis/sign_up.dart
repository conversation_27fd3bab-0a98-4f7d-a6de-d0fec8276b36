import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/core/api/network/network_helper.dart';

import '../../../features/sign_up/domain/models/sign_up_request.dart';

class SignUpApi {
  /// User registration
  /// 用户注册
  static Future<ResponseResult<bool>> register({required SignUpRequest signUpRequest}) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.register,
        data: signUpRequest.toJson(),
      );
      
      final responseModel = NetworkHelper.mappingResponseData<bool>(response);
      
      return ResponseResult(
        data: responseModel.data != null ? true : null,
        error: responseModel.data == null ? responseModel.msg : null,
      );
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
