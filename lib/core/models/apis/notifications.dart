import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/core/api/network/network_helper.dart';
import 'package:gp_stock_app/features/notifications/domain/models/notification/notification_model.dart';

/// Handles all notification-related API calls
class NotificationsApi {
  /// Get notification list
  /// 获取通知列表
  static Future<ResponseResult<NotificationModel>> getNotificationList({
    required String messageType,
    required int page,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.notificationPage,
        isAuthRequired: true,
        queryParameters: {
          'type': messageType,
          'pageNumber': page,
          'pageSize': 20,
        },
      );

      final responseModel = NetworkHelper.mappingResponseData<NotificationModel>(response);

      return ResponseResult(
        data: responseModel.data,
        error: responseModel.data == null ? responseModel.msg : null,
      );
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Get notification count
  /// 获取通知数量
  static Future<ResponseResult<int>> getNotificationCount() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.notificationCount,
        isAuthRequired: true,
      );

      final responseModel = NetworkHelper.mappingResponseData<int>(response);

      return ResponseResult(
        data: responseModel.data,
        error: responseModel.data == null ? responseModel.msg : null,
      );
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Mark notification as read
  /// 标记通知为已读
  static Future<ResponseResult<bool>> readNotification({
    required messageId,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.notificationRead,
        isAuthRequired: true,
        data: {
          'messageId': messageId,
        },
      );

      final responseModel = NetworkHelper.mappingResponseData<bool>(response);

      return ResponseResult(
        data: responseModel.data,
        error: responseModel.data == null ? responseModel.msg : null,
      );
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
