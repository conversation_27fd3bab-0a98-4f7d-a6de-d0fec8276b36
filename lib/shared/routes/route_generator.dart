import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/account/domain/models/account_summary/contract_summary_response.dart';
import 'package:gp_stock_app/features/account/logic/account/account_cubit.dart';
import 'package:gp_stock_app/features/account/logic/bank_list/bank_list_cubit.dart';
import 'package:gp_stock_app/features/account/logic/deposit/deposit_cubit.dart';
import 'package:gp_stock_app/features/account/logic/pay_order/pay_order_cubit.dart';
import 'package:gp_stock_app/features/account/logic/user_bank_list/user_bank_list_cubit.dart';
import 'package:gp_stock_app/features/account/logic/withdraw/withdraw_cubit.dart';
import 'package:gp_stock_app/features/account/screens/deposit_main_screen.dart';
import 'package:gp_stock_app/features/account/screens/deposit_screen.dart';
import 'package:gp_stock_app/features/account/screens/pay_order/pay_order_screen.dart';
import 'package:gp_stock_app/features/account/screens/transfer_type_screen.dart';
import 'package:gp_stock_app/features/account/screens/withdraw_main_screen.dart';
import 'package:gp_stock_app/features/account/screens/withdraw_screen.dart';
import 'package:gp_stock_app/features/company_news/logic/news/company_news_cubit.dart';
import 'package:gp_stock_app/features/contract/logic/contract/contract_cubit.dart';
import 'package:gp_stock_app/features/contract/logic/contract_withdraw/contract_withdraw_cubit.dart';
import 'package:gp_stock_app/features/contract/screens/contract_withdraw_screen.dart';
import 'package:gp_stock_app/features/forgot/logic/forgot/forgot_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_all_info_screen.dart';
import 'package:gp_stock_app/features/home/<USER>/ai_chat/ai_chat_cubit.dart' show AIChatCubit;
import 'package:gp_stock_app/features/home/<USER>/ai_chat_screen.dart';
import 'package:gp_stock_app/features/market/logic/index_page/index_page_cubit.dart';
import 'package:gp_stock_app/features/market/screens/index_page_screen.dart';
import 'package:gp_stock_app/features/profile/domain/models/app_info/app_info_model.dart';
import 'package:gp_stock_app/features/profile/logic/vip/vip_cubit.dart';
import 'package:gp_stock_app/features/profile/screens/about_us/app_info_content_screen.dart';
import 'package:gp_stock_app/features/sign_in/screens/sign_in_screen.dart';
import 'package:gp_stock_app/features/splash/screens/splash_screen.dart';

import '../../core/dependency_injection/injectable.dart';
import '../../features/account/logic/deposit_channel/deposit_channel_cubit.dart';
import '../../features/account/logic/otp/otp_cubit.dart';
import '../../features/account/logic/trading/trading_cubit.dart';
import '../../features/account/screens/account_details_screen.dart';
import '../../features/account/screens/deposit_record_screen.dart';
import '../../features/account/screens/history/spot_and_contract_history_screen.dart';
import '../../features/account/screens/order_detail_screen.dart';
import '../../features/account/screens/trading_center/trading_center_screen.dart';
import '../../features/account/screens/withdrawal_record_screen.dart';
import '../../features/company_news/screens/company_news_screen.dart';
import '../../features/contract/logic/contract_activity/contract_activity_cubit.dart';
import '../../features/contract/logic/contract_terminate/contract_terminate_cubit.dart';
import '../../features/contract/logic/expand_margin/margin_call_cubit.dart';
import '../../features/contract/logic/fund_records/fund_records_cubit.dart';
import '../../features/contract/screens/apply_records/contract_apply_records.dart';
import '../../features/contract/screens/apply_records/contract_apply_screen.dart';
import '../../features/contract/screens/apply_records/contract_settle_history_screen.dart';
import '../../features/contract/screens/contract_activity_screen.dart';
import '../../features/contract/screens/contract_application_screen.dart';
import '../../features/contract/screens/expand_margin/expand_margin_screen.dart';
import '../../features/contract/screens/fund_records/fund_records_screen.dart';
import '../../features/contract/screens/terminate_contract/terminate_contract_screen.dart';
import '../../features/convert_rate/logic/convert_rate/convert_rate_cubit.dart';
import '../../features/convert_rate/screens/convert_rate_screen.dart';
import '../../features/forgot/screens/forgot_screen.dart';
import '../../features/home/<USER>/models/home_notification/home_notification_model.dart' show HomeNotificationModel;
import '../../features/home/<USER>/models/news/news_model.dart';
import '../../features/home/<USER>/events/event_details_screen.dart';
import '../../features/home/<USER>/news/news_details_screen.dart';
import '../../features/invite/logic/invite/invite_cubit.dart';
import '../../features/invite/screens/invite_screen.dart';
import '../../features/main/screens/main_screen.dart';
import '../../features/market/domain/models/plate_info_request/plate_info_request.dart';
import '../../features/market/plate_info_screen.dart';
import '../../features/market/plate_list_screen.dart';
import '../../features/notifications/screens/notification_list_screen.dart';
import '../../features/profile/logic/app_info/app_info_cubit.dart';
import '../../features/profile/logic/help/help_cubit.dart';
import '../../features/profile/logic/third_party_channel/third_party_channel_cubit.dart';
import '../../features/profile/screens/about_us/about_us_screen.dart';
import '../../features/profile/screens/auth_n/auth_n_screen.dart';
import '../../features/profile/screens/avatar/avatar_screen.dart';
import '../../features/profile/screens/password/change_password_phone_screen.dart';
import '../../features/profile/screens/password/change_password_screen.dart';
import '../../features/profile/screens/password/password_settings_screen.dart';
import '../../features/profile/screens/questions/questions_details.dart';
import '../../features/profile/screens/questions/questions_screen.dart';
import '../../features/profile/screens/settings/mission_center_screen.dart';
import '../../features/profile/screens/settings/personal_info_edit.dart';
import '../../features/profile/screens/settings/settings_screen.dart';
import '../../features/profile/screens/third_party_channel/add_wallet_screen.dart';
import '../../features/profile/screens/third_party_channel/third_party_channel_screen.dart';
import '../../features/sign_up/screens/sign_up_screen.dart';
import '../constants/enums.dart';
import '../models/route_arguments/trading_arguments.dart';
import 'routes.dart';

class RouteGenerator {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case routeRoot:
        return MaterialPageRoute(builder: (_) => SplashScreen());
      case routeLogin:
        final arguments = settings.arguments as Map<String, dynamic>?;
        final needRedirection = arguments?['needRedirection'] ?? false;
        return MaterialPageRoute(builder: (_) => SignInScreen(needRedirection: needRedirection));
      case routeSignUp:
        return MaterialPageRoute(builder: (_) => SignUpScreen());
      case routeForgot:
        return MaterialPageRoute(
          builder: (_) => BlocProvider(create: (context) => getIt<ForgotCubit>(), child: ForgotScreen()),
        );
      case routeMain:
        return MaterialPageRoute(builder: (_) => MainScreen());
      case routeAboutUs:
        return MaterialPageRoute(
          builder: (_) => BlocProvider.value(
            value: getIt<AppInfoCubit>(),
            child: AboutUsScreen(),
          ),
        );
      case routeSettings:
        return MaterialPageRoute(builder: (_) => SettingsScreen());
      case routePassword:
        final arguments = settings.arguments as Map<String, dynamic>;
        final type = arguments['type'] as PasswordModifyType;
        return MaterialPageRoute(builder: (_) => PasswordSettingsScreen(type: type));
      case routeChangePasswordWithPhone:
        final arguments = settings.arguments as Map<String, dynamic>;
        final type = arguments['type'] as PasswordModifyType;
        return MaterialPageRoute(
          builder: (_) =>
              BlocProvider(create: (context) => getIt<OtpCubit>(), child: ChangePasswordWithPhoneScreen(type: type)),
        );
      case routeChangePassword:
        final arguments = settings.arguments as Map<String, dynamic>;
        final type = arguments['type'] as PasswordModifyType;
        return MaterialPageRoute(builder: (_) => ChangePasswordScreen(type: type));
      case routePersonalInfoEdit:
        return MaterialPageRoute(builder: (_) => PersonalInfoEditScreen());
      case routeAuthN:
        return MaterialPageRoute(builder: (_) => AuthNScreen());
      case routeQuestions:
        return MaterialPageRoute(
          builder: (_) =>
              BlocProvider(create: (context) => getIt<HelpCubit>()..getHelpList(), child: QuestionsScreen()),
        );
      case routeQuestionsDetails:
        final questionId = settings.arguments as int;
        return MaterialPageRoute(
          builder: (_) => BlocProvider(
            create: (_) => getIt<HelpCubit>()..getHelpQuestionDetail(questionId.toString()),
            child: QuestionsDetailsScreen(questionId: questionId),
          ),
        );
      case routeCompanyNewsDetails:
        final articleId = settings.arguments as String;
        return MaterialPageRoute(
          builder: (_) => BlocProvider(
            create: (_) => getIt<CompanyNewsCubit>()..getCompanyNewsDetails(articleId),
            child: CompanyNewsDetailsScreen(newsId: articleId),
          ),
        );
      case routeContractApply:
        final arguments = settings.arguments as Map<String, dynamic>;
        final mainContractType = arguments['mainContractType'] as MainContractType;
        return MaterialPageRoute(
          builder: (_) => BlocProvider(
            create: (context) => getIt<ContractCubit>()..getOpenContractType(),
            child: ContractApplyScreen(mainContractType: mainContractType),
          ),
        );
      case routeContractApplication:
        final arguments = settings.arguments as Map<String, dynamic>;
        final mainContractType = arguments['mainContractType'] as MainContractType;
        final contractType = arguments['contractType'] as ContractType;
        return MaterialPageRoute(
          builder: (_) => BlocProvider(
            create: (context) => getIt<ContractCubit>(),
            child: ContractApplicationScreen(
              mainContractType: mainContractType,
              contractType: contractType,
            ),
          ),
        );
      case routeContractActivity:
        final arguments = settings.arguments as Map<String, dynamic>;
        final contractType = arguments['contractType'] as ContractType;
        final mainContractType = arguments['mainContractType'] as MainContractType;
        return MaterialPageRoute(
          builder: (_) => BlocProvider(
            create: (context) => getIt<ContractActivityCubit>(),
            child: ContractActivityScreen(contractType: contractType, mainContractType: mainContractType),
          ),
        );
      case routeSpotAndContractHistory:
        final arguments = settings.arguments is Map<String, dynamic>
            ? settings.arguments as Map<String, dynamic>
            : {'contract': null};
        final contract = arguments['contract'];
        return MaterialPageRoute(builder: (_) => SpotAndContractHistoryScreen(contract: contract));
      case routeAccountDetails:
        return MaterialPageRoute(builder: (_) => AccountDetailsScreen());
      case routeAvatarScreen:
        return MaterialPageRoute(builder: (_) => AvatarScreen());
      case routeContractApplyRecord:
        return MaterialPageRoute(
          builder: (_) => BlocProvider(
            create: (context) => getIt<ContractCubit>(),
            child: ContractApplyRecordsScreen(),
          ),
        );
      case routeContractSettleHistory:
        return MaterialPageRoute(builder: (_) => ContractSettleHistoryScreen());
      // case routeNotification:
      //   return MaterialPageRoute(builder: (_) => NotificationsScreen());
      case routeNotificationList:
        return MaterialPageRoute(builder: (_) => NotificationListScreen());
      case routeTradingCenter:
        final arguments = settings.arguments as TradingArguments;
        final selectedIndex = arguments.selectedIndex;
        final isFromHome = arguments.shouldNavigateToIndex;
        final instrument = arguments.instrumentInfo;
        final isIndexTrading = arguments.isIndexTrading;
        final contract = arguments.contract;
        final tradingCubit = TradingCubit();
        return MaterialPageRoute(
          builder: (_) => MultiBlocProvider(
            providers: [
              BlocProvider(
                  create: (context) => tradingCubit
                    ..setContract(contract)
                    ..setTradingType(getContractLabel(contract))
                    ..setContractId(contract?.id)),
              BlocProvider(
                create: (context) => getIt<AccountCubit>()..startTradingCenterPolling(instrument, contract?.id),
                // ..getOrderList(
                //   AccountMarketType.currentPositions,
                //   symbol: instrument.symbol,
                //   market: instrument.market,
                //   securityType: instrument.securityType,
                //   contractId: contract?.id,
                // )
                // ..getContractSummary()
                // ..getOrderList(
                //   AccountMarketType.orderDetails,
                //   symbol: instrument.symbol,
                //   status: 0,
                //   market: instrument.market,
                //   securityType: instrument.securityType,
                //   contractId: contract?.id,
                // ),
              ),
            ],
            child: TradingCenterScreen(
              instrument: instrument,
              selectedIndex: selectedIndex,
              shouldNavigateToIndex: isFromHome,
              isIndexTrading: isIndexTrading,
            ),
          ),
        );
      case routeTerminateContract:
        final contractSummary = settings.arguments as ContractSummaryData;
        return MaterialPageRoute(
            builder: (_) => BlocProvider(
                  create: (context) => getIt<ContractTerminateCubit>(),
                  child: TerminateContractScreen(contractSummary: contractSummary),
                ));
      case routeFTradeAllInfo:
        assert(settings.arguments != null, 'arguments can not be null');
        return MaterialPageRoute(
          builder: (context) => FTradeAllInfoScreen.fromArgs(settings.arguments),
        );
      case routeDeposit:
        return MaterialPageRoute(
          builder: (_) => MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) => BankListCubit(),
              ),
              BlocProvider(
                create: (context) => UserBankListCubit()..fetchBankList(),
              ),
              BlocProvider(
                create: (context) => DepositCubit(),
              ),
            ],
            child: DepositScreen(),
          ),
        );
      case routeWithdraw:
        return MaterialPageRoute(
          builder: (_) => MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) => BankListCubit(),
              ),
              BlocProvider(
                create: (context) => UserBankListCubit()..fetchBankList(),
              ),
              BlocProvider(
                create: (context) => WithdrawalCubit()..getConfig(),
              ),
            ],
            child: WithdrawScreen(),
          ),
        );
      case routeTransferType:
        final transferType = settings.arguments as TransferType;
        return MaterialPageRoute(
          builder: (_) => MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) => DepositChannelCubit(),
              ),
            ],
            child: TransferTypeScreen(type: transferType),
          ),
        );
      case routeMissionCenter:
        final arguments = settings.arguments as bool?;
        final isVip = arguments ?? false;
        return MaterialPageRoute(
          builder: (_) => BlocProvider(
            create: (context) => getIt<VipCubit>(),
            child: MissionCenterScreen(isVip: isVip),
          ),
        );
      case routePlateInfo:
        final plateInfoRequest = settings.arguments as PlateInfoRequest;
        return MaterialPageRoute(
          builder: (_) => PlateInfoScreen(plateInfoRequest: plateInfoRequest),
        );
      case routeFundRecords:
        final arguments = settings.arguments is Map<String, dynamic>
            ? settings.arguments as Map<String, dynamic>
            : {'contractId': null};
        final contractId = arguments['contractId'];
        final isContractAccount = arguments['isContractAccount'] ?? false;
        return MaterialPageRoute(
          builder: (_) => BlocProvider(
            create: (context) => getIt<FundRecordsCubit>(),
            child: FundRecordsScreen(contractId: contractId, isContractAccount: isContractAccount),
          ),
        );
      case routeMarginCall:
        final arguments = settings.arguments is Map<String, dynamic>
            ? settings.arguments as Map<String, dynamic>
            : {'contractId': null, 'contractType': ContractAction.replenish};
        final contractId = arguments['contractId'];
        final contractActionType = arguments['contractType'];
        final type = arguments['type'];
        return MaterialPageRoute(
          builder: (_) => BlocProvider(
            create: (context) => getIt<MarginCallCubit>(),
            child:
                ExpandMarginScreen(contractId: contractId, contractActionType: contractActionType, contractType: type),
          ),
        );
      case routePlateList:
        return MaterialPageRoute(builder: (_) => PlateListScreen());
      case routeInvite:
        return MaterialPageRoute(
          builder: (_) => BlocProvider(
            create: (context) => getIt<InviteCubit>(),
            child: InviteScreen(),
          ),
        );
      case routeWithdrawRecords:
        return MaterialPageRoute(
            builder: (_) => BlocProvider(
                  create: (context) => WithdrawalCubit(),
                  child: WithdrawalRecordsScreen(),
                ));
      case routeDepositRecords:
        return MaterialPageRoute(
          builder: (_) => BlocProvider(
            create: (context) => DepositCubit(),
            child: DepositRecordsScreen(),
          ),
        );
      case routePayOrder:
        return MaterialPageRoute(
          builder: (_) => BlocProvider(
            create: (context) => PayOrderCubit(),
            child: const PayOrderScreen(),
          ),
        );
      case routeContractWithdraw:
        final arguments = settings.arguments is Map<String, dynamic>
            ? settings.arguments as Map<String, dynamic>
            : {'contractId': null};
        final contractId = arguments['contractId'];
        return MaterialPageRoute(
          builder: (_) => MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) => getIt<ContractWithdrawCubit>()..getWithdrawConfig(contractId: contractId),
              ),
            ],
            child: ContractWithdrawScreen(contractId: contractId),
          ),
        );
      case routeNewsDetails:
        final NewsItem news = settings.arguments as NewsItem;
        return MaterialPageRoute(
          builder: (_) => NewsDetailsScreen(news: news),
        );

      case routeAppInfoContent:
        final appInfo = settings.arguments as AppInfoModel;
        return MaterialPageRoute(
          builder: (_) => AppInfoContentScreen(appInfo: appInfo),
        );

      case routeEventDetails:
        final event = settings.arguments as HomeNotificationModel;
        return MaterialPageRoute(
          builder: (_) => EventDetailsScreen(event: event),
        );
      case routeAIChat:
        return MaterialPageRoute(
          builder: (_) => BlocProvider(
            create: (context) => AIChatCubit(),
            child: AIChatScreen(),
          ),
        );
      case routeConvertRate:
        return MaterialPageRoute(
          builder: (_) => BlocProvider(
            create: (context) => getIt<ConvertRateCubit>()..fetchConvertRate(),
            child: ConvertRateScreen(),
          ),
        );
      case routeThirdPartyChannel:
        final arguments = settings.arguments as Map<String, dynamic>?;
        final transactionType = arguments?['type'] as ThirdPartyTransactionType? ?? ThirdPartyTransactionType.deposit;
        return MaterialPageRoute(
          builder: (_) => BlocProvider(
            create: (context) => getIt<ThirdPartyChannelCubit>(),
            child: ThirdPartyChannelScreen(
              transactionType: transactionType,
            ),
          ),
        );
      case routeAddWallet:
        return MaterialPageRoute(
          builder: (_) => BlocProvider.value(
            value: getIt<ThirdPartyChannelCubit>(),
            child: AddWalletScreen(),
          ),
        );
      case routeIndexPage:
        final arguments = settings.arguments as Map<String, dynamic>?;
        final isTransactionDetail = arguments?['isTransactionDetail'] as bool? ?? false;
        return MaterialPageRoute(
          builder: (_) => BlocProvider(
            create: (_) => getIt<IndexPageCubit>(),
            child: IndexPageScreen(
              isTransactionDetail: isTransactionDetail,
            ),
          ),
        );
      case routeDepositMain:
        final arguments = settings.arguments as Map<String, dynamic>?;
        final depositType = arguments?['type'] as DepositType? ?? DepositType.bank;
        return MaterialPageRoute(
          builder: (_) => MultiBlocProvider(
            providers: [
              BlocProvider<DepositCubit>(create: (context) => DepositCubit()),
              BlocProvider<UserBankListCubit>(create: (context) => UserBankListCubit()..fetchBankList()),
              BlocProvider<BankListCubit>(create: (context) => BankListCubit()),
              BlocProvider(create: (context) => getIt<DepositChannelCubit>()..getDepositChannelsList()),
            ],
            child: DepositMainScreen(depositType: depositType),
          ),
        );
      case routeWithdrawMain:
        return MaterialPageRoute(
          builder: (_) => MultiBlocProvider(
            providers: [
              BlocProvider<WithdrawalCubit>(create: (context) => WithdrawalCubit()..getConfig()),
              BlocProvider<UserBankListCubit>(create: (context) => UserBankListCubit()..fetchBankList()),
              BlocProvider<BankListCubit>(create: (context) => BankListCubit()),
            ],
            child: WithdrawMainScreen(),
          ),
        );
      case routeOrderDetail:
        final args = settings.arguments as Map<String, dynamic>;
        final orderId = args['orderId'];
        return MaterialPageRoute(
          builder: (_) => BlocProvider(
            create: (context) => getIt<AccountCubit>(),
            child: OrderDetailScreen(orderId: orderId),
          ),
        );
      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            body: Center(child: Text('No route defined for ${settings.name}')),
          ),
        );
    }
  }
}
