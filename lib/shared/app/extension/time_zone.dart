import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tzdata;
import 'package:intl/intl.dart';

class TimeZoneHelper {
  static bool _isTimeZoneInitialized = false;

  static void initialize() {
    if (!_isTimeZoneInitialized) {
      tzdata.initializeTimeZones();
      _isTimeZoneInitialized = true;
    }
  }

  static tz.Location getLocation(String timeZone) {
    initialize();
    return tz.getLocation(timeZone);
  }

  static String formatTimeInZone(int epochSeconds, String timeZone, {String format = 'Hm'}) {
    initialize();
    final location = getLocation(timeZone);
    final utcDateTime = DateTime.fromMillisecondsSinceEpoch(epochSeconds * 1000, isUtc: true);
    final localTime = tz.TZDateTime.from(utcDateTime, location);
    return DateFormat(format).format(localTime);
  }

  static tz.TZDateTime dateTimeInTimeZone(int epochSeconds, String timeZone, {String format = 'Hm'}) {
    initialize();
    final location = getLocation(timeZone);
    final utcDateTime = DateTime.fromMillisecondsSinceEpoch(epochSeconds * 1000, isUtc: true);
    final localTime = tz.TZDateTime.from(utcDateTime, location);
    return localTime;
  }
}

Map<String, String> countryTimeZones = {
  'CN': 'Asia/Shanghai',
  'US': 'America/New_York',
  'RU': 'Europe/Moscow',
  'SG': 'Asia/Singapore',
  'GB': 'Europe/London',
  'DE': 'Europe/Berlin',
  'FR': 'Europe/Paris',
  'IN': 'Asia/Kolkata',
  'JP': 'Asia/Tokyo',
  'BR': 'America/Sao_Paulo',
  // 期货市场 Future Exchange
  'SHFE': 'Asia/Shanghai',
  'CZCE': 'Asia/Shanghai',
  'DCE': 'Asia/Shanghai',
  'CFFEX': 'Asia/Shanghai',
  'INE': 'Asia/Shanghai',
};

Map<String, int> countryTimeOffsets = {
  'CN': 4,
  'US': -9,
  'RU': -1,
  'SG': 4,
  'GB': -4,
  'DE': -3,
  'FR': -3,
  'IN': 1,
  'JP': 5,
  'BR': -7,
};
