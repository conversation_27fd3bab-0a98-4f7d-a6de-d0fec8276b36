import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import 'shared/logic/theme/theme_cubit.dart';
import 'shared/routes/navigator.dart';
import 'shared/routes/route_generator.dart';
import 'shared/routes/routes.dart';
import 'shared/theme/app_theme.dart';
import 'shared/widgets/flavor_banner.dart';

class MyApp extends StatefulWidget {
  final RouteObserver<ModalRoute<void>> routeObserver;
  const MyApp({super.key, required this.routeObserver});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  // Supported locales for easy switching in debug mode
  static const List<Locale> _debugLocales = [
    Locale('en', 'US'),
    Locale('zh', 'CN'),
    Locale('zh', 'TW'),
  ];

  int _currentLocaleIndex = 0;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async => FocusManager.instance.primaryFocus?.unfocus(),
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, state) {
          return MaterialApp(
            // Navigation configuration
            navigatorObservers: [widget.routeObserver],
            navigatorKey: navigatorKey,
            initialRoute: routeRoot,
            onGenerateRoute: RouteGenerator.generateRoute,
            // Localization configuration
            localizationsDelegates: context.localizationDelegates,
            supportedLocales: context.supportedLocales,
            locale: context.locale,
            // Theme configuration
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: state.themeMode,
            // App metadata
            title: 'appName'.tr(),
            debugShowCheckedModeBanner: false,
            builder: (context, child) {
              // Initialize GSScreenUtil
              context.initGSScreen(
                uiSize: const Size(375, 812), // Your design size
                maxWidth: 475, // Max width for tablet
              );
              return MediaQuery(
                data: MediaQuery.of(context).copyWith(
                  textScaler: const TextScaler.linear(1),
                ),
                // 18.verticalSpace,
                child: FlutterEasyLoading(
                  child: FlavorBanner(
                    message: AppConfig.instance.flavorTitle,
                    show: kDebugMode,
                    child: Stack(
                      children: [
                        child!,
                        // if (kDebugMode) ..._buildDebugControls(context, state),
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  /// Cycles through available locales in debug mode
  void _cycleLocale(BuildContext context) {
    if (!kDebugMode) return;

    _currentLocaleIndex = (_currentLocaleIndex + 1) % _debugLocales.length;
    final newLocale = _debugLocales[_currentLocaleIndex];
    context.setLocale(newLocale);
  }

  /// Toggles between light and dark theme
  void _toggleTheme(BuildContext context, ThemeState state) {
    final newTheme = state.themeMode == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;
    context.read<ThemeCubit>().changeTheme(newTheme);
  }

  /// Builds debug-only controls for theme and locale switching
  List<Widget> _buildDebugControls(BuildContext context, ThemeState state) {
    return [
      // Theme toggle button
      Positioned(
        top: 15,
        right: 10,
        child: FloatingActionButton(
          heroTag: 'theme_toggle',
          tooltip: 'Toggle Theme',
          onPressed: () => _toggleTheme(context, state),
          child: Icon(
            state.themeMode == ThemeMode.light ? Icons.dark_mode : Icons.light_mode,
          ),
        ),
      ),

      // Language toggle button
      Positioned(
        top: 15,
        left: 10,
        child: FloatingActionButton(
          heroTag: 'locale_toggle',
          tooltip: 'Change Language',
          onPressed: () => _cycleLocale(context),
          child: const Icon(Icons.language),
        ),
      ),
    ];
  }
}
