import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_config_model.dart';

FTradeConfigModel $FTradeConfigModelFromJson(Map<String, dynamic> json) {
  final FTradeConfigModel fTradeConfigModel = FTradeConfigModel();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    fTradeConfigModel.id = id;
  }
  final String? market = jsonConvert.convert<String>(json['market']);
  if (market != null) {
    fTradeConfigModel.market = market;
  }
  final double? marginRadio = jsonConvert.convert<double>(json['marginRadio']);
  if (marginRadio != null) {
    fTradeConfigModel.marginRadio = marginRadio;
  }
  final double? closeRadio = jsonConvert.convert<double>(json['closeRadio']);
  if (closeRadio != null) {
    fTradeConfigModel.closeRadio = closeRadio;
  }
  final String? currency = jsonConvert.convert<String>(json['currency']);
  if (currency != null) {
    fTradeConfigModel.currency = currency;
  }
  final double? maxEmptyMatch = jsonConvert.convert<double>(
      json['maxEmptyMatch']);
  if (maxEmptyMatch != null) {
    fTradeConfigModel.maxEmptyMatch = maxEmptyMatch;
  }
  final double? minEmptyMatch = jsonConvert.convert<double>(
      json['minEmptyMatch']);
  if (minEmptyMatch != null) {
    fTradeConfigModel.minEmptyMatch = minEmptyMatch;
  }
  final double? maxManyMatch = jsonConvert.convert<double>(
      json['maxManyMatch']);
  if (maxManyMatch != null) {
    fTradeConfigModel.maxManyMatch = maxManyMatch;
  }
  final double? minManyMatch = jsonConvert.convert<double>(
      json['minManyMatch']);
  if (minManyMatch != null) {
    fTradeConfigModel.minManyMatch = minManyMatch;
  }
  final double? maxIncrease = jsonConvert.convert<double>(json['maxIncrease']);
  if (maxIncrease != null) {
    fTradeConfigModel.maxIncrease = maxIncrease;
  }
  final double? minIncrease = jsonConvert.convert<double>(json['minIncrease']);
  if (minIncrease != null) {
    fTradeConfigModel.minIncrease = minIncrease;
  }
  final double? maxTradeQuantity = jsonConvert.convert<double>(
      json['maxTradeQuantity']);
  if (maxTradeQuantity != null) {
    fTradeConfigModel.maxTradeQuantity = maxTradeQuantity;
  }
  final double? minTradeQuantity = jsonConvert.convert<double>(
      json['minTradeQuantity']);
  if (minTradeQuantity != null) {
    fTradeConfigModel.minTradeQuantity = minTradeQuantity;
  }
  final int? multiple = jsonConvert.convert<int>(json['multiple']);
  if (multiple != null) {
    fTradeConfigModel.multiple = multiple;
  }
  final int? rebateStatus = jsonConvert.convert<int>(json['rebateStatus']);
  if (rebateStatus != null) {
    fTradeConfigModel.rebateStatus = rebateStatus;
  }
  final int? reverseTradeStatus = jsonConvert.convert<int>(
      json['reverseTradeStatus']);
  if (reverseTradeStatus != null) {
    fTradeConfigModel.reverseTradeStatus = reverseTradeStatus;
  }
  final int? sellModel = jsonConvert.convert<int>(json['sellModel']);
  if (sellModel != null) {
    fTradeConfigModel.sellModel = sellModel;
  }
  final int? sellValue = jsonConvert.convert<int>(json['sellValue']);
  if (sellValue != null) {
    fTradeConfigModel.sellValue = sellValue;
  }
  final bool? status = jsonConvert.convert<bool>(json['status']);
  if (status != null) {
    fTradeConfigModel.status = status;
  }
  final String? symbol = jsonConvert.convert<String>(json['symbol']);
  if (symbol != null) {
    fTradeConfigModel.symbol = symbol;
  }
  final int? tradeModel = jsonConvert.convert<int>(json['tradeModel']);
  if (tradeModel != null) {
    fTradeConfigModel.tradeModel = tradeModel;
  }
  final double? warnRadio = jsonConvert.convert<double>(json['warnRadio']);
  if (warnRadio != null) {
    fTradeConfigModel.warnRadio = warnRadio;
  }
  return fTradeConfigModel;
}

Map<String, dynamic> $FTradeConfigModelToJson(FTradeConfigModel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['market'] = entity.market;
  data['marginRadio'] = entity.marginRadio;
  data['closeRadio'] = entity.closeRadio;
  data['currency'] = entity.currency;
  data['maxEmptyMatch'] = entity.maxEmptyMatch;
  data['minEmptyMatch'] = entity.minEmptyMatch;
  data['maxManyMatch'] = entity.maxManyMatch;
  data['minManyMatch'] = entity.minManyMatch;
  data['maxIncrease'] = entity.maxIncrease;
  data['minIncrease'] = entity.minIncrease;
  data['maxTradeQuantity'] = entity.maxTradeQuantity;
  data['minTradeQuantity'] = entity.minTradeQuantity;
  data['multiple'] = entity.multiple;
  data['rebateStatus'] = entity.rebateStatus;
  data['reverseTradeStatus'] = entity.reverseTradeStatus;
  data['sellModel'] = entity.sellModel;
  data['sellValue'] = entity.sellValue;
  data['status'] = entity.status;
  data['symbol'] = entity.symbol;
  data['tradeModel'] = entity.tradeModel;
  data['warnRadio'] = entity.warnRadio;
  return data;
}

extension FTradeConfigModelExtension on FTradeConfigModel {
  FTradeConfigModel copyWith({
    int? id,
    String? market,
    double? marginRadio,
    double? closeRadio,
    String? currency,
    double? maxEmptyMatch,
    double? minEmptyMatch,
    double? maxManyMatch,
    double? minManyMatch,
    double? maxIncrease,
    double? minIncrease,
    double? maxTradeQuantity,
    double? minTradeQuantity,
    int? multiple,
    int? rebateStatus,
    int? reverseTradeStatus,
    int? sellModel,
    int? sellValue,
    bool? status,
    String? symbol,
    int? tradeModel,
    double? warnRadio,
  }) {
    return FTradeConfigModel()
      ..id = id ?? this.id
      ..market = market ?? this.market
      ..marginRadio = marginRadio ?? this.marginRadio
      ..closeRadio = closeRadio ?? this.closeRadio
      ..currency = currency ?? this.currency
      ..maxEmptyMatch = maxEmptyMatch ?? this.maxEmptyMatch
      ..minEmptyMatch = minEmptyMatch ?? this.minEmptyMatch
      ..maxManyMatch = maxManyMatch ?? this.maxManyMatch
      ..minManyMatch = minManyMatch ?? this.minManyMatch
      ..maxIncrease = maxIncrease ?? this.maxIncrease
      ..minIncrease = minIncrease ?? this.minIncrease
      ..maxTradeQuantity = maxTradeQuantity ?? this.maxTradeQuantity
      ..minTradeQuantity = minTradeQuantity ?? this.minTradeQuantity
      ..multiple = multiple ?? this.multiple
      ..rebateStatus = rebateStatus ?? this.rebateStatus
      ..reverseTradeStatus = reverseTradeStatus ?? this.reverseTradeStatus
      ..sellModel = sellModel ?? this.sellModel
      ..sellValue = sellValue ?? this.sellValue
      ..status = status ?? this.status
      ..symbol = symbol ?? this.symbol
      ..tradeModel = tradeModel ?? this.tradeModel
      ..warnRadio = warnRadio ?? this.warnRadio;
  }
}