import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/features/sign_in/domain/models/login/login_response.dart';

LoginResponse $LoginResponseFromJson(Map<String, dynamic> json) {
  final LoginResponse loginResponse = LoginResponse();
  final int? code = jsonConvert.convert<int>(json['code']);
  if (code != null) {
    loginResponse.code = code;
  }
  final UserData? data = jsonConvert.convert<UserData>(json['data']);
  if (data != null) {
    loginResponse.data = data;
  }
  final String? msg = jsonConvert.convert<String>(json['msg']);
  if (msg != null) {
    loginResponse.msg = msg;
  }
  return loginResponse;
}

Map<String, dynamic> $LoginResponseToJson(LoginResponse entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  data['data'] = entity.data?.toJson();
  data['msg'] = entity.msg;
  return data;
}

extension LoginResponseExtension on LoginResponse {
  LoginResponse copyWith({
    int? code,
    UserData? data,
    String? msg,
  }) {
    return LoginResponse()
      ..code = code ?? this.code
      ..data = data ?? this.data
      ..msg = msg ?? this.msg;
  }
}

UserData $UserDataFromJson(Map<String, dynamic> json) {
  final UserData userData = UserData();
  final bool? auth = jsonConvert.convert<bool>(json['auth']);
  if (auth != null) {
    userData.auth = auth;
  }
  final int? authStatus = jsonConvert.convert<int>(json['authStatus']);
  if (authStatus != null) {
    userData.authStatus = authStatus;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    userData.avatar = avatar;
  }
  final String? countryCode = jsonConvert.convert<String>(json['countryCode']);
  if (countryCode != null) {
    userData.countryCode = countryCode;
  }
  final String? email = jsonConvert.convert<String>(json['email']);
  if (email != null) {
    userData.email = email;
  }
  final int? fromType = jsonConvert.convert<int>(json['fromType']);
  if (fromType != null) {
    userData.fromType = fromType;
  }
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    userData.id = id;
  }
  final String? idCard = jsonConvert.convert<String>(json['idCard']);
  if (idCard != null) {
    userData.idCard = idCard;
  }
  final String? inviteCode = jsonConvert.convert<String>(json['inviteCode']);
  if (inviteCode != null) {
    userData.inviteCode = inviteCode;
  }
  final bool? isPayment = jsonConvert.convert<bool>(json['isPayment']);
  if (isPayment != null) {
    userData.isPayment = isPayment;
  }
  final int? level = jsonConvert.convert<int>(json['level']);
  if (level != null) {
    userData.level = level;
  }
  final String? mobile = jsonConvert.convert<String>(json['mobile']);
  if (mobile != null) {
    userData.mobile = mobile;
  }
  final String? nickname = jsonConvert.convert<String>(json['nickname']);
  if (nickname != null) {
    userData.nickname = nickname;
  }
  final int? pid = jsonConvert.convert<int>(json['pid']);
  if (pid != null) {
    userData.pid = pid;
  }
  final String? profiles = jsonConvert.convert<String>(json['profiles']);
  if (profiles != null) {
    userData.profiles = profiles;
  }
  final String? realName = jsonConvert.convert<String>(json['realName']);
  if (realName != null) {
    userData.realName = realName;
  }
  final int? score = jsonConvert.convert<int>(json['score']);
  if (score != null) {
    userData.score = score;
  }
  final int? sex = jsonConvert.convert<int>(json['sex']);
  if (sex != null) {
    userData.sex = sex;
  }
  final bool? status = jsonConvert.convert<bool>(json['status']);
  if (status != null) {
    userData.status = status;
  }
  final int? tradeStatus = jsonConvert.convert<int>(json['tradeStatus']);
  if (tradeStatus != null) {
    userData.tradeStatus = tradeStatus;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    userData.type = type;
  }
  return userData;
}

Map<String, dynamic> $UserDataToJson(UserData entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['auth'] = entity.auth;
  data['authStatus'] = entity.authStatus;
  data['avatar'] = entity.avatar;
  data['countryCode'] = entity.countryCode;
  data['email'] = entity.email;
  data['fromType'] = entity.fromType;
  data['id'] = entity.id;
  data['idCard'] = entity.idCard;
  data['inviteCode'] = entity.inviteCode;
  data['isPayment'] = entity.isPayment;
  data['level'] = entity.level;
  data['mobile'] = entity.mobile;
  data['nickname'] = entity.nickname;
  data['pid'] = entity.pid;
  data['profiles'] = entity.profiles;
  data['realName'] = entity.realName;
  data['score'] = entity.score;
  data['sex'] = entity.sex;
  data['status'] = entity.status;
  data['tradeStatus'] = entity.tradeStatus;
  data['type'] = entity.type;
  return data;
}

extension UserDataExtension on UserData {
  UserData copyWith({
    bool? auth,
    int? authStatus,
    String? avatar,
    String? countryCode,
    String? email,
    int? fromType,
    int? id,
    String? idCard,
    String? inviteCode,
    bool? isPayment,
    int? level,
    String? mobile,
    String? nickname,
    int? pid,
    String? profiles,
    String? realName,
    int? score,
    int? sex,
    bool? status,
    int? tradeStatus,
    int? type,
  }) {
    return UserData()
      ..auth = auth ?? this.auth
      ..authStatus = authStatus ?? this.authStatus
      ..avatar = avatar ?? this.avatar
      ..countryCode = countryCode ?? this.countryCode
      ..email = email ?? this.email
      ..fromType = fromType ?? this.fromType
      ..id = id ?? this.id
      ..idCard = idCard ?? this.idCard
      ..inviteCode = inviteCode ?? this.inviteCode
      ..isPayment = isPayment ?? this.isPayment
      ..level = level ?? this.level
      ..mobile = mobile ?? this.mobile
      ..nickname = nickname ?? this.nickname
      ..pid = pid ?? this.pid
      ..profiles = profiles ?? this.profiles
      ..realName = realName ?? this.realName
      ..score = score ?? this.score
      ..sex = sex ?? this.sex
      ..status = status ?? this.status
      ..tradeStatus = tradeStatus ?? this.tradeStatus
      ..type = type ?? this.type;
  }
}