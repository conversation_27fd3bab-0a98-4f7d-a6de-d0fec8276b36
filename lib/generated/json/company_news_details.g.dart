import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/features/company_news/domain/models/company_news_details.dart';

CompanyNewsDetailsResponse $CompanyNewsDetailsResponseFromJson(Map<String, dynamic> json) {
  final CompanyNewsDetailsResponse companyNewsDetailsResponse = CompanyNewsDetailsResponse();
  final int? code = jsonConvert.convert<int>(json['code']);
  if (code != null) {
    companyNewsDetailsResponse.code = code;
  }
  final CompanyNewsDetailsData? data = jsonConvert.convert<CompanyNewsDetailsData>(json['data']);
  if (data != null) {
    companyNewsDetailsResponse.data = data;
  }
  final String? msg = jsonConvert.convert<String>(json['msg']);
  if (msg != null) {
    companyNewsDetailsResponse.msg = msg;
  }
  return companyNewsDetailsResponse;
}

Map<String, dynamic> $CompanyNewsDetailsResponseToJson(CompanyNewsDetailsResponse entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  data['data'] = entity.data?.toJson();
  data['msg'] = entity.msg;
  return data;
}

extension CompanyNewsDetailsResponseExtension on CompanyNewsDetailsResponse {
  CompanyNewsDetailsResponse copyWith({
    int? code,
    CompanyNewsDetailsData? data,
    String? msg,
  }) {
    return CompanyNewsDetailsResponse()
      ..code = code ?? this.code
      ..data = data ?? this.data
      ..msg = msg ?? this.msg;
  }
}

CompanyNewsDetailsData $CompanyNewsDetailsDataFromJson(Map<String, dynamic> json) {
  final CompanyNewsDetailsData companyNewsDetailsData = CompanyNewsDetailsData();
  final String? articleTitle = jsonConvert.convert<String>(json['article_title']);
  if (articleTitle != null) {
    companyNewsDetailsData.articleTitle = articleTitle;
  }
  final String? articleMarket = jsonConvert.convert<String>(json['article_market']);
  if (articleMarket != null) {
    companyNewsDetailsData.articleMarket = articleMarket;
  }
  final String? articleSymbol = jsonConvert.convert<String>(json['article_symbol']);
  if (articleSymbol != null) {
    companyNewsDetailsData.articleSymbol = articleSymbol;
  }
  final String? articleAuth = jsonConvert.convert<String>(json['article_auth']);
  if (articleAuth != null) {
    companyNewsDetailsData.articleAuth = articleAuth;
  }
  final String? articleContent = jsonConvert.convert<String>(json['article_content']);
  if (articleContent != null) {
    companyNewsDetailsData.articleContent = articleContent;
  }
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    companyNewsDetailsData.id = id;
  }
  final String? articleDate = jsonConvert.convert<String>(json['article_date']);
  if (articleDate != null) {
    companyNewsDetailsData.articleDate = articleDate;
  }
  return companyNewsDetailsData;
}

Map<String, dynamic> $CompanyNewsDetailsDataToJson(CompanyNewsDetailsData entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['article_title'] = entity.articleTitle;
  data['article_market'] = entity.articleMarket;
  data['article_symbol'] = entity.articleSymbol;
  data['article_auth'] = entity.articleAuth;
  data['article_content'] = entity.articleContent;
  data['id'] = entity.id;
  data['article_date'] = entity.articleDate;
  return data;
}

extension CompanyNewsDetailsDataExtension on CompanyNewsDetailsData {
  CompanyNewsDetailsData copyWith({
    String? articleTitle,
    String? articleMarket,
    String? articleSymbol,
    String? articleAuth,
    String? articleContent,
    String? id,
    String? articleDate,
  }) {
    return CompanyNewsDetailsData()
      ..articleTitle = articleTitle ?? this.articleTitle
      ..articleMarket = articleMarket ?? this.articleMarket
      ..articleSymbol = articleSymbol ?? this.articleSymbol
      ..articleAuth = articleAuth ?? this.articleAuth
      ..articleContent = articleContent ?? this.articleContent
      ..id = id ?? this.id
      ..articleDate = articleDate ?? this.articleDate;
  }
}
