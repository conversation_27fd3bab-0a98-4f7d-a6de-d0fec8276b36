import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/account.dart';
import 'package:equatable/equatable.dart';


AccountInfo $AccountInfoFromJson(Map<String, dynamic> json) {
  final AccountInfo accountInfo = AccountInfo();
  final double? accountAmount = jsonConvert.convert<double>(
      json['accountAmount']);
  if (accountAmount != null) {
    accountInfo.accountAmount = accountAmount;
  }
  final double? assetAmount = jsonConvert.convert<double>(json['assetAmount']);
  if (assetAmount != null) {
    accountInfo.assetAmount = assetAmount;
  }
  final int? assetId = jsonConvert.convert<int>(json['assetId']);
  if (assetId != null) {
    accountInfo.assetId = assetId;
  }
  final String? currency = jsonConvert.convert<String>(json['currency']);
  if (currency != null) {
    accountInfo.currency = currency;
  }
  final double? freezeCash = jsonConvert.convert<double>(json['freezeCash']);
  if (freezeCash != null) {
    accountInfo.freezeCash = freezeCash;
  }
  final double? interestCash = jsonConvert.convert<double>(
      json['interestCash']);
  if (interestCash != null) {
    accountInfo.interestCash = interestCash;
  }
  final double? todayWinAmount = jsonConvert.convert<double>(
      json['todayWinAmount']);
  if (todayWinAmount != null) {
    accountInfo.todayWinAmount = todayWinAmount;
  }
  final double? todayWinRate = jsonConvert.convert<double>(
      json['todayWinRate']);
  if (todayWinRate != null) {
    accountInfo.todayWinRate = todayWinRate;
  }
  final double? usableCash = jsonConvert.convert<double>(json['usableCash']);
  if (usableCash != null) {
    accountInfo.usableCash = usableCash;
  }
  final double? profitLoss = jsonConvert.convert<double>(json['profitLoss']);
  if (profitLoss != null) {
    accountInfo.profitLoss = profitLoss;
  }
  return accountInfo;
}

Map<String, dynamic> $AccountInfoToJson(AccountInfo entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['accountAmount'] = entity.accountAmount;
  data['assetAmount'] = entity.assetAmount;
  data['assetId'] = entity.assetId;
  data['currency'] = entity.currency;
  data['freezeCash'] = entity.freezeCash;
  data['interestCash'] = entity.interestCash;
  data['todayWinAmount'] = entity.todayWinAmount;
  data['todayWinRate'] = entity.todayWinRate;
  data['usableCash'] = entity.usableCash;
  data['profitLoss'] = entity.profitLoss;
  return data;
}

extension AccountInfoExtension on AccountInfo {
  AccountInfo copyWith({
    double? accountAmount,
    double? assetAmount,
    int? assetId,
    String? currency,
    double? freezeCash,
    double? interestCash,
    double? todayWinAmount,
    double? todayWinRate,
    double? usableCash,
    double? profitLoss,
  }) {
    return AccountInfo()
      ..accountAmount = accountAmount ?? this.accountAmount
      ..assetAmount = assetAmount ?? this.assetAmount
      ..assetId = assetId ?? this.assetId
      ..currency = currency ?? this.currency
      ..freezeCash = freezeCash ?? this.freezeCash
      ..interestCash = interestCash ?? this.interestCash
      ..todayWinAmount = todayWinAmount ?? this.todayWinAmount
      ..todayWinRate = todayWinRate ?? this.todayWinRate
      ..usableCash = usableCash ?? this.usableCash
      ..profitLoss = profitLoss ?? this.profitLoss;
  }
}

PositionEntity $PositionEntityFromJson(Map<String, dynamic> json) {
  final PositionEntity positionEntity = PositionEntity();
  final double? appendMargin = jsonConvert.convert<double>(
      json['appendMargin']);
  if (appendMargin != null) {
    positionEntity.appendMargin = appendMargin;
  }
  final int? availableMargin = jsonConvert.convert<int>(
      json['availableMargin']);
  if (availableMargin != null) {
    positionEntity.availableMargin = availableMargin;
  }
  final double? buyAvgPrice = jsonConvert.convert<double>(json['buyAvgPrice']);
  if (buyAvgPrice != null) {
    positionEntity.buyAvgPrice = buyAvgPrice;
  }
  final double? buyTotalNum = jsonConvert.convert<double>(json['buyTotalNum']);
  if (buyTotalNum != null) {
    positionEntity.buyTotalNum = buyTotalNum;
  }
  final dynamic closeLine = json['closeLine'];
  if (closeLine != null) {
    positionEntity.closeLine = closeLine;
  }
  final double? costPrice = jsonConvert.convert<double>(json['costPrice']);
  if (costPrice != null) {
    positionEntity.costPrice = costPrice;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    positionEntity.createTime = createTime;
  }
  final String? currency = jsonConvert.convert<String>(json['currency']);
  if (currency != null) {
    positionEntity.currency = currency;
  }
  final int? direction = jsonConvert.convert<int>(json['direction']);
  if (direction != null) {
    positionEntity.direction = direction;
  }
  final double? disableNum = jsonConvert.convert<double>(json['disableNum']);
  if (disableNum != null) {
    positionEntity.disableNum = disableNum;
  }
  final double? distanceCloseLine = jsonConvert.convert<double>(
      json['distanceCloseLine']);
  if (distanceCloseLine != null) {
    positionEntity.distanceCloseLine = distanceCloseLine;
  }
  final double? distanceWarningLine = jsonConvert.convert<double>(
      json['distanceWarningLine']);
  if (distanceWarningLine != null) {
    positionEntity.distanceWarningLine = distanceWarningLine;
  }
  final double? feeAmount = jsonConvert.convert<double>(json['feeAmount']);
  if (feeAmount != null) {
    positionEntity.feeAmount = feeAmount;
  }
  final double? floatingProfitLoss = jsonConvert.convert<double>(
      json['floatingProfitLoss']);
  if (floatingProfitLoss != null) {
    positionEntity.floatingProfitLoss = floatingProfitLoss;
  }
  final double? floatingProfitLossRate = jsonConvert.convert<double>(
      json['floatingProfitLossRate']);
  if (floatingProfitLossRate != null) {
    positionEntity.floatingProfitLossRate = floatingProfitLossRate;
  }
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    positionEntity.id = id;
  }
  final double? marginAmount = jsonConvert.convert<double>(
      json['marginAmount']);
  if (marginAmount != null) {
    positionEntity.marginAmount = marginAmount;
  }
  final double? marginRatio = jsonConvert.convert<double>(json['marginRatio']);
  if (marginRatio != null) {
    positionEntity.marginRatio = marginRatio;
  }
  final String? market = jsonConvert.convert<String>(json['market']);
  if (market != null) {
    positionEntity.market = market;
  }
  final double? marketValue = jsonConvert.convert<double>(json['marketValue']);
  if (marketValue != null) {
    positionEntity.marketValue = marketValue;
  }
  final String? orderNo = jsonConvert.convert<String>(json['orderNo']);
  if (orderNo != null) {
    positionEntity.orderNo = orderNo;
  }
  final int? positionDays = jsonConvert.convert<int>(json['positionDays']);
  if (positionDays != null) {
    positionEntity.positionDays = positionDays;
  }
  final double? positionTotalNum = jsonConvert.convert<double>(
      json['positionTotalNum']);
  if (positionTotalNum != null) {
    positionEntity.positionTotalNum = positionTotalNum;
  }
  final double? restNum = jsonConvert.convert<double>(json['restNum']);
  if (restNum != null) {
    positionEntity.restNum = restNum;
  }
  final String? securityType = jsonConvert.convert<String>(
      json['securityType']);
  if (securityType != null) {
    positionEntity.securityType = securityType;
  }
  final int? stockPrice = jsonConvert.convert<int>(json['stockPrice']);
  if (stockPrice != null) {
    positionEntity.stockPrice = stockPrice;
  }
  final double? stopLossValue = jsonConvert.convert<double>(
      json['stopLossValue']);
  if (stopLossValue != null) {
    positionEntity.stopLossValue = stopLossValue;
  }
  final String? symbol = jsonConvert.convert<String>(json['symbol']);
  if (symbol != null) {
    positionEntity.symbol = symbol;
  }
  final String? symbolName = jsonConvert.convert<String>(json['symbolName']);
  if (symbolName != null) {
    positionEntity.symbolName = symbolName;
  }
  final double? takeProfitValue = jsonConvert.convert<double>(
      json['takeProfitValue']);
  if (takeProfitValue != null) {
    positionEntity.takeProfitValue = takeProfitValue;
  }
  final int? tradeType = jsonConvert.convert<int>(json['tradeType']);
  if (tradeType != null) {
    positionEntity.tradeType = tradeType;
  }
  final double? tradeUnit = jsonConvert.convert<double>(json['tradeUnit']);
  if (tradeUnit != null) {
    positionEntity.tradeUnit = tradeUnit;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    positionEntity.type = type;
  }
  final int? warningLine = jsonConvert.convert<int>(json['warningLine']);
  if (warningLine != null) {
    positionEntity.warningLine = warningLine;
  }
  return positionEntity;
}

Map<String, dynamic> $PositionEntityToJson(PositionEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['appendMargin'] = entity.appendMargin;
  data['availableMargin'] = entity.availableMargin;
  data['buyAvgPrice'] = entity.buyAvgPrice;
  data['buyTotalNum'] = entity.buyTotalNum;
  data['closeLine'] = entity.closeLine;
  data['costPrice'] = entity.costPrice;
  data['createTime'] = entity.createTime;
  data['currency'] = entity.currency;
  data['direction'] = entity.direction;
  data['disableNum'] = entity.disableNum;
  data['distanceCloseLine'] = entity.distanceCloseLine;
  data['distanceWarningLine'] = entity.distanceWarningLine;
  data['feeAmount'] = entity.feeAmount;
  data['floatingProfitLoss'] = entity.floatingProfitLoss;
  data['floatingProfitLossRate'] = entity.floatingProfitLossRate;
  data['id'] = entity.id;
  data['marginAmount'] = entity.marginAmount;
  data['marginRatio'] = entity.marginRatio;
  data['market'] = entity.market;
  data['marketValue'] = entity.marketValue;
  data['orderNo'] = entity.orderNo;
  data['positionDays'] = entity.positionDays;
  data['positionTotalNum'] = entity.positionTotalNum;
  data['restNum'] = entity.restNum;
  data['securityType'] = entity.securityType;
  data['stockPrice'] = entity.stockPrice;
  data['stopLossValue'] = entity.stopLossValue;
  data['symbol'] = entity.symbol;
  data['symbolName'] = entity.symbolName;
  data['takeProfitValue'] = entity.takeProfitValue;
  data['tradeType'] = entity.tradeType;
  data['tradeUnit'] = entity.tradeUnit;
  data['type'] = entity.type;
  data['warningLine'] = entity.warningLine;
  return data;
}

extension PositionEntityExtension on PositionEntity {
  PositionEntity copyWith({
    double? appendMargin,
    int? availableMargin,
    double? buyAvgPrice,
    double? buyTotalNum,
    dynamic closeLine,
    double? costPrice,
    String? createTime,
    String? currency,
    int? direction,
    double? disableNum,
    double? distanceCloseLine,
    double? distanceWarningLine,
    double? feeAmount,
    double? floatingProfitLoss,
    double? floatingProfitLossRate,
    int? id,
    double? marginAmount,
    double? marginRatio,
    String? market,
    double? marketValue,
    String? orderNo,
    int? positionDays,
    double? positionTotalNum,
    double? restNum,
    String? securityType,
    int? stockPrice,
    double? stopLossValue,
    String? symbol,
    String? symbolName,
    double? takeProfitValue,
    int? tradeType,
    double? tradeUnit,
    int? type,
    int? warningLine,
  }) {
    return PositionEntity()
      ..appendMargin = appendMargin ?? this.appendMargin
      ..availableMargin = availableMargin ?? this.availableMargin
      ..buyAvgPrice = buyAvgPrice ?? this.buyAvgPrice
      ..buyTotalNum = buyTotalNum ?? this.buyTotalNum
      ..closeLine = closeLine ?? this.closeLine
      ..costPrice = costPrice ?? this.costPrice
      ..createTime = createTime ?? this.createTime
      ..currency = currency ?? this.currency
      ..direction = direction ?? this.direction
      ..disableNum = disableNum ?? this.disableNum
      ..distanceCloseLine = distanceCloseLine ?? this.distanceCloseLine
      ..distanceWarningLine = distanceWarningLine ?? this.distanceWarningLine
      ..feeAmount = feeAmount ?? this.feeAmount
      ..floatingProfitLoss = floatingProfitLoss ?? this.floatingProfitLoss
      ..floatingProfitLossRate = floatingProfitLossRate ??
          this.floatingProfitLossRate
      ..id = id ?? this.id
      ..marginAmount = marginAmount ?? this.marginAmount
      ..marginRatio = marginRatio ?? this.marginRatio
      ..market = market ?? this.market
      ..marketValue = marketValue ?? this.marketValue
      ..orderNo = orderNo ?? this.orderNo
      ..positionDays = positionDays ?? this.positionDays
      ..positionTotalNum = positionTotalNum ?? this.positionTotalNum
      ..restNum = restNum ?? this.restNum
      ..securityType = securityType ?? this.securityType
      ..stockPrice = stockPrice ?? this.stockPrice
      ..stopLossValue = stopLossValue ?? this.stopLossValue
      ..symbol = symbol ?? this.symbol
      ..symbolName = symbolName ?? this.symbolName
      ..takeProfitValue = takeProfitValue ?? this.takeProfitValue
      ..tradeType = tradeType ?? this.tradeType
      ..tradeUnit = tradeUnit ?? this.tradeUnit
      ..type = type ?? this.type
      ..warningLine = warningLine ?? this.warningLine;
  }
}