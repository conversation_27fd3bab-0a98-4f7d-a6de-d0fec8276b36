import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_depth_model.dart';

FTradeDepthModel $FTradeDepthModelFromJson(Map<String, dynamic> json) {
  final FTradeDepthModel fTradeDepthModel = FTradeDepthModel();
  final String? market = jsonConvert.convert<String>(json['market']);
  if (market != null) {
    fTradeDepthModel.market = market;
  }
  final double? latestPrice = jsonConvert.convert<double>(json['latestPrice']);
  if (latestPrice != null) {
    fTradeDepthModel.latestPrice = latestPrice;
  }
  final String? symbol = jsonConvert.convert<String>(json['symbol']);
  if (symbol != null) {
    fTradeDepthModel.symbol = symbol;
  }
  final String? securityType = jsonConvert.convert<String>(
      json['securityType']);
  if (securityType != null) {
    fTradeDepthModel.securityType = securityType;
  }
  final double? close = jsonConvert.convert<double>(json['close']);
  if (close != null) {
    fTradeDepthModel.close = close;
  }
  final List<FTradeDepthAsk>? ask = (json['ask'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<FTradeDepthAsk>(e) as FTradeDepthAsk)
      .toList();
  if (ask != null) {
    fTradeDepthModel.ask = ask;
  }
  final List<FTradeDepthBid>? bid = (json['bid'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<FTradeDepthBid>(e) as FTradeDepthBid)
      .toList();
  if (bid != null) {
    fTradeDepthModel.bid = bid;
  }
  return fTradeDepthModel;
}

Map<String, dynamic> $FTradeDepthModelToJson(FTradeDepthModel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['market'] = entity.market;
  data['latestPrice'] = entity.latestPrice;
  data['symbol'] = entity.symbol;
  data['securityType'] = entity.securityType;
  data['close'] = entity.close;
  data['ask'] = entity.ask.map((v) => v.toJson()).toList();
  data['bid'] = entity.bid.map((v) => v.toJson()).toList();
  return data;
}

extension FTradeDepthModelExtension on FTradeDepthModel {
  FTradeDepthModel copyWith({
    String? market,
    double? latestPrice,
    String? symbol,
    String? securityType,
    double? close,
    List<FTradeDepthAsk>? ask,
    List<FTradeDepthBid>? bid,
  }) {
    return FTradeDepthModel()
      ..market = market ?? this.market
      ..latestPrice = latestPrice ?? this.latestPrice
      ..symbol = symbol ?? this.symbol
      ..securityType = securityType ?? this.securityType
      ..close = close ?? this.close
      ..ask = ask ?? this.ask
      ..bid = bid ?? this.bid;
  }
}

FTradeDepthAsk $FTradeDepthAskFromJson(Map<String, dynamic> json) {
  final FTradeDepthAsk fTradeDepthAsk = FTradeDepthAsk();
  final int? no = jsonConvert.convert<int>(json['no']);
  if (no != null) {
    fTradeDepthAsk.no = no;
  }
  final int? vol = jsonConvert.convert<int>(json['vol']);
  if (vol != null) {
    fTradeDepthAsk.vol = vol;
  }
  final double? price = jsonConvert.convert<double>(json['price']);
  if (price != null) {
    fTradeDepthAsk.price = price;
  }
  final int? depthNo = jsonConvert.convert<int>(json['depthNo']);
  if (depthNo != null) {
    fTradeDepthAsk.depthNo = depthNo;
  }
  return fTradeDepthAsk;
}

Map<String, dynamic> $FTradeDepthAskToJson(FTradeDepthAsk entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['no'] = entity.no;
  data['vol'] = entity.vol;
  data['price'] = entity.price;
  data['depthNo'] = entity.depthNo;
  return data;
}

extension FTradeDepthAskExtension on FTradeDepthAsk {
  FTradeDepthAsk copyWith({
    int? no,
    int? vol,
    double? price,
    int? depthNo,
  }) {
    return FTradeDepthAsk()
      ..no = no ?? this.no
      ..vol = vol ?? this.vol
      ..price = price ?? this.price
      ..depthNo = depthNo ?? this.depthNo;
  }
}

FTradeDepthBid $FTradeDepthBidFromJson(Map<String, dynamic> json) {
  final FTradeDepthBid fTradeDepthBid = FTradeDepthBid();
  final int? no = jsonConvert.convert<int>(json['no']);
  if (no != null) {
    fTradeDepthBid.no = no;
  }
  final int? vol = jsonConvert.convert<int>(json['vol']);
  if (vol != null) {
    fTradeDepthBid.vol = vol;
  }
  final double? price = jsonConvert.convert<double>(json['price']);
  if (price != null) {
    fTradeDepthBid.price = price;
  }
  final int? depthNo = jsonConvert.convert<int>(json['depthNo']);
  if (depthNo != null) {
    fTradeDepthBid.depthNo = depthNo;
  }
  return fTradeDepthBid;
}

Map<String, dynamic> $FTradeDepthBidToJson(FTradeDepthBid entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['no'] = entity.no;
  data['vol'] = entity.vol;
  data['price'] = entity.price;
  data['depthNo'] = entity.depthNo;
  return data;
}

extension FTradeDepthBidExtension on FTradeDepthBid {
  FTradeDepthBid copyWith({
    int? no,
    int? vol,
    double? price,
    int? depthNo,
  }) {
    return FTradeDepthBid()
      ..no = no ?? this.no
      ..vol = vol ?? this.vol
      ..price = price ?? this.price
      ..depthNo = depthNo ?? this.depthNo;
  }
}