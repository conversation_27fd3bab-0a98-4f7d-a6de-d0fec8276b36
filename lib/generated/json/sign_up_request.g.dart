import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/features/sign_up/domain/models/sign_up_request.dart';

SignUpRequest $SignUpRequestFromJson(Map<String, dynamic> json) {
  final SignUpRequest signUpRequest = SignUpRequest();
  final String? mobile = jsonConvert.convert<String>(json['mobile']);
  if (mobile != null) {
    signUpRequest.mobile = mobile;
  }
  final String? password = jsonConvert.convert<String>(json['password']);
  if (password != null) {
    signUpRequest.password = password;
  }
  final String? inviteCode = jsonConvert.convert<String>(json['inviteCode']);
  if (inviteCode != null) {
    signUpRequest.inviteCode = inviteCode;
  }
  final String? smsCode = jsonConvert.convert<String>(json['smsCode']);
  if (smsCode != null) {
    signUpRequest.smsCode = smsCode;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    signUpRequest.avatar = avatar;
  }
  final String? countryCode = jsonConvert.convert<String>(json['countryCode']);
  if (countryCode != null) {
    signUpRequest.countryCode = countryCode;
  }
  final String? email = jsonConvert.convert<String>(json['email']);
  if (email != null) {
    signUpRequest.email = email;
  }
  final String? nickname = jsonConvert.convert<String>(json['nickname']);
  if (nickname != null) {
    signUpRequest.nickname = nickname;
  }
  final int? pid = jsonConvert.convert<int>(json['pid']);
  if (pid != null) {
    signUpRequest.pid = pid;
  }
  final int? sex = jsonConvert.convert<int>(json['sex']);
  if (sex != null) {
    signUpRequest.sex = sex;
  }
  final int? siteId = jsonConvert.convert<int>(json['siteId']);
  if (siteId != null) {
    signUpRequest.siteId = siteId;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    signUpRequest.type = type;
  }
  return signUpRequest;
}

Map<String, dynamic> $SignUpRequestToJson(SignUpRequest entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['mobile'] = entity.mobile;
  data['password'] = entity.password;
  data['inviteCode'] = entity.inviteCode;
  data['smsCode'] = entity.smsCode;
  data['avatar'] = entity.avatar;
  data['countryCode'] = entity.countryCode;
  data['email'] = entity.email;
  data['nickname'] = entity.nickname;
  data['pid'] = entity.pid;
  data['sex'] = entity.sex;
  data['siteId'] = entity.siteId;
  data['type'] = entity.type;
  return data;
}

extension SignUpRequestExtension on SignUpRequest {
  SignUpRequest copyWith({
    String? mobile,
    String? password,
    String? inviteCode,
    String? smsCode,
    String? avatar,
    String? countryCode,
    String? email,
    String? nickname,
    int? pid,
    int? sex,
    int? siteId,
    int? type,
  }) {
    return SignUpRequest()
      ..mobile = mobile ?? this.mobile
      ..password = password ?? this.password
      ..inviteCode = inviteCode ?? this.inviteCode
      ..smsCode = smsCode ?? this.smsCode
      ..avatar = avatar ?? this.avatar
      ..countryCode = countryCode ?? this.countryCode
      ..email = email ?? this.email
      ..nickname = nickname ?? this.nickname
      ..pid = pid ?? this.pid
      ..sex = sex ?? this.sex
      ..siteId = siteId ?? this.siteId
      ..type = type ?? this.type;
  }
}