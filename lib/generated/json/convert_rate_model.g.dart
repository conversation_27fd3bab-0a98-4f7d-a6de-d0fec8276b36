import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/features/convert_rate/domain/models/convert_rate_model.dart';

ConvertRate $ConvertRateFromJson(Map<String, dynamic> json) {
  final ConvertRate convertRate = ConvertRate();
  final String? currencyBase = jsonConvert.convert<String>(json['currencyBase']);
  if (currencyBase != null) {
    convertRate.currencyBase = currencyBase;
  }
  final String? currencyTarget = jsonConvert.convert<String>(json['currencyTarget']);
  if (currencyTarget != null) {
    convertRate.currencyTarget = currencyTarget;
  }
  final double? rate = jsonConvert.convert<double>(json['rate']);
  if (rate != null) {
    convertRate.rate = rate;
  }
  return convertRate;
}

Map<String, dynamic> $ConvertRateToJson(ConvertRate entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['currencyBase'] = entity.currencyBase;
  data['currencyTarget'] = entity.currencyTarget;
  data['rate'] = entity.rate;
  return data;
}

extension ConvertRateExtension on ConvertRate {
  ConvertRate copyWith({
    String? currencyBase,
    String? currencyTarget,
    double? rate,
  }) {
    return ConvertRate()
      ..currencyBase = currencyBase ?? this.currencyBase
      ..currencyTarget = currencyTarget ?? this.currencyTarget
      ..rate = rate ?? this.rate;
  }
}
