import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_info_model.dart';


FTradeListModel $FTradeListModelFromJson(Map<String, dynamic> json) {
  final FTradeListModel fTradeListModel = FTradeListModel();
  final int? totalNum = jsonConvert.convert<int>(json['totalNum']);
  if (totalNum != null) {
    fTradeListModel.totalNum = totalNum;
  }
  final List<FTradeListItemModel>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<FTradeListItemModel>(e) as FTradeListItemModel)
      .toList();
  if (list != null) {
    fTradeListModel.list = list;
  }
  return fTradeListModel;
}

Map<String, dynamic> $FTradeListModelToJson(FTradeListModel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['totalNum'] = entity.totalNum;
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension FTradeListModelExtension on FTradeListModel {
  FTradeListModel copyWith({
    int? totalNum,
    List<FTradeListItemModel>? list,
  }) {
    return FTradeListModel()
      ..totalNum = totalNum ?? this.totalNum
      ..list = list ?? this.list;
  }
}

FTradeListItemModel $FTradeListItemModelFromJson(Map<String, dynamic> json) {
  final FTradeListItemModel fTradeListItemModel = FTradeListItemModel();
  final double? latestPrice = jsonConvert.convert<double>(json['latestPrice']);
  if (latestPrice != null) {
    fTradeListItemModel.latestPrice = latestPrice;
  }
  final String? symbol = jsonConvert.convert<String>(json['symbol']);
  if (symbol != null) {
    fTradeListItemModel.symbol = symbol;
  }
  final String? productCode = jsonConvert.convert<String>(json['productCode']);
  if (productCode != null) {
    fTradeListItemModel.productCode = productCode;
  }
  final double? chg = jsonConvert.convert<double>(json['chg']);
  if (chg != null) {
    fTradeListItemModel.chg = chg;
  }
  final bool? isMain = jsonConvert.convert<bool>(json['isMain']);
  if (isMain != null) {
    fTradeListItemModel.isMain = isMain;
  }
  final int? precision = jsonConvert.convert<int>(json['precision']);
  if (precision != null) {
    fTradeListItemModel.precision = precision;
  }
  final double? gain = jsonConvert.convert<double>(json['gain']);
  if (gain != null) {
    fTradeListItemModel.gain = gain;
  }
  final String? market = jsonConvert.convert<String>(json['market']);
  if (market != null) {
    fTradeListItemModel.market = market;
  }
  final int? volume = jsonConvert.convert<int>(json['volume']);
  if (volume != null) {
    fTradeListItemModel.volume = volume;
  }
  final String? securityType = jsonConvert.convert<String>(
      json['securityType']);
  if (securityType != null) {
    fTradeListItemModel.securityType = securityType;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    fTradeListItemModel.name = name;
  }
  final bool? isContinuous = jsonConvert.convert<bool>(json['isContinuous']);
  if (isContinuous != null) {
    fTradeListItemModel.isContinuous = isContinuous;
  }
  final int? position = jsonConvert.convert<int>(json['position']);
  if (position != null) {
    fTradeListItemModel.position = position;
  }
  final int? prevPosition = jsonConvert.convert<int>(json['prevPosition']);
  if (prevPosition != null) {
    fTradeListItemModel.prevPosition = prevPosition;
  }
  return fTradeListItemModel;
}

Map<String, dynamic> $FTradeListItemModelToJson(FTradeListItemModel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['latestPrice'] = entity.latestPrice;
  data['symbol'] = entity.symbol;
  data['productCode'] = entity.productCode;
  data['chg'] = entity.chg;
  data['isMain'] = entity.isMain;
  data['precision'] = entity.precision;
  data['gain'] = entity.gain;
  data['market'] = entity.market;
  data['volume'] = entity.volume;
  data['securityType'] = entity.securityType;
  data['name'] = entity.name;
  data['isContinuous'] = entity.isContinuous;
  data['position'] = entity.position;
  data['prevPosition'] = entity.prevPosition;
  return data;
}

extension FTradeListItemModelExtension on FTradeListItemModel {
  FTradeListItemModel copyWith({
    double? latestPrice,
    String? symbol,
    String? productCode,
    double? chg,
    bool? isMain,
    int? precision,
    double? gain,
    String? market,
    int? volume,
    String? securityType,
    String? name,
    bool? isContinuous,
    int? position,
    int? prevPosition,
  }) {
    return FTradeListItemModel()
      ..latestPrice = latestPrice ?? this.latestPrice
      ..symbol = symbol ?? this.symbol
      ..productCode = productCode ?? this.productCode
      ..chg = chg ?? this.chg
      ..isMain = isMain ?? this.isMain
      ..precision = precision ?? this.precision
      ..gain = gain ?? this.gain
      ..market = market ?? this.market
      ..volume = volume ?? this.volume
      ..securityType = securityType ?? this.securityType
      ..name = name ?? this.name
      ..isContinuous = isContinuous ?? this.isContinuous
      ..position = position ?? this.position
      ..prevPosition = prevPosition ?? this.prevPosition;
  }
}