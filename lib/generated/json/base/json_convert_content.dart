// ignore_for_file: non_constant_identifier_names
// ignore_for_file: camel_case_types
// ignore_for_file: prefer_single_quotes

// This file is automatically generated. DO NOT EDIT, all your changes would be lost.
import 'package:flutter/material.dart' show debugPrint;
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_config_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_state_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_depth_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_info_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_k_line_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_tick_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';
import 'package:gp_stock_app/features/sign_in/domain/models/login/login_response.dart';

JsonConvert jsonConvert = JsonConvert();

typedef JsonConvertFunction<T> = T Function(Map<String, dynamic> json);
typedef EnumConvertFunction<T> = T Function(String value);
typedef ConvertExceptionHandler = void Function(Object error, StackTrace stackTrace);

extension MapSafeExt<K, V> on Map<K, V> {
  T? getOrNull<T>(K? key) {
    if (!containsKey(key) || key == null) {
      return null;
    } else {
      return this[key] as T?;
    }
  }
}

class JsonConvert {
  static ConvertExceptionHandler? onError;
  JsonConvertClassCollection convertFuncMap = JsonConvertClassCollection();

  /// When you are in the development, to generate a new model class, hot-reload doesn't find new generation model class, you can build on MaterialApp method called jsonConvert. ReassembleConvertFuncMap (); This method only works in a development environment
  /// https://flutter.cn/docs/development/tools/hot-reload
  /// class MyApp extends StatelessWidget {
  ///    const MyApp({Key? key})
  ///        : super(key: key);
  ///
  ///    @override
  ///    Widget build(BuildContext context) {
  ///      jsonConvert.reassembleConvertFuncMap();
  ///      return MaterialApp();
  ///    }
  /// }
  void reassembleConvertFuncMap() {
    bool isReleaseMode = const bool.fromEnvironment('dart.vm.product');
    if (!isReleaseMode) {
      convertFuncMap = JsonConvertClassCollection();
    }
  }

  T? convert<T>(dynamic value, {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    if (value is T) {
      return value;
    }
    try {
      return _asT<T>(value, enumConvert: enumConvert);
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return null;
    }
  }

  List<T?>? convertList<T>(List<dynamic>? value, {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return value.map((dynamic e) => _asT<T>(e, enumConvert: enumConvert)).toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  List<T>? convertListNotNull<T>(dynamic value, {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return (value as List<dynamic>).map((dynamic e) => _asT<T>(e, enumConvert: enumConvert)!).toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  T? _asT<T extends Object?>(dynamic value, {EnumConvertFunction? enumConvert}) {
    final String type = T.toString();
    final String valueS = value.toString();
    if (enumConvert != null) {
      return enumConvert(valueS) as T;
    } else if (type == "String") {
      return valueS as T;
    } else if (type == "int") {
      final int? intValue = int.tryParse(valueS);
      if (intValue == null) {
        return double.tryParse(valueS)?.toInt() as T?;
      } else {
        return intValue as T;
      }
    } else if (type == "double") {
      return double.parse(valueS) as T;
    } else if (type == "DateTime") {
      return DateTime.parse(valueS) as T;
    } else if (type == "bool") {
      if (valueS == '0' || valueS == '1') {
        return (valueS == '1') as T;
      }
      return (valueS == 'true') as T;
    } else if (type == "Map" || type.startsWith("Map<")) {
      return value as T;
    } else {
      if (convertFuncMap.containsKey(type)) {
        if (value == null) {
          return null;
        }
        var covertFunc = convertFuncMap[type]!;
        if (covertFunc is Map<String, dynamic>) {
          return covertFunc(value as Map<String, dynamic>) as T;
        } else {
          return covertFunc(Map<String, dynamic>.from(value)) as T;
        }
      } else {
        throw UnimplementedError('$type unimplemented,you can try running the app again');
      }
    }
  }

  //list is returned by type
  static M? _getListChildType<M>(List<Map<String, dynamic>> data) {
    if (<FTradeConfigModel>[] is M) {
      return data.map<FTradeConfigModel>((Map<String, dynamic> e) => FTradeConfigModel.fromJson(e)).toList() as M;
    }
    if (<FTradeStateModel>[] is M) {
      return data.map<FTradeStateModel>((Map<String, dynamic> e) => FTradeStateModel.fromJson(e)).toList() as M;
    }
    if (<FTradeDepthModel>[] is M) {
      return data.map<FTradeDepthModel>((Map<String, dynamic> e) => FTradeDepthModel.fromJson(e)).toList() as M;
    }
    if (<FTradeDepthAsk>[] is M) {
      return data.map<FTradeDepthAsk>((Map<String, dynamic> e) => FTradeDepthAsk.fromJson(e)).toList() as M;
    }
    if (<FTradeDepthBid>[] is M) {
      return data.map<FTradeDepthBid>((Map<String, dynamic> e) => FTradeDepthBid.fromJson(e)).toList() as M;
    }
    if (<FTradeInfoModel>[] is M) {
      return data.map<FTradeInfoModel>((Map<String, dynamic> e) => FTradeInfoModel.fromJson(e)).toList() as M;
    }
    if (<FTradeKLineModel>[] is M) {
      return data.map<FTradeKLineModel>((Map<String, dynamic> e) => FTradeKLineModel.fromJson(e)).toList() as M;
    }
    if (<FTradeInfoKLineModel>[] is M) {
      return data.map<FTradeInfoKLineModel>((Map<String, dynamic> e) => FTradeInfoKLineModel.fromJson(e)).toList() as M;
    }
    if (<FTradeKLineItem>[] is M) {
      return data.map<FTradeKLineItem>((Map<String, dynamic> e) => FTradeKLineItem.fromJson(e)).toList() as M;
    }
    if (<FTradeTickModel>[] is M) {
      return data.map<FTradeTickModel>((Map<String, dynamic> e) => FTradeTickModel.fromJson(e)).toList() as M;
    }
    if (<FTradeTickRecords>[] is M) {
      return data.map<FTradeTickRecords>((Map<String, dynamic> e) => FTradeTickRecords.fromJson(e)).toList() as M;
    }
    if (<FTradeListModel>[] is M) {
      return data.map<FTradeListModel>((Map<String, dynamic> e) => FTradeListModel.fromJson(e)).toList() as M;
    }
    if (<FTradeListItemModel>[] is M) {
      return data.map<FTradeListItemModel>((Map<String, dynamic> e) => FTradeListItemModel.fromJson(e)).toList() as M;
    }
    if (<LoginResponse>[] is M) {
      return data.map<LoginResponse>((Map<String, dynamic> e) => LoginResponse.fromJson(e)).toList() as M;
    }
    if (<UserData>[] is M) {
      return data.map<UserData>((Map<String, dynamic> e) => UserData.fromJson(e)).toList() as M;
    }

    debugPrint("$M not found");

    return null;
  }

  static M? fromJsonAsT<M>(dynamic json) {
    if (json is M) {
      return json;
    }
    if (json is List) {
      return _getListChildType<M>(json.map((dynamic e) => e as Map<String, dynamic>).toList());
    } else {
      return jsonConvert.convert<M>(json);
    }
  }
}

class JsonConvertClassCollection {
  Map<String, JsonConvertFunction> convertFuncMap = {
    (FTradeConfigModel).toString(): FTradeConfigModel.fromJson,
    (FTradeStateModel).toString(): FTradeStateModel.fromJson,
    (FTradeDepthModel).toString(): FTradeDepthModel.fromJson,
    (FTradeDepthAsk).toString(): FTradeDepthAsk.fromJson,
    (FTradeDepthBid).toString(): FTradeDepthBid.fromJson,
    (FTradeInfoModel).toString(): FTradeInfoModel.fromJson,
    (FTradeKLineModel).toString(): FTradeKLineModel.fromJson,
    (FTradeInfoKLineModel).toString(): FTradeInfoKLineModel.fromJson,
    (FTradeKLineItem).toString(): FTradeKLineItem.fromJson,
    (FTradeTickModel).toString(): FTradeTickModel.fromJson,
    (FTradeTickRecords).toString(): FTradeTickRecords.fromJson,
    (FTradeListModel).toString(): FTradeListModel.fromJson,
    (FTradeListItemModel).toString(): FTradeListItemModel.fromJson,
    (LoginResponse).toString(): LoginResponse.fromJson,
    (UserData).toString(): UserData.fromJson,
  };

  bool containsKey(String type) {
    return convertFuncMap.containsKey(type);
  }

  JsonConvertFunction? operator [](String key) {
    return convertFuncMap[key];
  }
}
