import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/features/sign_in/domain/models/app_info/app_info.dart';

AppInfo $AppInfoFromJson(Map<String, dynamic> json) {
  final AppInfo appInfo = AppInfo();
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    appInfo.content = content;
  }
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    appInfo.id = id;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    appInfo.title = title;
  }
  return appInfo;
}

Map<String, dynamic> $AppInfoToJson(AppInfo entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['content'] = entity.content;
  data['id'] = entity.id;
  data['title'] = entity.title;
  return data;
}

extension AppInfoExtension on AppInfo {
  AppInfo copyWith({
    String? content,
    int? id,
    String? title,
  }) {
    return AppInfo()
      ..content = content ?? this.content
      ..id = id ?? this.id
      ..title = title ?? this.title;
  }
}
