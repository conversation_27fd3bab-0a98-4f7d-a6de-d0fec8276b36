import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/features/notifications/domain/models/notification/notification_model.dart';

NotificationModel $NotificationModelFromJson(Map<String, dynamic> json) {
  final NotificationModel notificationModel = NotificationModel();
  final int? code = jsonConvert.convert<int>(json['code']);
  if (code != null) {
    notificationModel.code = code;
  }
  final String? msg = jsonConvert.convert<String>(json['msg']);
  if (msg != null) {
    notificationModel.msg = msg;
  }
  final NotificationData? data = jsonConvert.convert<NotificationData>(
      json['data']);
  if (data != null) {
    notificationModel.data = data;
  }
  return notificationModel;
}

Map<String, dynamic> $NotificationModelToJson(NotificationModel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  data['msg'] = entity.msg;
  data['data'] = entity.data?.toJson();
  return data;
}

extension NotificationModelExtension on NotificationModel {
  NotificationModel copyWith({
    int? code,
    String? msg,
    NotificationData? data,
  }) {
    return NotificationModel()
      ..code = code ?? this.code
      ..msg = msg ?? this.msg
      ..data = data ?? this.data;
  }
}

NotificationData $NotificationDataFromJson(Map<String, dynamic> json) {
  final NotificationData notificationData = NotificationData();
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    notificationData.current = current;
  }
  final bool? optimizeCountSql = jsonConvert.convert<bool>(
      json['optimizeCountSql']);
  if (optimizeCountSql != null) {
    notificationData.optimizeCountSql = optimizeCountSql;
  }
  final List<dynamic>? orders = (json['orders'] as List<dynamic>?)?.map(
          (e) => e).toList();
  if (orders != null) {
    notificationData.orders = orders;
  }
  final int? pages = jsonConvert.convert<int>(json['pages']);
  if (pages != null) {
    notificationData.pages = pages;
  }
  final List<NotificationRecord>? records = (json['records'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<NotificationRecord>(e) as NotificationRecord)
      .toList();
  if (records != null) {
    notificationData.records = records;
  }
  final bool? searchCount = jsonConvert.convert<bool>(json['searchCount']);
  if (searchCount != null) {
    notificationData.searchCount = searchCount;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    notificationData.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    notificationData.total = total;
  }
  return notificationData;
}

Map<String, dynamic> $NotificationDataToJson(NotificationData entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['current'] = entity.current;
  data['optimizeCountSql'] = entity.optimizeCountSql;
  data['orders'] = entity.orders;
  data['pages'] = entity.pages;
  data['records'] = entity.records?.map((v) => v.toJson()).toList();
  data['searchCount'] = entity.searchCount;
  data['size'] = entity.size;
  data['total'] = entity.total;
  return data;
}

extension NotificationDataExtension on NotificationData {
  NotificationData copyWith({
    int? current,
    bool? optimizeCountSql,
    List<dynamic>? orders,
    int? pages,
    List<NotificationRecord>? records,
    bool? searchCount,
    int? size,
    int? total,
  }) {
    return NotificationData()
      ..current = current ?? this.current
      ..optimizeCountSql = optimizeCountSql ?? this.optimizeCountSql
      ..orders = orders ?? this.orders
      ..pages = pages ?? this.pages
      ..records = records ?? this.records
      ..searchCount = searchCount ?? this.searchCount
      ..size = size ?? this.size
      ..total = total ?? this.total;
  }
}

NotificationRecord $NotificationRecordFromJson(Map<String, dynamic> json) {
  final NotificationRecord notificationRecord = NotificationRecord();
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    notificationRecord.content = content;
  }
  final int? contentType = jsonConvert.convert<int>(json['contentType']);
  if (contentType != null) {
    notificationRecord.contentType = contentType;
  }
  final String? extra = jsonConvert.convert<String>(json['extra']);
  if (extra != null) {
    notificationRecord.extra = extra;
  }
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    notificationRecord.id = id;
  }
  final int? isNeedPush = jsonConvert.convert<int>(json['isNeedPush']);
  if (isNeedPush != null) {
    notificationRecord.isNeedPush = isNeedPush;
  }
  final String? messageNo = jsonConvert.convert<String>(json['messageNo']);
  if (messageNo != null) {
    notificationRecord.messageNo = messageNo;
  }
  final int? messageType = jsonConvert.convert<int>(json['messageType']);
  if (messageType != null) {
    notificationRecord.messageType = messageType;
  }
  final int? pushStatus = jsonConvert.convert<int>(json['pushStatus']);
  if (pushStatus != null) {
    notificationRecord.pushStatus = pushStatus;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    notificationRecord.status = status;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    notificationRecord.title = title;
  }
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    notificationRecord.type = type;
  }
  final int? userId = jsonConvert.convert<int>(json['userId']);
  if (userId != null) {
    notificationRecord.userId = userId;
  }
  return notificationRecord;
}

Map<String, dynamic> $NotificationRecordToJson(NotificationRecord entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['content'] = entity.content;
  data['contentType'] = entity.contentType;
  data['extra'] = entity.extra;
  data['id'] = entity.id;
  data['isNeedPush'] = entity.isNeedPush;
  data['messageNo'] = entity.messageNo;
  data['messageType'] = entity.messageType;
  data['pushStatus'] = entity.pushStatus;
  data['status'] = entity.status;
  data['title'] = entity.title;
  data['type'] = entity.type;
  data['userId'] = entity.userId;
  return data;
}

extension NotificationRecordExtension on NotificationRecord {
  NotificationRecord copyWith({
    String? content,
    int? contentType,
    String? extra,
    int? id,
    int? isNeedPush,
    String? messageNo,
    int? messageType,
    int? pushStatus,
    int? status,
    String? title,
    String? type,
    int? userId,
  }) {
    return NotificationRecord()
      ..content = content ?? this.content
      ..contentType = contentType ?? this.contentType
      ..extra = extra ?? this.extra
      ..id = id ?? this.id
      ..isNeedPush = isNeedPush ?? this.isNeedPush
      ..messageNo = messageNo ?? this.messageNo
      ..messageType = messageType ?? this.messageType
      ..pushStatus = pushStatus ?? this.pushStatus
      ..status = status ?? this.status
      ..title = title ?? this.title
      ..type = type ?? this.type
      ..userId = userId ?? this.userId;
  }
}